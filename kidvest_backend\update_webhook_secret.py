import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def update_webhook_secret():
    """Update the webhook secret in the .env file"""
    print("=== Update Webhook Secret ===")
    
    # Get the webhook secret from user input
    webhook_secret = input("Enter the webhook secret from Stripe Dashboard: ")
    
    # Update the .env file
    update_env_file("STRIPE_WEBHOOK_SECRET", webhook_secret)
    
    print("\n=== Webhook Secret Updated ===")
    print("Make sure to restart your backend server to apply the changes.")

def update_env_file(key, value):
    """Update a key in the .env file"""
    try:
        # Read the current .env file
        with open(".env", "r") as f:
            lines = f.readlines()
        
        # Check if the key already exists
        key_exists = False
        for i, line in enumerate(lines):
            if line.startswith(f"{key}="):
                lines[i] = f"{key}={value}\n"
                key_exists = True
                break
        
        # Add the key if it doesn't exist
        if not key_exists:
            lines.append(f"{key}={value}\n")
        
        # Write the updated .env file
        with open(".env", "w") as f:
            f.writelines(lines)
        
        print(f"Updated {key} in .env file")
    except Exception as e:
        print(f"Error updating .env file: {e}")

if __name__ == "__main__":
    update_webhook_secret()
