import requests

def test_token_endpoint():
    """Test the token endpoint directly"""
    url = "http://localhost:8000/api/token"
    
    # The API expects form data, not JSON
    data = {
        "username": "<EMAIL>",  # Replace with your email
        "password": "gymnast1234"  # Replace with your password
    }
    
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    try:
        print(f"Sending request to {url} with data: {data}")
        response = requests.post(url, data=data, headers=headers)
        
        print(f"Response status code: {response.status_code}")
        print(f"Response headers: {response.headers}")
        
        try:
            print(f"Response body: {response.json()}")
        except:
            print(f"Response text: {response.text}")
    except Exception as e:
        print(f"Exception: {str(e)}")

if __name__ == "__main__":
    test_token_endpoint()
