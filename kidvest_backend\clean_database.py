import os
import psycopg2
from dotenv import load_dotenv
from sqlalchemy import create_engine
from app.database import Base
from app import models

# Load environment variables
load_dotenv()

# Get database connection details from DATABASE_URL
DATABASE_URL = os.getenv("DATABASE_URL")

print("=== Database Cleanup Script ===")

# Parse the DATABASE_URL
if DATABASE_URL.startswith("postgresql://"):
    # Format: postgresql://username:password@host:port/database
    url_without_protocol = DATABASE_URL.replace("postgresql://", "")
    user_pass, host_port_db = url_without_protocol.split("@")
    
    if ":" in user_pass:
        username, password = user_pass.split(":")
    else:
        username = user_pass
        password = ""
    
    host_port, database = host_port_db.split("/")
    
    if ":" in host_port:
        host, port = host_port.split(":")
    else:
        host = host_port
        port = "5432"
else:
    print("Invalid DATABASE_URL format")
    exit(1)

def clean_database():
    """Clean the database by dropping all tables and recreating them"""
    try:
        # Connect to the database
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=password
        )
        
        # Create a cursor
        cur = conn.cursor()
        
        # Set to autocommit mode to avoid transaction issues with DROP statements
        conn.autocommit = True
        
        print("\n=== Step 1: Dropping all tables ===")
        
        # Get all tables
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        
        tables = [table[0] for table in cur.fetchall()]
        print(f"Found {len(tables)} tables: {', '.join(tables)}")
        
        # Drop all tables
        if tables:
            # Disable foreign key checks
            cur.execute("SET session_replication_role = 'replica';")
            
            for table in tables:
                print(f"Dropping table: {table}")
                cur.execute(f"DROP TABLE IF EXISTS {table} CASCADE")
            
            # Re-enable foreign key checks
            cur.execute("SET session_replication_role = 'origin';")
            
            print("All tables dropped successfully")
        else:
            print("No tables to drop")
        
        # Close the connection
        cur.close()
        conn.close()
        
        print("\n=== Step 2: Recreating tables with SQLAlchemy ===")
        
        # Create a SQLAlchemy engine
        engine = create_engine(DATABASE_URL)
        
        # Create all tables
        Base.metadata.create_all(engine)
        
        print("All tables created successfully")
        
        print("\n=== Database cleanup completed successfully! ===")
        
    except Exception as e:
        print(f"Error during database cleanup: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Ask for confirmation
    confirm = input("This will DELETE ALL DATA in the database. Are you sure? (y/n): ")
    if confirm.lower() == 'y':
        clean_database()
        print("\nDone.")
    else:
        print("Operation cancelled.")
