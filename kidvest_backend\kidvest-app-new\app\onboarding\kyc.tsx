import React, { useState } from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert,
  Modal,
  FlatList,
  Pressable
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Text, View } from '@/components/Themed';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';

// Helper function to format date from MM/DD/YYYY to YYYY-MM-DD
const formatDateForBackend = (dateString: string) => {
  if (!dateString) return '';

  // Check if the date is already in the correct format
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
    return dateString;
  }

  // Parse MM/DD/YYYY format
  const parts = dateString.split('/');
  if (parts.length !== 3) {
    console.error('Invalid date format. Expected MM/DD/YYYY');
    return dateString; // Return as is if format is unexpected
  }

  const month = parts[0].padStart(2, '0');
  const day = parts[1].padStart(2, '0');
  const year = parts[2].length === 2 ? `20${parts[2]}` : parts[2];

  return `${year}-${month}-${day}`;
};
import { useAuth } from '../../context/AuthContext';
import { onboardingAPI } from '../../services/api';

export default function KYCScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { user } = useAuth();

  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Step 1: Personal Information
  const [firstName, setFirstName] = useState(user?.name?.split(' ')[0] || '');
  const [lastName, setLastName] = useState(user?.name?.split(' ')[1] || '');
  const [dob, setDob] = useState('');
  const [ssn, setSsn] = useState('');
  const [phone, setPhone] = useState(user?.phone_number || '');

  // Step 2: Address Information
  const [address, setAddress] = useState(user?.address || '');
  const [city, setCity] = useState(user?.city || '');
  const [state, setState] = useState(user?.state || '');
  const [zipCode, setZipCode] = useState(user?.postal_code || '');
  const [country, setCountry] = useState(user?.country || 'USA');

  // Step 3: Employment Information
  const [employmentStatus, setEmploymentStatus] = useState('');
  const [incomeRange, setIncomeRange] = useState('');
  const [netWorthRange, setNetWorthRange] = useState('');
  const [fundingSource, setFundingSource] = useState('');
  const [investmentExperienceLevel, setInvestmentExperienceLevel] = useState('');
  const [riskToleranceLevel, setRiskToleranceLevel] = useState('');

  // Modal visibility states for dropdowns
  const [employmentModalVisible, setEmploymentModalVisible] = useState(false);
  const [incomeModalVisible, setIncomeModalVisible] = useState(false);
  const [netWorthModalVisible, setNetWorthModalVisible] = useState(false);
  const [fundingModalVisible, setFundingModalVisible] = useState(false);
  const [experienceModalVisible, setExperienceModalVisible] = useState(false);
  const [riskModalVisible, setRiskModalVisible] = useState(false);

  // Step 4: Disclosures and Agreements
  const [isControlPerson, setIsControlPerson] = useState(false);
  const [isAffiliatedExchangeOrFinra, setIsAffiliatedExchangeOrFinra] = useState(false);
  const [isPoliticallyExposed, setIsPoliticallyExposed] = useState(false);
  const [immediateFamilyExposed, setImmediateFamilyExposed] = useState(false);
  const [customerAgreementAccepted, setCustomerAgreementAccepted] = useState(false);
  const [marginAgreementAccepted, setMarginAgreementAccepted] = useState(false);
  const [accountAgreementAccepted, setAccountAgreementAccepted] = useState(false);

  // Session token for multi-step onboarding
  const [sessionToken, setSessionToken] = useState<string | null>(null);

  const validateStep1 = () => {
    if (!firstName.trim()) {
      setError('First name is required');
      return false;
    }

    if (!lastName.trim()) {
      setError('Last name is required');
      return false;
    }

    if (!dob.trim()) {
      setError('Date of birth is required');
      return false;
    }

    if (!ssn.trim()) {
      setError('SSN is required');
      return false;
    }

    if (!phone.trim()) {
      setError('Phone number is required');
      return false;
    }

    setError(null);
    return true;
  };

  const validateStep2 = () => {
    if (!address.trim()) {
      setError('Address is required');
      return false;
    }

    if (!city.trim()) {
      setError('City is required');
      return false;
    }

    if (!state.trim()) {
      setError('State is required');
      return false;
    }

    if (!zipCode.trim()) {
      setError('ZIP code is required');
      return false;
    }

    setError(null);
    return true;
  };

  const validateStep3 = () => {
    if (!employmentStatus.trim()) {
      setError('Employment status is required');
      return false;
    }

    if (!incomeRange.trim()) {
      setError('Annual income range is required');
      return false;
    }

    if (!netWorthRange.trim()) {
      setError('Net worth range is required');
      return false;
    }

    if (!fundingSource.trim()) {
      setError('Funding source is required');
      return false;
    }

    if (!investmentExperienceLevel.trim()) {
      setError('Investment experience is required');
      return false;
    }

    if (!riskToleranceLevel.trim()) {
      setError('Risk tolerance is required');
      return false;
    }

    setError(null);
    return true;
  };

  const validateStep4 = () => {
    // All agreements must be accepted
    if (!customerAgreementAccepted) {
      setError('You must accept the Customer Agreement');
      return false;
    }

    if (!marginAgreementAccepted) {
      setError('You must accept the Margin Agreement');
      return false;
    }

    if (!accountAgreementAccepted) {
      setError('You must accept the Account Agreement');
      return false;
    }

    setError(null);
    return true;
  };

  const handleNextStep = async () => {
    let isValid = false;

    switch (step) {
      case 1:
        isValid = validateStep1();
        if (isValid) {
          setIsLoading(true);
          try {
            // The backend expects a different format for step1
            const step1Data = {
              full_name: `${firstName} ${lastName}`,
              email: user?.email || '',
            };
            console.log('Submitting step 1 data:', step1Data);
            const response = await onboardingAPI.submitStep1(step1Data);
            console.log('Step 1 response:', response);

            if (response.session_token) {
              setSessionToken(response.session_token);
              setStep(2);
            } else {
              // If no session token is returned but the request was successful
              console.warn('No session token returned from backend, but request was successful');
              // For testing purposes, we can still proceed to step 2
              setStep(2);
            }
          } catch (error: any) {
            console.error('Error submitting step 1:', error);
            // Extract more specific error message if available
            const errorMessage = error.response?.data?.message ||
                               error.response?.data?.detail ||
                               'Failed to submit personal information. Please try again.';
            setError(errorMessage);
          } finally {
            setIsLoading(false);
          }
        }
        break;

      case 2:
        isValid = validateStep2();
        if (isValid) {
          // Check if we have a session token
          if (!sessionToken) {
            setError('No session token available. Please restart the onboarding process.');
            return;
          }

          setIsLoading(true);
          try {
            // Format data according to backend expectations
            const step2Data = {
              session_token: sessionToken,
              dob: formatDateForBackend(dob),
              phone_number: phone,
              street_address: address,
              city: city,
              state: state,
              postal_code: zipCode,
              country: country,
              ssn: ssn,
            };
            console.log('Submitting step 2 data:', step2Data);
            const response = await onboardingAPI.submitStep2(step2Data);
            console.log('Step 2 response:', response);

            setStep(3);
          } catch (error: any) {
            console.error('Error submitting step 2:', error);
            // Extract more specific error message if available
            const errorMessage = error.response?.data?.message ||
                               error.response?.data?.detail ||
                               'Failed to submit address information. Please try again.';
            setError(errorMessage);
          } finally {
            setIsLoading(false);
          }
        }
        break;

      case 3:
        isValid = validateStep3();
        if (isValid) {
          setIsLoading(true);
          try {
            // Format data according to backend expectations
            const step3Data = {
              session_token: sessionToken,
              employment_status: employmentStatus,
              income_range: incomeRange,
              net_worth_range: netWorthRange,
              funding_source: fundingSource,
              investment_experience: investmentExperienceLevel,
              risk_tolerance: riskToleranceLevel,
            };
            console.log('Submitting step 3 data:', step3Data);
            const response = await onboardingAPI.submitStep3(step3Data);
            console.log('Step 3 response:', response);

            setStep(4);
          } catch (error: any) {
            console.error('Error submitting step 3:', error);
            // Extract more specific error message if available
            const errorMessage = error.response?.data?.message ||
                               error.response?.data?.detail ||
                               'Failed to submit employment information. Please try again.';
            setError(errorMessage);
          } finally {
            setIsLoading(false);
          }
        }
        break;

      case 4:
        isValid = validateStep4();
        if (isValid) {
          setIsLoading(true);
          try {
            // Format data according to backend expectations
            const step4Data = {
              session_token: sessionToken,
              is_control_person: isControlPerson,
              is_affiliated_exchange_or_finra: isAffiliatedExchangeOrFinra,
              is_politically_exposed: isPoliticallyExposed,
              immediate_family_exposed: immediateFamilyExposed,
              customer_agreement_accepted: customerAgreementAccepted,
              margin_agreement_accepted: marginAgreementAccepted,
              account_agreement_accepted: accountAgreementAccepted,
            };
            console.log('Submitting step 4 data:', step4Data);
            const response = await onboardingAPI.submitStep4(step4Data);
            console.log('Step 4 response:', response);

            // Submit to Alpaca
            console.log('Submitting to Alpaca with session token:', sessionToken);
            try {
              const alpacaResponse = await onboardingAPI.submitToAlpaca(sessionToken!);
              console.log('Alpaca response:', alpacaResponse);

              if (alpacaResponse.success) {
                // Navigate to completion screen
                router.replace('/onboarding/complete');
              } else {
                // Handle unsuccessful response
                setError(alpacaResponse.message || 'Failed to create Alpaca account. Please try again.');
              }
            } catch (alpacaError: any) {
              console.error('Error submitting to Alpaca:', alpacaError);
              const alpacaErrorMessage = alpacaError.response?.data?.message ||
                                      alpacaError.response?.data?.detail ||
                                      'Failed to create Alpaca account. Please try again.';
              setError(alpacaErrorMessage);
            }
          } catch (error: any) {
            console.error('Error submitting step 4:', error);
            // Extract more specific error message if available
            const errorMessage = error.response?.data?.message ||
                               error.response?.data?.detail ||
                               'Failed to submit disclosures and agreements. Please try again.';
            setError(errorMessage);
          } finally {
            setIsLoading(false);
          }
        }
        break;
    }
  };

  const handlePreviousStep = () => {
    if (step > 1) {
      setStep(step - 1);
    } else {
      router.back();
    }
  };

  const renderStepIndicator = () => {
    return (
      <View style={styles.stepIndicator}>
        {[1, 2, 3, 4].map((i) => (
          <View
            key={i}
            style={[
              styles.stepDot,
              i <= step && { backgroundColor: Colors[colorScheme].primary },
            ]}
          />
        ))}
      </View>
    );
  };

  // Dropdown component
  const Dropdown = ({
    label,
    value,
    placeholder,
    options,
    displayOptions,
    isVisible,
    setIsVisible,
    onSelect
  }) => {
    return (
      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>{label}</Text>
        <TouchableOpacity
          style={styles.pickerButton}
          onPress={() => setIsVisible(true)}
        >
          <Text style={[styles.pickerButtonText, !value && styles.placeholderText]}>
            {value ? displayOptions[options.indexOf(value)] : placeholder}
          </Text>
          <FontAwesome5 name="chevron-down" size={16} color="#666" />
        </TouchableOpacity>

        <Modal
          animationType="slide"
          transparent={true}
          visible={isVisible}
          onRequestClose={() => setIsVisible(false)}
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setIsVisible(false)}
          >
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{`Select ${label}`}</Text>
                <TouchableOpacity onPress={() => setIsVisible(false)}>
                  <FontAwesome5 name="times" size={20} color="#333" />
                </TouchableOpacity>
              </View>

              <FlatList
                data={options}
                keyExtractor={(item) => item}
                renderItem={({ item, index }) => (
                  <TouchableOpacity
                    style={[
                      styles.optionItem,
                      value === item && styles.selectedOption
                    ]}
                    onPress={() => {
                      onSelect(item);
                      setIsVisible(false);
                    }}
                  >
                    <Text style={[
                      styles.optionText,
                      value === item && styles.selectedOptionText
                    ]}>
                      {displayOptions[index]}
                    </Text>
                    {value === item && (
                      <FontAwesome5 name="check" size={16} color={Colors[colorScheme].primary} />
                    )}
                  </TouchableOpacity>
                )}
              />
            </View>
          </TouchableOpacity>
        </Modal>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handlePreviousStep}
          >
            <FontAwesome5 name="arrow-left" size={16} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>KYC Verification</Text>
          {renderStepIndicator()}
        </View>

        {error && (
          <View style={styles.errorContainer}>
            <FontAwesome5 name="exclamation-circle" size={16} color="#F44336" />
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        {step === 1 && (
          <View style={styles.formContainer}>
            <Text style={styles.stepTitle}>Personal Information</Text>
            <Text style={styles.stepDescription}>
              Please provide your personal information to verify your identity.
            </Text>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>First Name</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your first name"
                value={firstName}
                onChangeText={setFirstName}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Last Name</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your last name"
                value={lastName}
                onChangeText={setLastName}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Date of Birth</Text>
              <TextInput
                style={styles.input}
                placeholder="MM/DD/YYYY"
                value={dob}
                onChangeText={setDob}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Social Security Number</Text>
              <TextInput
                style={styles.input}
                placeholder="XXX-XX-XXXX"
                value={ssn}
                onChangeText={setSsn}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Phone Number</Text>
              <TextInput
                style={styles.input}
                placeholder="(XXX) XXX-XXXX"
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
              />
            </View>
          </View>
        )}

        {step === 2 && (
          <View style={styles.formContainer}>
            <Text style={styles.stepTitle}>Address Information</Text>
            <Text style={styles.stepDescription}>
              Please provide your current residential address.
            </Text>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Street Address</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your street address"
                value={address}
                onChangeText={setAddress}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>City</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your city"
                value={city}
                onChangeText={setCity}
              />
            </View>

            <View style={styles.rowContainer}>
              <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
                <Text style={styles.inputLabel}>State</Text>
                <TextInput
                  style={styles.input}
                  placeholder="State"
                  value={state}
                  onChangeText={setState}
                />
              </View>

              <View style={[styles.inputContainer, { flex: 1, marginLeft: 8 }]}>
                <Text style={styles.inputLabel}>ZIP Code</Text>
                <TextInput
                  style={styles.input}
                  placeholder="ZIP Code"
                  value={zipCode}
                  onChangeText={setZipCode}
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Country</Text>
              <TextInput
                style={styles.input}
                placeholder="Country"
                value={country}
                onChangeText={setCountry}
                editable={false}
              />
            </View>
          </View>
        )}

        {step === 3 && (
          <View style={styles.formContainer}>
            <Text style={styles.stepTitle}>Financial Profile</Text>
            <Text style={styles.stepDescription}>
              Please provide information about your financial situation.
            </Text>

            <Dropdown
              label="Employment Status"
              value={employmentStatus}
              placeholder="Select Employment Status"
              options={['EMPLOYED', 'UNEMPLOYED', 'STUDENT', 'RETIRED']}
              displayOptions={['Employed', 'Unemployed', 'Student', 'Retired']}
              isVisible={employmentModalVisible}
              setIsVisible={setEmploymentModalVisible}
              onSelect={setEmploymentStatus}
            />

            <Dropdown
              label="Annual Income Range"
              value={incomeRange}
              placeholder="Select Annual Income Range"
              options={['under_50k', '50k_100k', '100k_250k', 'over_250k']}
              displayOptions={['Under $50,000', '$50,000 - $100,000', '$100,000 - $250,000', 'Over $250,000']}
              isVisible={incomeModalVisible}
              setIsVisible={setIncomeModalVisible}
              onSelect={setIncomeRange}
            />

            <Dropdown
              label="Net Worth Range"
              value={netWorthRange}
              placeholder="Select Net Worth Range"
              options={['under_50k', '50k_100k', '100k_250k', 'over_250k']}
              displayOptions={['Under $50,000', '$50,000 - $100,000', '$100,000 - $250,000', 'Over $250,000']}
              isVisible={netWorthModalVisible}
              setIsVisible={setNetWorthModalVisible}
              onSelect={setNetWorthRange}
            />

            <Dropdown
              label="Funding Source"
              value={fundingSource}
              placeholder="Select Funding Source"
              options={['employment_income', 'investments', 'inheritance', 'savings', 'other']}
              displayOptions={['Employment Income', 'Investments', 'Inheritance', 'Savings', 'Other']}
              isVisible={fundingModalVisible}
              setIsVisible={setFundingModalVisible}
              onSelect={setFundingSource}
            />

            <Dropdown
              label="Investment Experience"
              value={investmentExperienceLevel}
              placeholder="Select Investment Experience"
              options={['none', 'some', 'experienced']}
              displayOptions={['None', 'Some', 'Experienced']}
              isVisible={experienceModalVisible}
              setIsVisible={setExperienceModalVisible}
              onSelect={setInvestmentExperienceLevel}
            />

            <Dropdown
              label="Risk Tolerance"
              value={riskToleranceLevel}
              placeholder="Select Risk Tolerance"
              options={['conservative', 'moderate', 'aggressive']}
              displayOptions={['Conservative', 'Moderate', 'Aggressive']}
              isVisible={riskModalVisible}
              setIsVisible={setRiskModalVisible}
              onSelect={setRiskToleranceLevel}
            />
          </View>
        )}

        {step === 4 && (
          <View style={styles.formContainer}>
            <Text style={styles.stepTitle}>Disclosures and Agreements</Text>
            <Text style={styles.stepDescription}>
              Please answer the following questions and accept the agreements to complete your application.
            </Text>

            <View style={styles.disclosureContainer}>
              <Text style={styles.disclosureQuestion}>
                Are you a control person of a publicly traded company? (Director, Officer, or 10% Stockholder)
              </Text>
              <View style={styles.radioGroup}>
                <TouchableOpacity
                  style={styles.radioOption}
                  onPress={() => setIsControlPerson(true)}
                >
                  <View style={[styles.radioButton, isControlPerson && styles.radioButtonSelected]}>
                    {isControlPerson && <View style={styles.radioButtonInner} />}
                  </View>
                  <Text style={styles.radioLabel}>Yes</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.radioOption}
                  onPress={() => setIsControlPerson(false)}
                >
                  <View style={[styles.radioButton, !isControlPerson && styles.radioButtonSelected]}>
                    {!isControlPerson && <View style={styles.radioButtonInner} />}
                  </View>
                  <Text style={styles.radioLabel}>No</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.disclosureContainer}>
              <Text style={styles.disclosureQuestion}>
                Are you affiliated with a securities exchange or FINRA?
              </Text>
              <View style={styles.radioGroup}>
                <TouchableOpacity
                  style={styles.radioOption}
                  onPress={() => setIsAffiliatedExchangeOrFinra(true)}
                >
                  <View style={[styles.radioButton, isAffiliatedExchangeOrFinra && styles.radioButtonSelected]}>
                    {isAffiliatedExchangeOrFinra && <View style={styles.radioButtonInner} />}
                  </View>
                  <Text style={styles.radioLabel}>Yes</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.radioOption}
                  onPress={() => setIsAffiliatedExchangeOrFinra(false)}
                >
                  <View style={[styles.radioButton, !isAffiliatedExchangeOrFinra && styles.radioButtonSelected]}>
                    {!isAffiliatedExchangeOrFinra && <View style={styles.radioButtonInner} />}
                  </View>
                  <Text style={styles.radioLabel}>No</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.disclosureContainer}>
              <Text style={styles.disclosureQuestion}>
                Are you a politically exposed person?
              </Text>
              <View style={styles.radioGroup}>
                <TouchableOpacity
                  style={styles.radioOption}
                  onPress={() => setIsPoliticallyExposed(true)}
                >
                  <View style={[styles.radioButton, isPoliticallyExposed && styles.radioButtonSelected]}>
                    {isPoliticallyExposed && <View style={styles.radioButtonInner} />}
                  </View>
                  <Text style={styles.radioLabel}>Yes</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.radioOption}
                  onPress={() => setIsPoliticallyExposed(false)}
                >
                  <View style={[styles.radioButton, !isPoliticallyExposed && styles.radioButtonSelected]}>
                    {!isPoliticallyExposed && <View style={styles.radioButtonInner} />}
                  </View>
                  <Text style={styles.radioLabel}>No</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.disclosureContainer}>
              <Text style={styles.disclosureQuestion}>
                Is any member of your immediate family a politically exposed person?
              </Text>
              <View style={styles.radioGroup}>
                <TouchableOpacity
                  style={styles.radioOption}
                  onPress={() => setImmediateFamilyExposed(true)}
                >
                  <View style={[styles.radioButton, immediateFamilyExposed && styles.radioButtonSelected]}>
                    {immediateFamilyExposed && <View style={styles.radioButtonInner} />}
                  </View>
                  <Text style={styles.radioLabel}>Yes</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.radioOption}
                  onPress={() => setImmediateFamilyExposed(false)}
                >
                  <View style={[styles.radioButton, !immediateFamilyExposed && styles.radioButtonSelected]}>
                    {!immediateFamilyExposed && <View style={styles.radioButtonInner} />}
                  </View>
                  <Text style={styles.radioLabel}>No</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.agreementContainer}>
              <TouchableOpacity
                style={styles.checkboxContainer}
                onPress={() => setCustomerAgreementAccepted(!customerAgreementAccepted)}
              >
                <View style={[styles.checkbox, customerAgreementAccepted && styles.checkboxSelected]}>
                  {customerAgreementAccepted && <FontAwesome5 name="check" size={12} color="#FFFFFF" />}
                </View>
                <Text style={styles.agreementText}>
                  I accept the Customer Agreement
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.agreementContainer}>
              <TouchableOpacity
                style={styles.checkboxContainer}
                onPress={() => setMarginAgreementAccepted(!marginAgreementAccepted)}
              >
                <View style={[styles.checkbox, marginAgreementAccepted && styles.checkboxSelected]}>
                  {marginAgreementAccepted && <FontAwesome5 name="check" size={12} color="#FFFFFF" />}
                </View>
                <Text style={styles.agreementText}>
                  I accept the Margin Agreement
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.agreementContainer}>
              <TouchableOpacity
                style={styles.checkboxContainer}
                onPress={() => setAccountAgreementAccepted(!accountAgreementAccepted)}
              >
                <View style={[styles.checkbox, accountAgreementAccepted && styles.checkboxSelected]}>
                  {accountAgreementAccepted && <FontAwesome5 name="check" size={12} color="#FFFFFF" />}
                </View>
                <Text style={styles.agreementText}>
                  I accept the Account Agreement
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.nextButton, { backgroundColor: Colors[colorScheme].primary }]}
            onPress={handleNextStep}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="white" />
            ) : (
              <>
                <Text style={styles.nextButtonText}>
                  {step === 4 ? 'Submit' : 'Next'}
                </Text>
                {step !== 4 && (
                  <FontAwesome5 name="arrow-right" size={16} color="white" />
                )}
              </>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  stepIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#E0E0E0',
    marginHorizontal: 4,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFEBEE',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: '#F44336',
    marginLeft: 8,
    flex: 1,
  },
  formContainer: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 24,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: '#666666',
  },
  input: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 48,
    fontSize: 16,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  buttonContainer: {
    marginTop: 24,
  },
  nextButton: {
    height: 48,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
    marginRight: 8,
  },
  // Dropdown styles
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    overflow: 'hidden',
  },
  pickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 12,
    height: 48,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
  },
  pickerButtonText: {
    fontSize: 16,
    color: '#333',
  },
  placeholderText: {
    color: '#999',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  selectedOption: {
    backgroundColor: '#F5F5F5',
  },
  optionText: {
    fontSize: 16,
    color: '#333',
  },
  selectedOptionText: {
    fontWeight: 'bold',
    color: '#000',
  },
  // Disclosure and agreement styles
  disclosureContainer: {
    marginBottom: 20,
  },
  disclosureQuestion: {
    fontSize: 16,
    color: '#333',
    marginBottom: 12,
  },
  radioGroup: {
    flexDirection: 'row',
    marginTop: 8,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    borderColor: Colors.light.primary,
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: Colors.light.primary,
  },
  radioLabel: {
    marginLeft: 8,
    fontSize: 16,
    color: '#333',
  },
  agreementContainer: {
    marginBottom: 16,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  agreementText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
});
