<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KidVest - Kid Profiles</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app-container">
        <header>
            <div class="logo">
                <h1>KidVest</h1>
            </div>
            <nav>
                <ul>
                    <li><a href="#" class="active">Kid Profiles</a></li>
                    <li><a href="#">Investments</a></li>
                    <li><a href="#">Gifts</a></li>
                    <li><a href="#">Account</a></li>
                </ul>
            </nav>
        </header>

        <main>
            <div class="page-header">
                <h2>Kid Profiles</h2>
                <button id="new-profile-btn" class="btn btn-primary">+ New Profile</button>
            </div>

            <div class="profiles-container">
                <div class="profiles-list" id="profiles-list">
                    <!-- Profiles will be loaded here -->
                    <div class="loading">Loading profiles...</div>
                </div>

                <div class="profile-detail" id="profile-detail">
                    <div class="empty-state">
                        <p>Select a profile to view details or create a new profile.</p>
                    </div>
                </div>
            </div>
        </main>

        <div class="modal" id="profile-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modal-title">Create New Profile</h3>
                    <button class="close-btn" id="close-modal">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="profile-form">
                        <input type="hidden" id="profile-id">
                        <div class="form-group">
                            <label for="name">Name</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="age">Age</label>
                            <input type="number" id="age" name="age" min="0" max="18">
                        </div>
                        <div class="form-group">
                            <label for="handle">Handle</label>
                            <div class="input-with-prefix">
                                <span class="input-prefix">@</span>
                                <input type="text" id="handle" name="handle" required>
                            </div>
                            <small>This will be used for the public profile URL</small>
                        </div>
                        <div class="form-group">
                            <label for="bio">Bio</label>
                            <textarea id="bio" name="bio" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="is_public" name="is_public" checked>
                                Make profile public
                            </label>
                            <small>Public profiles can receive gifts from anyone with the link</small>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" id="cancel-btn">Cancel</button>
                            <button type="submit" class="btn btn-primary" id="save-btn">Save Profile</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="modal" id="delete-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Delete Profile</h3>
                    <button class="close-btn" id="close-delete-modal">&times;</button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this profile? This action cannot be undone.</p>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="cancel-delete-btn">Cancel</button>
                        <button type="button" class="btn btn-danger" id="confirm-delete-btn">Delete Profile</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="toast" id="toast">
            <div class="toast-content">
                <span id="toast-message"></span>
                <button class="close-toast">&times;</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
