import os
import http.server
import socketserver
from pathlib import Path

PORT = 8080
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

class SimpleHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        print(f"Request path: {self.path}")
        
        # Map paths to directories
        if self.path == "/":
            self.path = "/auth-ui/index.html"
        elif self.path.startswith("/auth"):
            self.path = "/auth-ui" + self.path[5:] if len(self.path) > 5 else "/auth-ui/index.html"
        
        # Try to serve the file
        try:
            return super().do_GET()
        except Exception as e:
            print(f"Error serving {self.path}: {str(e)}")
            self.send_error(404, f"File not found: {self.path}")
    
    def translate_path(self, path):
        # Convert path to absolute filesystem path
        path = super().translate_path(path)
        print(f"Translated path: {path}")
        return path

def run_server():
    handler = SimpleHandler
    handler.directory = BASE_DIR
    
    with socketserver.TCPServer(("", PORT), handler) as httpd:
        print(f"Serving at http://localhost:{PORT}")
        print(f"Base directory: {BASE_DIR}")
        print(f"Available UIs:")
        print(f"  - Auth UI: http://localhost:{PORT}/auth")
        httpd.serve_forever()

if __name__ == "__main__":
    run_server()
