import os
import sys
import requests
import json
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# Add the current directory to the path so we can import app modules
sys.path.append('.')

# Load environment variables
load_dotenv()

# Import models after setting up the path
from app.models import Gift, KidProfile
from app.database import Base

# Database connection
DATABASE_URL = os.getenv("DATABASE_URL")
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

def test_create_gift_api():
    """Test creating a gift through the API"""
    print("Testing gift creation through API...")
    
    # Get the kid profile
    kid_profile = db.query(KidProfile).filter(KidProfile.handle == "zohaib_ali").first()
    if not kid_profile:
        print("Error: Kid profile 'zohaib_ali' not found")
        return
    
    print(f"Found kid profile: {kid_profile.name} (@{kid_profile.handle})")
    
    # Create gift data
    gift_data = {
        "kid_profile_handle": "zohaib_ali",
        "from_name": "Test Gifter",
        "from_email": "<EMAIL>",
        "amount_usd": 25,
        "message": "Test gift from Python script",
        "is_anonymous": False
    }
    
    try:
        # Send POST request to create gift
        response = requests.post(
            "http://127.0.0.1:8000/api/wall/zohaib_ali/gift",
            json=gift_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("Gift created successfully!")
            print(f"Gift ID: {data.get('gift_id')}")
            print(f"Checkout URL: {data.get('checkout_url')}")
            return data
        else:
            print(f"Error response: {response.text}")
            return None
    
    except Exception as e:
        print(f"Error: {str(e)}")
        return None

def check_gifts_in_db():
    """Check gifts in the database"""
    print("\nChecking gifts in database...")
    
    # Get the kid profile
    kid_profile = db.query(KidProfile).filter(KidProfile.handle == "zohaib_ali").first()
    if not kid_profile:
        print("Error: Kid profile 'zohaib_ali' not found")
        return
    
    # Get all gifts for this profile
    gifts = db.query(Gift).filter(Gift.kid_profile_id == kid_profile.id).all()
    
    print(f"Found {len(gifts)} gifts for {kid_profile.name}:")
    
    for i, gift in enumerate(gifts, 1):
        print(f"\nGift #{i}:")
        print(f"  ID: {gift.id}")
        print(f"  From: {gift.from_name or 'Anonymous'}")
        print(f"  Amount: ${gift.amount_usd}")
        print(f"  Message: {gift.message or 'No message'}")
        print(f"  Payment Status: {gift.payment_status}")
        print(f"  Created At: {gift.created_at}")

if __name__ == "__main__":
    print("=== Testing Gift Creation ===")
    
    # First check existing gifts
    check_gifts_in_db()
    
    # Test creating a gift
    result = test_create_gift_api()
    
    # Check gifts again after creation
    if result:
        check_gifts_in_db()
    
    print("\nDone.")
