# KidVest API Documentation

## Overview

This document provides detailed information about the KidVest API endpoints, request/response formats, and authentication mechanisms. KidVest is a platform that allows parents to create investment accounts for children, receive monetary gifts through a social wall, and invest using Alpaca's trading API.

## Base URL

- Development: `http://localhost:8000`
- Production: `https://your-production-api.com` (Replace with actual production URL)

## Authentication

KidVest uses OAuth2 with JWT tokens for authentication.

### Authentication Endpoints

#### Login

```
POST /api/token
```

**Request Format:**
```
Content-Type: application/x-www-form-urlencoded

username=<EMAIL>&password=yourpassword
```

**Response Format:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

#### Register

```
POST /api/register
```

**Request Format:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "securepassword",
  "user_type": "parent",
  "phone_number": "**********",
  "address": "123 Main St",
  "city": "Anytown",
  "state": "CA",
  "postal_code": "12345",
  "country": "USA"
}
```

**Response Format:**
```json
{
  "id": "uuid-string",
  "name": "John Doe",
  "email": "<EMAIL>",
  "user_type": "parent",
  "created_at": "2023-05-01T12:00:00Z",
  "is_active": true,
  "phone_number": "**********",
  "address": "123 Main St",
  "city": "Anytown",
  "state": "CA",
  "postal_code": "12345",
  "country": "USA"
}
```

#### Get Current User

```
GET /api/users/me
```

**Headers:**
```
Authorization: Bearer <access_token>
```

**Response Format:**
```json
{
  "id": "uuid-string",
  "name": "John Doe",
  "email": "<EMAIL>",
  "user_type": "parent",
  "created_at": "2023-05-01T12:00:00Z",
  "is_active": true,
  "phone_number": "**********",
  "address": "123 Main St",
  "city": "Anytown",
  "state": "CA",
  "postal_code": "12345",
  "country": "USA",
  "broker_accounts": [
    {
      "id": "uuid-string",
      "broker_type": "alpaca",
      "external_account_id": "alpaca-account-id",
      "status": "active",
      "created_at": "2023-05-01T12:00:00Z"
    }
  ]
}
```

## Child Profiles API

### Get Child Profiles

```
GET /api/child-profiles
```

**Headers:**
```
Authorization: Bearer <access_token>
```

**Response Format:**
```json
[
  {
    "id": "uuid-string",
    "parent_id": "parent-uuid-string",
    "name": "Child Name",
    "age": 10,
    "handle": "child-handle",
    "is_public": true,
    "bio": "Child bio",
    "avatar": "avatar-url",
    "dob": "2013-05-01",
    "created_at": "2023-05-01T12:00:00Z",
    "updated_at": "2023-05-01T12:00:00Z"
  }
]
```

### Create Child Profile

```
POST /api/child-profiles
```

**Headers:**
```
Authorization: Bearer <access_token>
```

**Request Format:**
```json
{
  "name": "Child Name",
  "age": 10,
  "handle": "child-handle",
  "is_public": true,
  "bio": "Child bio",
  "dob": "2013-05-01"
}
```

**Response Format:**
```json
{
  "id": "uuid-string",
  "parent_id": "parent-uuid-string",
  "name": "Child Name",
  "age": 10,
  "handle": "child-handle",
  "is_public": true,
  "bio": "Child bio",
  "avatar": null,
  "dob": "2013-05-01",
  "created_at": "2023-05-01T12:00:00Z",
  "updated_at": "2023-05-01T12:00:00Z"
}
```

## Gift Wall API

### Get Gift Wall

```
GET /api/wall/{handle}
```

**Response Format:**
```json
{
  "profile": {
    "id": "uuid-string",
    "parent_id": "parent-uuid-string",
    "name": "Child Name",
    "age": 10,
    "handle": "child-handle",
    "is_public": true,
    "bio": "Child bio",
    "avatar": "avatar-url",
    "dob": "2013-05-01",
    "created_at": "2023-05-01T12:00:00Z",
    "updated_at": "2023-05-01T12:00:00Z"
  },
  "gifts": [
    {
      "id": "uuid-string",
      "from_name": "Grandma",
      "amount_usd": 50.0,
      "message": "Happy birthday!",
      "created_at": "2023-05-01T12:00:00Z"
    }
  ],
  "total_gifts": 1,
  "total_amount": 50.0
}
```

### Create Gift

```
POST /api/wall/{handle}/gift
```

**Request Format:**
```json
{
  "from_name": "Grandma",
  "from_email": "<EMAIL>",
  "amount_usd": 50.0,
  "message": "Happy birthday!",
  "is_anonymous": false
}
```

**Response Format:**
```json
{
  "success": true,
  "gift_id": "uuid-string",
  "checkout_url": "https://checkout.stripe.com/..."
}
```

## Onboarding API

### Step 1: Basic Account Creation

```
POST /api/onboarding/step1
```

**Request Format:**
```json
{
  "full_name": "John Doe",
  "email": "<EMAIL>"
}
```

**Response Format:**
```json
{
  "success": true,
  "message": "Step 1 completed successfully",
  "session_token": "session-token-string",
  "current_step": 0,
  "next_step": 1
}
```

### Step 2: Identity Verification

```
POST /api/onboarding/step2
```

**Request Format:**
```json
{
  "session_token": "session-token-string",
  "dob": "1980-01-01",
  "phone_number": "**********",
  "street_address": "123 Main St",
  "city": "Anytown",
  "state": "CA",
  "postal_code": "12345",
  "country": "USA",
  "ssn": "***********"
}
```

**Response Format:**
```json
{
  "success": true,
  "message": "Step 2 completed successfully",
  "session_token": "session-token-string",
  "current_step": 1,
  "next_step": 2
}
```

### Step 3: Financial Profile

```
POST /api/onboarding/step3
```

**Request Format:**
```json
{
  "session_token": "session-token-string",
  "employment_status": "EMPLOYED",
  "income_range": "50k_100k",
  "net_worth_range": "50k_100k",
  "funding_source": "employment_income",
  "investment_experience": "some",
  "risk_tolerance": "moderate"
}
```

**Response Format:**
```json
{
  "success": true,
  "message": "Step 3 completed successfully",
  "session_token": "session-token-string",
  "current_step": 2,
  "next_step": 3
}
```

### Step 4: Disclosures and Agreements

```
POST /api/onboarding/step4
```

**Request Format:**
```json
{
  "session_token": "session-token-string",
  "is_control_person": false,
  "is_affiliated_exchange_or_finra": false,
  "is_politically_exposed": false,
  "immediate_family_exposed": false,
  "customer_agreement_accepted": true,
  "margin_agreement_accepted": true,
  "account_agreement_accepted": true
}
```

**Response Format:**
```json
{
  "success": true,
  "message": "Step 4 completed successfully",
  "session_token": "session-token-string",
  "current_step": 3,
  "next_step": 4
}
```

### Submit to Alpaca

```
POST /api/onboarding/submit
```

**Request Format:**
```json
{
  "session_token": "session-token-string"
}
```

**Response Format:**
```json
{
  "success": true,
  "message": "Account created successfully",
  "account_id": "alpaca-account-id"
}
```

## Stripe Webhook

```
POST /webhook/
```

This endpoint handles Stripe webhook events for payment processing. It requires a Stripe signature header for verification.

**Headers:**
```
stripe-signature: t=timestamp,v1=signature
```

**Response Format:**
```json
{
  "status": "success"
}
```
