# Start-Frontend.ps1
# This script starts only the frontend server

# Set execution policy for this process
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process

# Function to check if a port is in use
function Test-PortInUse {
    param (
        [int]$Port
    )
    
    $connections = Get-NetTCPConnection -State Listen -ErrorAction SilentlyContinue | Where-Object { $_.LocalPort -eq $Port }
    return ($null -ne $connections)
}

# Check if frontend port is already in use
if (Test-PortInUse -Port 19000) {
    Write-Host "Port 19000 is already in use. Frontend server may already be running." -ForegroundColor Yellow
    $startFrontend = Read-Host "Do you want to try starting the frontend server anyway? (y/n)"
    if ($startFrontend -ne "y") {
        Write-Host "Skipping frontend server startup." -ForegroundColor Yellow
        exit
    }
}

# Start the frontend server
Write-Host "Starting frontend server..." -ForegroundColor Green
cd "$PSScriptRoot\kidvest-app-new"
npm start
