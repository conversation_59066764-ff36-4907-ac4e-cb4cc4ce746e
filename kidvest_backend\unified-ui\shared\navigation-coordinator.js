/**
 * Navigation Coordinator for KidVest UI
 * 
 * This script provides a centralized way to handle navigation between different UIs.
 * It uses a combination of URL parameters and sessionStorage to ensure consistent state
 * across navigation events.
 */

// Define the base URL for the API
const API_BASE_URL = 'http://localhost:8080/api';

// Navigation state keys
const NAV_STATE_KEY = 'kidvest_nav_state';
const AUTH_TOKEN_KEY = 'access_token';

/**
 * Initialize the navigation coordinator
 * This should be called at the start of each page load
 */
function initNavigation() {
    console.log('Initializing navigation coordinator...');
    
    // Parse URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const navSource = urlParams.get('nav_source');
    const navTarget = urlParams.get('nav_target');
    
    // Get the current page from the URL path
    const currentPage = getCurrentPage();
    
    // Log navigation information
    console.log(`Current page: ${currentPage}`);
    console.log(`Navigation source: ${navSource || 'direct'}`);
    console.log(`Navigation target: ${navTarget || 'none'}`);
    
    // Store current page in session storage
    sessionStorage.setItem('current_page', currentPage);
    
    // Check if we have a valid token
    const token = localStorage.getItem(AUTH_TOKEN_KEY);
    if (!token && currentPage !== 'auth') {
        console.log('No authentication token found, redirecting to auth page');
        navigateTo('auth');
        return false;
    }
    
    // If this is a navigation target, process any required actions
    if (navSource) {
        console.log(`Processing navigation from ${navSource} to ${currentPage}`);
        processNavigation(navSource, currentPage);
    }
    
    return true;
}

/**
 * Get the current page from the URL path
 */
function getCurrentPage() {
    const path = window.location.pathname;
    
    if (path.startsWith('/auth')) return 'auth';
    if (path.startsWith('/dashboard')) return 'dashboard';
    if (path.startsWith('/child-profile')) return 'child-profile';
    if (path.startsWith('/onboarding')) return 'onboarding';
    if (path.startsWith('/gift-wall')) return 'gift-wall';
    
    return 'unknown';
}

/**
 * Process navigation between pages
 */
function processNavigation(source, target) {
    console.log(`Processing navigation from ${source} to ${target}`);
    
    // Handle specific navigation scenarios
    if (source === 'dashboard' && target === 'child-profile') {
        console.log('Dashboard to Child Profile navigation detected');
        // Force reload of child profiles
        sessionStorage.setItem('force_reload_profiles', 'true');
    }
    
    if (source === 'dashboard' && target === 'onboarding') {
        console.log('Dashboard to Onboarding navigation detected');
        // Any special handling for onboarding
    }
}

/**
 * Navigate to another page with proper state tracking
 */
function navigateTo(page, params = {}) {
    const currentPage = getCurrentPage();
    console.log(`Navigating from ${currentPage} to ${page}`);
    
    // Build the URL with navigation parameters
    let url = `/${page}?nav_source=${currentPage}`;
    
    // Add any additional parameters
    for (const [key, value] of Object.entries(params)) {
        url += `&${key}=${encodeURIComponent(value)}`;
    }
    
    // Navigate to the new page
    console.log(`Redirecting to: ${url}`);
    window.location.href = url;
}

/**
 * Check if we need to force reload data (like profiles)
 */
function shouldForceReload(key) {
    const shouldReload = sessionStorage.getItem(`force_reload_${key}`) === 'true';
    if (shouldReload) {
        console.log(`Force reload of ${key} requested`);
        sessionStorage.removeItem(`force_reload_${key}`);
    }
    return shouldReload;
}

/**
 * Handle logout across all pages
 */
function logout() {
    console.log('Logging out...');
    
    // Clear all authentication data
    localStorage.removeItem(AUTH_TOKEN_KEY);
    localStorage.removeItem('token_type');
    localStorage.removeItem('token_expires_at');
    
    // Clear navigation state
    sessionStorage.clear();
    
    // Redirect to auth page
    window.location.href = '/auth';
}

// Export the functions for use in other scripts
window.NavCoordinator = {
    init: initNavigation,
    navigateTo: navigateTo,
    shouldForceReload: shouldForceReload,
    logout: logout,
    getCurrentPage: getCurrentPage
};

console.log('Navigation coordinator loaded');
