:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --success-color: #4caf50;
    --error-color: #f44336;
    --text-color: #333;
    --light-text: #666;
    --border-color: #ddd;
    --background-color: #f9f9f9;
    --card-background: #fff;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    margin: 0;
    padding-top: 70px;
}

/* Navigation Bar */
.navbar {
    background-color: var(--primary-color);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    height: 60px;
}

.navbar-brand {
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
    text-decoration: none;
}

.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-left: 1.5rem;
}

.nav-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.nav-link:hover {
    color: white;
}

.nav-item.active .nav-link {
    color: white;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

header {
    text-align: center;
    margin-bottom: 2rem;
}

header h1 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

header p {
    color: var(--light-text);
}

.progress-container {
    margin-bottom: 2rem;
}

.progress-bar {
    display: flex;
    justify-content: space-between;
    position: relative;
    margin-bottom: 1rem;
    max-width: 100%;
}

.progress-bar::before {
    content: '';
    background-color: var(--border-color);
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    height: 4px;
    width: 100%;
    z-index: 1;
}

.progress {
    background-color: var(--primary-color);
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    height: 4px;
    width: 0%;
    z-index: 2;
    transition: var(--transition);
}

.step {
    width: 30px;
    height: 30px;
    background-color: var(--card-background);
    border: 3px solid var(--border-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    position: relative;
    z-index: 3;
    transition: var(--transition);
}

.step.active {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.step.completed {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    color: white;
}

.step-labels {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.step-label {
    text-align: center;
    font-size: 0.8rem;
    color: var(--light-text);
    width: 20%;
    transition: var(--transition);
}

.step-label.active {
    color: var(--primary-color);
    font-weight: 500;
}

.form-container {
    background-color: var(--card-background);
    border-radius: 8px;
    box-shadow: var(--shadow);
    padding: 2rem;
    margin-bottom: 2rem;
}

.form-step {
    display: none;
}

.form-step.active {
    display: block;
}

h2 {
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: flex;
    gap: 1rem;
}

.form-row .form-group {
    flex: 1;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

input[type="text"],
input[type="email"],
input[type="date"],
select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    transition: var(--transition);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="date"]:focus,
select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
}

.form-hint {
    display: block;
    font-size: 0.8rem;
    color: var(--light-text);
    margin-top: 0.25rem;
}

.checkbox-group {
    margin-bottom: 1rem;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox-container input {
    margin-right: 0.5rem;
}

.agreement-link {
    color: var(--primary-color);
    text-decoration: none;
}

.agreement-link:hover {
    text-decoration: underline;
}

.button-group {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: rgba(67, 97, 238, 0.1);
}

.completion-container {
    text-align: center;
    padding: 2rem 0;
}

.completion-icon {
    width: 80px;
    height: 80px;
    background-color: var(--success-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    margin: 0 auto 1.5rem;
}

.account-details {
    background-color: var(--background-color);
    border-radius: 4px;
    padding: 1rem;
    margin: 1.5rem 0;
    text-align: left;
}

.detail-row {
    display: flex;
    margin-bottom: 0.5rem;
}

.detail-label {
    font-weight: 500;
    width: 100px;
}

.completion-message {
    margin-bottom: 0.5rem;
    color: var(--light-text);
}

.next-steps-message {
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    font-weight: 500;
}

.disclosure-question {
    margin-bottom: 0.5rem;
}

.radio-options {
    display: flex;
    gap: 1.5rem;
}

.radio-options label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.radio-options input {
    margin-right: 0.5rem;
}

.api-response-container {
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 2rem;
}

.api-response-container h3 {
    margin-bottom: 0.5rem;
    font-size: 1rem;
    color: var(--light-text);
}

#api-response {
    background-color: #2d2d2d;
    color: #f8f8f8;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    font-family: monospace;
    font-size: 0.9rem;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
}
