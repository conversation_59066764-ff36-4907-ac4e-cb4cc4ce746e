:root {
    --primary-color: #4361ee;
    --primary-light: #edf2ff;
    --secondary-color: #3a0ca3;
    --success-color: #4cc9a0;
    --danger-color: #ef476f;
    --warning-color: #ffd166;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --gray-color: #6c757d;
    --light-gray: #e9ecef;
    --border-color: #dee2e6;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #f5f7fb;
}

.app-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 30px;
}

.logo h1 {
    color: var(--primary-color);
    font-size: 24px;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    text-decoration: none;
    color: var(--gray-color);
    font-weight: 500;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

nav ul li a:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
}

nav ul li a.active {
    color: var(--primary-color);
    background-color: var(--primary-light);
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-header h2 {
    font-size: 24px;
    font-weight: 600;
}

/* Profiles Container */
.profiles-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 20px;
    margin-bottom: 30px;
}

.profiles-list {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    max-height: 600px;
    overflow-y: auto;
}

.profile-card {
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 10px;
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.profile-card:hover {
    background-color: var(--primary-light);
}

.profile-card.active {
    background-color: var(--primary-light);
    border-color: var(--primary-color);
}

.profile-card h3 {
    font-size: 18px;
    margin-bottom: 5px;
}

.profile-card .handle {
    color: var(--gray-color);
    font-size: 14px;
}

.profile-detail {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 30px;
}

.profile-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: var(--primary-color);
    margin-right: 20px;
}

.profile-info h3 {
    font-size: 24px;
    margin-bottom: 5px;
}

.profile-info .handle {
    color: var(--gray-color);
    font-size: 16px;
    margin-bottom: 10px;
}

.profile-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.profile-details {
    margin-top: 30px;
}

.detail-item {
    margin-bottom: 15px;
}

.detail-item label {
    font-weight: 600;
    display: block;
    margin-bottom: 5px;
    color: var(--gray-color);
}

.detail-item p {
    font-size: 16px;
}

.empty-state {
    text-align: center;
    padding: 50px 0;
    color: var(--gray-color);
}

.loading {
    text-align: center;
    padding: 20px;
    color: var(--gray-color);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: white;
    border-radius: var(--border-radius);
    width: 100%;
    max-width: 500px;
    box-shadow: var(--box-shadow);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: 20px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--gray-color);
}

.modal-body {
    padding: 20px;
}

/* Form */
.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

input[type="text"],
input[type="number"],
textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
}

input[type="text"]:focus,
input[type="number"]:focus,
textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.input-with-prefix {
    display: flex;
    align-items: center;
}

.input-prefix {
    background-color: var(--light-gray);
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-right: none;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    color: var(--gray-color);
}

.input-with-prefix input {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

small {
    display: block;
    color: var(--gray-color);
    font-size: 12px;
    margin-top: 5px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox-label input {
    margin-right: 10px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

/* Buttons */
.btn {
    padding: 10px 16px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
}

.btn-secondary {
    background-color: var(--light-gray);
    color: var(--dark-color);
}

.btn-secondary:hover {
    background-color: var(--border-color);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #d64161;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-light);
}

/* Toast */
.toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    transform: translateY(100px);
    opacity: 0;
    transition: var(--transition);
    z-index: 1001;
}

.toast.active {
    transform: translateY(0);
    opacity: 1;
}

.toast-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.close-toast {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--gray-color);
    margin-left: 15px;
}

/* Responsive */
@media (max-width: 768px) {
    .profiles-container {
        grid-template-columns: 1fr;
    }

    .profile-detail {
        margin-top: 20px;
    }

    header {
        flex-direction: column;
        align-items: flex-start;
    }

    nav ul {
        margin-top: 15px;
    }

    nav ul li {
        margin-left: 0;
        margin-right: 15px;
    }
}
