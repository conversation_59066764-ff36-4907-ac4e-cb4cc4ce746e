import requests
import json
import sys

def test_register():
    """Test the registration endpoint"""
    print("=== Testing Registration Endpoint ===")
    
    url = "http://localhost:8001/api/register"
    data = {
        "name": "Test User",
        "email": "<EMAIL>",
        "password": "password123",
        "user_type": "parent",
        "phone_number": None,
        "address": None,
        "city": None,
        "state": None,
        "postal_code": None,
        "country": None
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"Sending request to {url}")
        print(f"Request data: {json.dumps(data)}")
        
        response = requests.post(url, json=data, headers=headers)
        
        print(f"Response status code: {response.status_code}")
        print(f"Response headers: {response.headers}")
        
        try:
            print(f"Response body: {response.json()}")
        except:
            print(f"Response body (raw): {response.text}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def test_login():
    """Test the login endpoint"""
    print("\n=== Testing Login Endpoint ===")
    
    url = "http://localhost:8001/api/token"
    data = {
        "username": "<EMAIL>",
        "password": "password123"
    }
    
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    try:
        print(f"Sending request to {url}")
        print(f"Request data: {json.dumps(data)}")
        
        response = requests.post(url, data=data, headers=headers)
        
        print(f"Response status code: {response.status_code}")
        print(f"Response headers: {response.headers}")
        
        try:
            print(f"Response body: {response.json()}")
            token = response.json().get("access_token")
            if token:
                return token
        except:
            print(f"Response body (raw): {response.text}")
        
        return None
    except Exception as e:
        print(f"Error: {str(e)}")
        return None

def test_user_profile(token):
    """Test the user profile endpoint"""
    print("\n=== Testing User Profile Endpoint ===")
    
    if not token:
        print("No token available, skipping profile test")
        return False
    
    url = "http://localhost:8001/api/users/me"
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    try:
        print(f"Sending request to {url}")
        print(f"Request headers: {headers}")
        
        response = requests.get(url, headers=headers)
        
        print(f"Response status code: {response.status_code}")
        print(f"Response headers: {response.headers}")
        
        try:
            print(f"Response body: {response.json()}")
        except:
            print(f"Response body (raw): {response.text}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("Starting API endpoint tests...")
    
    # Test registration
    register_success = test_register()
    print(f"\nRegistration test {'succeeded' if register_success else 'failed'}")
    
    # Test login
    token = test_login()
    login_success = token is not None
    print(f"\nLogin test {'succeeded' if login_success else 'failed'}")
    
    # Test user profile
    if login_success:
        profile_success = test_user_profile(token)
        print(f"\nProfile test {'succeeded' if profile_success else 'failed'}")
    
    print("\nDone.")
