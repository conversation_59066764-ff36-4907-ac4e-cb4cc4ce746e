import requests
import json
import uuid
import datetime

# Base URL for API
BASE_URL = "http://localhost:8000/api/onboarding"

# Generate unique data for testing
timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
unique_id = str(uuid.uuid4())[:8]
unique_email = f"test.user.{timestamp}.{unique_id}@example.com"

def call_api(endpoint, data=None, params=None, method="POST"):
    """Call the API and return the response"""
    url = f"{BASE_URL}/{endpoint}"
    
    if params:
        param_str = "&".join([f"{k}={v}" for k, v in params.items()])
        url = f"{url}?{param_str}"
    
    print(f"\n==== API CALL: {method} {url} ====")
    if data:
        print(f"Request Data: {json.dumps(data, indent=2)}")
    
    headers = {"Content-Type": "application/json"}
    
    if method == "POST":
        response = requests.post(url, json=data, headers=headers)
    else:
        response = requests.get(url, headers=headers)
    
    print(f"Response Status: {response.status_code}")
    
    try:
        response_json = response.json()
        print(f"Response: {json.dumps(response_json, indent=2)}")
        return response_json
    except:
        print(f"Response (not JSON): {response.text}")
        return None

# Step 1: Basic account creation
print("\n===== STEP 1: BASIC ACCOUNT CREATION =====")
step1_data = {
    "full_name": "Alex Johnson",
    "email": unique_email
}
print(f"Using unique email: {unique_email}")

step1_response = call_api("step1", step1_data)
if not step1_response or not step1_response.get("success"):
    print("Step 1 failed. Exiting.")
    exit(1)

session_token = step1_response.get("session_token")
print(f"Session token: {session_token}")

# Step 2: Identity verification
print("\n===== STEP 2: IDENTITY VERIFICATION =====")
step2_data = {
    "dob": "1992-03-15",
    "phone_number": "************",
    "street_address": "789 Pine Street",
    "city": "Boston",
    "state": "MA",
    "postal_code": "02108",
    "country": "USA",
    "ssn": "***********"
}

step2_response = call_api("step2", step2_data, {"session_token": session_token})
if not step2_response or not step2_response.get("success"):
    print("Step 2 failed. Exiting.")
    exit(1)

# Step 3: Financial profile
print("\n===== STEP 3: FINANCIAL PROFILE =====")
step3_data = {
    "employment_status": "EMPLOYED",
    "income_range": "50k_100k",
    "net_worth_range": "50k_100k",
    "funding_source": "employment_income",
    "investment_experience": "some",
    "risk_tolerance": "moderate"
}

step3_response = call_api("step3", step3_data, {"session_token": session_token})
if not step3_response or not step3_response.get("success"):
    print("Step 3 failed. Exiting.")
    exit(1)

# Step 4: Disclosures and agreements
print("\n===== STEP 4: DISCLOSURES AND AGREEMENTS =====")
step4_data = {
    "is_control_person": False,
    "is_affiliated_exchange_or_finra": False,
    "is_politically_exposed": False,
    "immediate_family_exposed": False,
    "customer_agreement_accepted": True,
    "margin_agreement_accepted": True,
    "account_agreement_accepted": True
}

step4_response = call_api("step4", step4_data, {"session_token": session_token})
if not step4_response or not step4_response.get("success"):
    print("Step 4 failed. Exiting.")
    exit(1)

# Final submission
print("\n===== FINAL SUBMISSION =====")
submit_response = call_api("submit", params={"session_token": session_token})
if not submit_response or not submit_response.get("success"):
    print("Submission failed. Exiting.")
    exit(1)

print("\n===== ONBOARDING COMPLETE =====")
print(f"Account ID: {submit_response.get('account_id')}")
print(f"Status: {submit_response.get('status')}")
print("Onboarding process completed successfully!")
