// API Base URL
const API_BASE_URL = 'http://localhost:8000/api';

// DOM Elements
const profilesList = document.getElementById('profiles-list');
const profileDetail = document.getElementById('profile-detail');
const profileModal = document.getElementById('profile-modal');
const deleteModal = document.getElementById('delete-modal');
const profileForm = document.getElementById('profile-form');
const modalTitle = document.getElementById('modal-title');
const toast = document.getElementById('toast');
const toastMessage = document.getElementById('toast-message');

// Buttons
const newProfileBtn = document.getElementById('new-profile-btn');
const closeModalBtn = document.getElementById('close-modal');
const cancelBtn = document.getElementById('cancel-btn');
const closeDeleteModalBtn = document.getElementById('close-delete-modal');
const cancelDeleteBtn = document.getElementById('cancel-delete-btn');
const confirmDeleteBtn = document.getElementById('confirm-delete-btn');

// State
let profiles = [];
let selectedProfileId = null;

// Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    loadProfiles();

    // New profile button
    newProfileBtn.addEventListener('click', () => {
        openCreateModal();
    });

    // Close modal buttons
    closeModalBtn.addEventListener('click', () => {
        profileModal.classList.remove('active');
    });

    cancelBtn.addEventListener('click', () => {
        profileModal.classList.remove('active');
    });

    closeDeleteModalBtn.addEventListener('click', () => {
        deleteModal.classList.remove('active');
    });

    cancelDeleteBtn.addEventListener('click', () => {
        deleteModal.classList.remove('active');
    });

    // Form submission
    profileForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const profileId = document.getElementById('profile-id').value;
        
        if (profileId) {
            updateProfile(profileId);
        } else {
            createProfile();
        }
    });

    // Close toast
    document.querySelector('.close-toast').addEventListener('click', () => {
        toast.classList.remove('active');
    });

    // Delete confirmation
    confirmDeleteBtn.addEventListener('click', () => {
        if (selectedProfileId) {
            deleteProfile(selectedProfileId);
        }
    });
});

// API Functions
async function loadProfiles() {
    try {
        profilesList.innerHTML = '<div class="loading">Loading profiles...</div>';
        
        const response = await fetch(`${API_BASE_URL}/profiles/`);
        
        if (!response.ok) {
            throw new Error('Failed to load profiles');
        }
        
        profiles = await response.json();
        
        if (profiles.length === 0) {
            profilesList.innerHTML = '<div class="empty-state">No profiles found. Create your first profile!</div>';
        } else {
            renderProfilesList();
        }
    } catch (error) {
        showToast(`Error: ${error.message}`);
        profilesList.innerHTML = '<div class="empty-state">Failed to load profiles. Please try again.</div>';
    }
}

async function createProfile() {
    try {
        const formData = getFormData();
        
        const response = await fetch(`${API_BASE_URL}/profiles/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || 'Failed to create profile');
        }
        
        const newProfile = await response.json();
        profiles.push(newProfile);
        
        renderProfilesList();
        selectProfile(newProfile.id);
        profileModal.classList.remove('active');
        showToast('Profile created successfully!');
    } catch (error) {
        showToast(`Error: ${error.message}`);
    }
}

async function updateProfile(profileId) {
    try {
        const formData = getFormData();
        
        const response = await fetch(`${API_BASE_URL}/profiles/${profileId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || 'Failed to update profile');
        }
        
        const updatedProfile = await response.json();
        
        // Update profiles array
        const index = profiles.findIndex(p => p.id === profileId);
        if (index !== -1) {
            profiles[index] = updatedProfile;
        }
        
        renderProfilesList();
        selectProfile(updatedProfile.id);
        profileModal.classList.remove('active');
        showToast('Profile updated successfully!');
    } catch (error) {
        showToast(`Error: ${error.message}`);
    }
}

async function deleteProfile(profileId) {
    try {
        const response = await fetch(`${API_BASE_URL}/profiles/${profileId}`, {
            method: 'DELETE'
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || 'Failed to delete profile');
        }
        
        // Remove from profiles array
        profiles = profiles.filter(p => p.id !== profileId);
        
        renderProfilesList();
        profileDetail.innerHTML = '<div class="empty-state"><p>Select a profile to view details or create a new profile.</p></div>';
        deleteModal.classList.remove('active');
        selectedProfileId = null;
        showToast('Profile deleted successfully!');
    } catch (error) {
        showToast(`Error: ${error.message}`);
    }
}

// UI Functions
function renderProfilesList() {
    profilesList.innerHTML = '';
    
    profiles.forEach(profile => {
        const profileCard = document.createElement('div');
        profileCard.className = `profile-card ${profile.id === selectedProfileId ? 'active' : ''}`;
        profileCard.innerHTML = `
            <h3>${profile.name}</h3>
            <div class="handle">@${profile.handle}</div>
        `;
        
        profileCard.addEventListener('click', () => {
            selectProfile(profile.id);
        });
        
        profilesList.appendChild(profileCard);
    });
}

function selectProfile(profileId) {
    selectedProfileId = profileId;
    const profile = profiles.find(p => p.id === profileId);
    
    if (!profile) return;
    
    // Update active class in list
    document.querySelectorAll('.profile-card').forEach(card => {
        card.classList.remove('active');
    });
    
    const selectedCard = Array.from(document.querySelectorAll('.profile-card')).find(
        card => card.querySelector('h3').textContent === profile.name
    );
    
    if (selectedCard) {
        selectedCard.classList.add('active');
    }
    
    // Render profile detail
    profileDetail.innerHTML = `
        <div class="profile-header">
            <div class="profile-avatar">${getInitials(profile.name)}</div>
            <div class="profile-info">
                <h3>${profile.name}</h3>
                <div class="handle">@${profile.handle}</div>
                <div class="visibility-badge ${profile.is_public ? 'public' : 'private'}">
                    ${profile.is_public ? 'Public Profile' : 'Private Profile'}
                </div>
            </div>
        </div>
        
        <div class="profile-actions">
            <button class="btn btn-outline" id="edit-profile-btn">Edit Profile</button>
            <button class="btn btn-outline" id="view-wall-btn">View Gift Wall</button>
            <button class="btn btn-danger" id="delete-profile-btn">Delete</button>
        </div>
        
        <div class="profile-details">
            <div class="detail-item">
                <label>Age</label>
                <p>${profile.age || 'Not specified'}</p>
            </div>
            <div class="detail-item">
                <label>Bio</label>
                <p>${profile.bio || 'No bio provided'}</p>
            </div>
            <div class="detail-item">
                <label>Gift Wall URL</label>
                <p class="gift-url">${window.location.origin}/wall/${profile.handle}</p>
            </div>
        </div>
    `;
    
    // Add event listeners to buttons
    document.getElementById('edit-profile-btn').addEventListener('click', () => {
        openEditModal(profile);
    });
    
    document.getElementById('delete-profile-btn').addEventListener('click', () => {
        openDeleteModal(profile);
    });
    
    document.getElementById('view-wall-btn').addEventListener('click', () => {
        window.open(`/wall/${profile.handle}`, '_blank');
    });
}

function openCreateModal() {
    modalTitle.textContent = 'Create New Profile';
    document.getElementById('profile-id').value = '';
    profileForm.reset();
    profileModal.classList.add('active');
}

function openEditModal(profile) {
    modalTitle.textContent = 'Edit Profile';
    document.getElementById('profile-id').value = profile.id;
    document.getElementById('name').value = profile.name;
    document.getElementById('age').value = profile.age || '';
    document.getElementById('handle').value = profile.handle;
    document.getElementById('bio').value = profile.bio || '';
    document.getElementById('is_public').checked = profile.is_public;
    profileModal.classList.add('active');
}

function openDeleteModal(profile) {
    deleteModal.classList.add('active');
}

function getFormData() {
    return {
        name: document.getElementById('name').value,
        age: document.getElementById('age').value ? parseInt(document.getElementById('age').value) : null,
        handle: document.getElementById('handle').value,
        bio: document.getElementById('bio').value,
        is_public: document.getElementById('is_public').checked
    };
}

function getInitials(name) {
    return name
        .split(' ')
        .map(part => part.charAt(0))
        .join('')
        .toUpperCase();
}

function showToast(message) {
    toastMessage.textContent = message;
    toast.classList.add('active');
    
    setTimeout(() => {
        toast.classList.remove('active');
    }, 3000);
}

// Mock data for testing (remove in production)
function loadMockProfiles() {
    profiles = [
        {
            id: '1',
            name: 'Emma Johnson',
            age: 8,
            handle: 'emma_j',
            is_public: true,
            bio: 'I love science and art!',
            parent_id: '123',
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z'
        },
        {
            id: '2',
            name: 'Noah Smith',
            age: 12,
            handle: 'noah_s',
            is_public: true,
            bio: 'Future astronaut and basketball player',
            parent_id: '123',
            created_at: '2023-01-02T00:00:00Z',
            updated_at: '2023-01-02T00:00:00Z'
        }
    ];
    
    renderProfilesList();
}
