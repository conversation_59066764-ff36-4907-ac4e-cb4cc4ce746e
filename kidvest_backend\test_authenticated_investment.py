#!/usr/bin/env python3

"""
Test investment with proper authentication
"""

import requests
import json
import sys
from datetime import datetime
from sqlalchemy import text
from app.database import SessionLocal

BASE_URL = "http://localhost:8000"
AUSTIN_EMAIL = "<EMAIL>"
AUSTIN_PASSWORD = "password123"  # You'll need to provide the correct password

def login_and_get_token():
    """Login as Austin and get JWT token"""
    print("🔐 Logging in as Austin to get JWT token...")
    
    login_data = {
        "username": AUSTIN_EMAIL,
        "password": AUSTIN_PASSWORD
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/login",
            data=login_data,  # Form data for OAuth2
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            timeout=10
        )
        
        print(f"   Login response status: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get("access_token")
            print(f"   ✅ Login successful!")
            print(f"   Token type: {token_data.get('token_type')}")
            print(f"   Token: {access_token[:20]}...")
            return access_token
        else:
            print(f"   ❌ Login failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ Login error: {str(e)}")
        return None

def test_authenticated_investment(token, child_id, amount=15.0):
    """Test investment with authentication token"""
    print(f"\n💰 Testing authenticated investment...")
    print(f"   Child ID: {child_id}")
    print(f"   Amount: ${amount}")
    
    investment_request = {
        "child_profile_id": child_id,
        "stock_symbol": "SPY",
        "amount_usd": amount
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print(f"   Request: {json.dumps(investment_request, indent=2)}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/invest/",
            json=investment_request,
            headers=headers,
            timeout=60  # Longer timeout for trade execution
        )
        
        print(f"   Response Status: {response.status_code}")
        print(f"   Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"   Response Body: {json.dumps(response_data, indent=2)}")
            
            if response.status_code in [200, 201]:
                print(f"   ✅ Investment successful!")
                return response_data
            else:
                print(f"   ❌ Investment failed: {response_data}")
                return None
                
        except json.JSONDecodeError:
            print(f"   Response Text: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ Investment request failed: {str(e)}")
        return None

def check_investment_in_database():
    """Check if investment was recorded in database"""
    print(f"\n💾 Checking database for new investments...")
    
    db = SessionLocal()
    try:
        # Check recent investments
        result = db.execute(text("""
            SELECT i.id, i.amount_usd, i.symbol, i.shares, i.status, 
                   i.transaction_id, i.created_at, cp.name as child_name
            FROM investments i
            JOIN child_profiles cp ON i.child_profile_id = cp.id
            WHERE i.created_at > NOW() - INTERVAL '10 minutes'
            ORDER BY i.created_at DESC
        """))
        
        investments = result.fetchall()
        
        if not investments:
            print("   ❌ No recent investments found")
            return False
        
        print(f"   ✅ Found {len(investments)} recent investments:")
        for inv in investments:
            print(f"   - ${inv[1]} {inv[2]} for {inv[7]}")
            print(f"     Status: {inv[4]} | Shares: {inv[3]}")
            print(f"     Transaction: {inv[5]} | Created: {inv[6]}")
            print()
        
        return True
        
    except Exception as e:
        print(f"   ❌ Database check failed: {str(e)}")
        return False
    finally:
        db.close()

def get_austin_child_id():
    """Get Austin's child ID"""
    db = SessionLocal()
    try:
        result = db.execute(text("""
            SELECT cp.id, cp.name
            FROM child_profiles cp
            JOIN users u ON cp.parent_id = u.id
            WHERE u.email = :email
            LIMIT 1
        """), {"email": AUSTIN_EMAIL})
        
        child_row = result.fetchone()
        if child_row:
            return str(child_row[0]), child_row[1]
        return None, None
        
    finally:
        db.close()

def test_market_data_bypass():
    """Test if we can bypass market data issues"""
    print(f"\n📊 Testing market data bypass...")
    
    try:
        # Try to get market data
        from app.alpaca_service import get_alpaca_market_data
        
        market_data, error = get_alpaca_market_data("SPY")
        if error:
            print(f"   ⚠️ Market data error: {error}")
            print(f"   💡 Investment might still work with fallback pricing")
        else:
            print(f"   ✅ Market data available: ${market_data.get('last_price')}")
        
        return market_data, error
        
    except Exception as e:
        print(f"   ❌ Market data test failed: {str(e)}")
        return None, str(e)

def main():
    """Main test function"""
    print("🧪 Authenticated Investment Test")
    print("=" * 50)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Get child ID
    child_id, child_name = get_austin_child_id()
    if not child_id:
        print("❌ Could not find Austin's child")
        return False
    
    print(f"✅ Testing with child: {child_name} (ID: {child_id})")
    
    # Step 2: Test market data
    market_data, market_error = test_market_data_bypass()
    
    # Step 3: Login and get token
    print(f"\n⚠️ AUTHENTICATION REQUIRED")
    print(f"Please provide Austin's password for authentication test")
    
    # For security, ask for password
    import getpass
    try:
        password = getpass.getpass("Enter Austin's password: ")
    except KeyboardInterrupt:
        print("\n❌ Test cancelled")
        return False
    
    # Update password
    global AUSTIN_PASSWORD
    AUSTIN_PASSWORD = password
    
    token = login_and_get_token()
    if not token:
        print("❌ Could not authenticate - cannot test investment")
        print("💡 This explains why frontend investments are failing!")
        return False
    
    # Step 4: Test investment
    investment_result = test_authenticated_investment(token, child_id, 15.0)
    
    # Step 5: Check database
    db_success = check_investment_in_database()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 AUTHENTICATED INVESTMENT TEST SUMMARY")
    print("=" * 60)
    
    if token:
        print("✅ AUTHENTICATION: Working")
    else:
        print("❌ AUTHENTICATION: Failed")
    
    if investment_result:
        print("✅ INVESTMENT API: Working with auth")
    else:
        print("❌ INVESTMENT API: Failed")
    
    if db_success:
        print("✅ DATABASE RECORDING: Working")
    else:
        print("❌ DATABASE RECORDING: Failed")
    
    print(f"\n🔍 FRONTEND ISSUE DIAGNOSIS:")
    if not token:
        print("❌ Frontend authentication is broken")
        print("   - Check login flow in frontend")
        print("   - Verify JWT token storage")
        print("   - Check Authorization header in API calls")
    elif not investment_result:
        print("❌ Investment API has issues beyond authentication")
        print("   - Check backend logs for errors")
        print("   - Market data issues might be blocking trades")
    else:
        print("✅ Backend working - frontend auth issue")
        print("   - Frontend not sending proper auth tokens")
        print("   - Check browser dev tools for auth errors")
    
    return investment_result is not None

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
