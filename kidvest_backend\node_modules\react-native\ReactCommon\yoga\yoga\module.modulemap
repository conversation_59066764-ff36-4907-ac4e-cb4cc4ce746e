/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

module yoga [system] {
    module core {
        header "YGConfig.h"
        header "YGEnums.h"
        header "YGMacros.h"
        header "YGNode.h"
        header "YGNodeLayout.h"
        header "YGNodeStyle.h"
        header "YGPixelGrid.h"
        header "YGValue.h"
        header "Yoga.h"
        export *
    }
}
