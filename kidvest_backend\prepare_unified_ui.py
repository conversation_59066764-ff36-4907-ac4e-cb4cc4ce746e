import os
import shutil
from pathlib import Path

# Define the source directories
SOURCE_DIRS = {
    "auth": "auth-ui",
    "onboarding": "onboarding-ui",
    "child-profile": "child-profile-ui",
    "dashboard": "dashboard-ui",
    "gift-wall": "gift-wall-ui"
}

# Define the target directory
TARGET_DIR = "unified-ui"

# Create the target directory if it doesn't exist
os.makedirs(TARGET_DIR, exist_ok=True)

# Copy files from source directories to target directory
for section, source_dir in SOURCE_DIRS.items():
    if os.path.exists(source_dir):
        # Create section directory in target
        section_dir = os.path.join(TARGET_DIR, section)
        os.makedirs(section_dir, exist_ok=True)
        
        # Copy files
        for item in os.listdir(source_dir):
            source_item = os.path.join(source_dir, item)
            target_item = os.path.join(section_dir, item)
            
            if os.path.isfile(source_item):
                shutil.copy2(source_item, target_item)
                print(f"Copied {source_item} to {target_item}")
            elif os.path.isdir(source_item):
                shutil.copytree(source_item, target_item, dirs_exist_ok=True)
                print(f"Copied directory {source_item} to {target_item}")

# Create an index.html file in the target directory
index_html = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KidVest - Unified UI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 50px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .card {
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #4a6cf7;
            color: white;
            font-weight: bold;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }
        .btn-primary {
            background-color: #4a6cf7;
            border-color: #4a6cf7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h2 class="mb-0">KidVest Unified UI</h2>
            </div>
            <div class="card-body">
                <p class="lead">Welcome to the KidVest Unified UI. Please select a section to navigate to:</p>
                
                <div class="list-group mt-4">
                    <a href="/auth" class="list-group-item list-group-item-action">
                        <h5 class="mb-1">Authentication</h5>
                        <p class="mb-1">Login or register to access the KidVest platform</p>
                    </a>
                    <a href="/dashboard" class="list-group-item list-group-item-action">
                        <h5 class="mb-1">Dashboard</h5>
                        <p class="mb-1">View your account dashboard</p>
                    </a>
                    <a href="/onboarding" class="list-group-item list-group-item-action">
                        <h5 class="mb-1">Onboarding</h5>
                        <p class="mb-1">Complete the KYC onboarding process</p>
                    </a>
                    <a href="/child-profile" class="list-group-item list-group-item-action">
                        <h5 class="mb-1">Child Profiles</h5>
                        <p class="mb-1">Manage your child profiles</p>
                    </a>
                    <a href="/gift-wall" class="list-group-item list-group-item-action">
                        <h5 class="mb-1">Gift Walls</h5>
                        <p class="mb-1">View and share gift walls</p>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

with open(os.path.join(TARGET_DIR, "index.html"), "w") as f:
    f.write(index_html)
    print(f"Created {os.path.join(TARGET_DIR, 'index.html')}")

print("\nUnified UI preparation complete!")
print(f"To serve the unified UI, run: python -m http.server 8080 --directory {TARGET_DIR}")
print("Then navigate to: http://localhost:8080")
