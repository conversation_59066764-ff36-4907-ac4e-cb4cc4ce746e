import React, { createContext, useState, useEffect, useContext } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { api, authAPI } from '../services/api';

// Define the shape of our auth context
interface AuthContextType {
  user: any | null;
  isLoading: boolean;
  isSignedIn: boolean;
  needsOnboarding: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (userData: any) => Promise<void>;
  signOut: () => Promise<void>;
  completeOnboarding: () => Promise<void>;
  error: string | null;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  user: null,
  isLoading: true,
  isSignedIn: false,
  needsOnboarding: false,
  signIn: async () => {},
  signUp: async () => {},
  signOut: async () => {},
  completeOnboarding: async () => {},
  error: null,
});

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Provider component that wraps the app and makes auth object available
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [needsOnboarding, setNeedsOnboarding] = useState(false);

  // Check if the user is already logged in
  useEffect(() => {
    const loadUser = async () => {
      try {
        const token = await AsyncStorage.getItem('auth_token');
        // Check if onboarding has been completed
        const onboardingCompleted = await AsyncStorage.getItem('onboarding_completed');
        // Check if we have stored user data
        const storedUserData = await AsyncStorage.getItem('user_data');

        console.log('AuthContext: Loading user with token:', token ? 'exists' : 'none');
        console.log('AuthContext: Onboarding completed:', onboardingCompleted === 'true' ? 'yes' : 'no');
        console.log('AuthContext: Stored user data:', storedUserData ? 'exists' : 'none');

        if (token) {
          // Set the token in the API instance
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;

          try {
            // Get the user data from the backend
            let userData = await authAPI.getCurrentUser();

            // If we have stored user data with broker accounts, use that information
            if (storedUserData) {
              const parsedUserData = JSON.parse(storedUserData);

              // If the stored user data has broker accounts but the backend data doesn't,
              // merge the broker accounts into the backend data
              if (parsedUserData.broker_accounts && parsedUserData.broker_accounts.length > 0 &&
                  (!userData.broker_accounts || userData.broker_accounts.length === 0)) {
                console.log('AuthContext: Merging stored broker accounts with backend user data');
                userData = {
                  ...userData,
                  broker_accounts: parsedUserData.broker_accounts
                };

                // Store the merged data back to AsyncStorage
                await AsyncStorage.setItem('user_data', JSON.stringify(userData));
              }
            }

            // Set the user state
            setUser(userData);

            // Check if the user needs onboarding - prioritize the stored value
            if (onboardingCompleted === 'true') {
              console.log('AuthContext: Onboarding marked as completed in AsyncStorage');
              setNeedsOnboarding(false);
            } else {
              // Fall back to checking broker accounts if no stored value
              const needsOnboarding = !userData.broker_accounts || userData.broker_accounts.length === 0;
              console.log('AuthContext: Checking broker accounts, needs onboarding:', needsOnboarding);
              setNeedsOnboarding(needsOnboarding);
            }
          } catch (error) {
            console.error('Error fetching user data:', error);

            // If we have stored user data, use that as a fallback
            if (storedUserData) {
              console.log('AuthContext: Using stored user data as fallback');
              const parsedUserData = JSON.parse(storedUserData);
              setUser(parsedUserData);

              // Check if the user needs onboarding based on stored data
              const needsOnboarding = !parsedUserData.broker_accounts || parsedUserData.broker_accounts.length === 0;
              setNeedsOnboarding(onboardingCompleted === 'true' ? false : needsOnboarding);
            } else {
              // If we can't get the user data and have no fallback, sign out
              await signOut();
            }
          }
        }
      } catch (e) {
        console.error('Error loading user:', e);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, []);

  // Mark onboarding as complete
  const completeOnboarding = async () => {
    try {
      console.log('AuthContext: Marking onboarding as complete');

      // Store the onboarding completion status in AsyncStorage
      await AsyncStorage.setItem('onboarding_completed', 'true');

      // If we have a user, update their broker_accounts to indicate KYC is complete
      if (user) {
        // Create a copy of the user with broker_accounts
        const updatedUser = {
          ...user,
          broker_accounts: user.broker_accounts || [{
            id: 'default-broker-account',
            status: 'active',
            created_at: new Date().toISOString()
          }]
        };

        // Update the user state
        setUser(updatedUser);

        // Store the updated user in AsyncStorage for persistence across sessions
        await AsyncStorage.setItem('user_data', JSON.stringify(updatedUser));
        console.log('AuthContext: User data with broker accounts stored in AsyncStorage');
      }

      // Update the state
      setNeedsOnboarding(false);
      console.log('AuthContext: Onboarding marked as complete');
    } catch (error) {
      console.error('AuthContext: Error marking onboarding as complete:', error);
    }
  };

  // Sign in function
  const signIn = async (email: string, password: string) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('AuthContext: Attempting to sign in with email:', email);

      // Try to login
      let response;
      try {
        response = await authAPI.login(email, password);
        console.log('AuthContext: Login successful, response:', response);
      } catch (loginError: any) {
        console.error('AuthContext: Login error:', loginError);

        // Format a user-friendly error message
        let errorMessage = 'Failed to sign in. Please check your credentials.';

        if (loginError.response?.data?.detail) {
          errorMessage = loginError.response.data.detail;
        } else if (loginError.response?.data?.message) {
          errorMessage = loginError.response.data.message;
        } else if (loginError.message) {
          errorMessage = loginError.message;
        }

        setError(errorMessage);
        throw loginError;
      }

      // Set the token in the API instance
      if (response.access_token) {
        api.defaults.headers.common['Authorization'] = `Bearer ${response.access_token}`;
        console.log('AuthContext: Set Authorization header with token');
      } else {
        console.warn('AuthContext: No access_token in response');
      }

      try {
        // After successful login, get the user data
        console.log('AuthContext: Fetching user data');
        const userData = await authAPI.getCurrentUser();
        console.log('AuthContext: User data fetched:', userData);
        setUser(userData);

        // Check if we have stored user data from previous sessions
        const storedUserData = await AsyncStorage.getItem('user_data');

        // If we have stored user data with broker accounts, merge it with the current user data
        if (storedUserData) {
          const parsedUserData = JSON.parse(storedUserData);

          // If the stored user data has broker accounts but the current data doesn't,
          // merge the broker accounts into the current data
          if (parsedUserData.broker_accounts && parsedUserData.broker_accounts.length > 0 &&
              (!userData.broker_accounts || userData.broker_accounts.length === 0)) {
            console.log('AuthContext: Merging stored broker accounts with current user data');
            userData = {
              ...userData,
              broker_accounts: parsedUserData.broker_accounts
            };

            // Update the user state with the merged data
            setUser(userData);

            // Store the merged data back to AsyncStorage
            await AsyncStorage.setItem('user_data', JSON.stringify(userData));
          }
        } else {
          // If no stored user data, store the current user data
          await AsyncStorage.setItem('user_data', JSON.stringify(userData));
        }

        // Check if the user has completed KYC onboarding
        // First check if we have a stored value
        const onboardingCompleted = await AsyncStorage.getItem('onboarding_completed');
        if (onboardingCompleted === 'true') {
          console.log('AuthContext: Onboarding marked as completed in AsyncStorage');
          setNeedsOnboarding(false);
        } else {
          // Fall back to checking broker accounts
          const needsOnboarding = !userData.broker_accounts || userData.broker_accounts.length === 0;
          console.log('AuthContext: Checking broker accounts, needs onboarding:', needsOnboarding);
          setNeedsOnboarding(needsOnboarding);
        }
      } catch (userDataError) {
        console.error('AuthContext: Error fetching user data:', userDataError);
        // Set a default user object if we can't get the real data
        const defaultUser = {
          id: 'temp-id',
          name: 'User',
          email: email,
          user_type: 'parent',
          is_active: true,
          created_at: new Date().toISOString()
        };
        console.log('AuthContext: Setting default user:', defaultUser);
        setUser(defaultUser);

        // Assume they need onboarding
        console.log('AuthContext: Assuming user needs onboarding');
        setNeedsOnboarding(true);
      }
    } catch (e: any) {
      console.error('AuthContext: Sign in error:', e);
      // Error message is already set in the try/catch for login
      throw e;
    } finally {
      setIsLoading(false);
    }
  };

  // Sign up function
  const signUp = async (userData: any) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('AuthContext: Attempting to sign up with email:', userData.email);

      // Try to register
      let response;
      try {
        response = await authAPI.register(userData);
        console.log('AuthContext: Registration successful, response:', response);
      } catch (registerError: any) {
        console.error('AuthContext: Registration error:', registerError);

        // Format a user-friendly error message
        let errorMessage = 'Failed to sign up. Please try again.';

        if (registerError.response?.data?.detail) {
          errorMessage = registerError.response.data.detail;
        } else if (registerError.response?.data?.message) {
          errorMessage = registerError.response.data.message;
        } else if (registerError.message) {
          errorMessage = registerError.message;
        }

        setError(errorMessage);
        throw registerError;
      }

      // Automatically sign in after registration
      console.log('AuthContext: Attempting to sign in after registration');
      try {
        await signIn(userData.email, userData.password);
        console.log('AuthContext: Sign in after registration successful');
      } catch (signInError) {
        console.error('AuthContext: Error signing in after registration:', signInError);
        // Even if sign in fails, we'll consider the registration successful
        // Set a default user object
        const defaultUser = {
          id: response.id || 'temp-id',
          name: `${userData.firstName} ${userData.lastName}`,
          email: userData.email,
          user_type: 'parent',
          is_active: true,
          created_at: new Date().toISOString()
        };
        console.log('AuthContext: Setting default user after registration:', defaultUser);
        setUser(defaultUser);

        // New users always need onboarding
        console.log('AuthContext: Setting needsOnboarding to true for new user');
        setNeedsOnboarding(true);
      }
    } catch (e: any) {
      console.error('AuthContext: Sign up error:', e);
      // Error message is already set in the try/catch for register
      throw e;
    } finally {
      setIsLoading(false);
    }
  };

  // Sign out function
  const signOut = async () => {
    setIsLoading(true);

    try {
      console.log('AuthContext: Signing out user');

      // Save the current user data before clearing
      const currentUserData = user ? JSON.stringify(user) : null;
      const currentOnboardingStatus = await AsyncStorage.getItem('onboarding_completed');

      // Clear only authentication-related items from AsyncStorage
      console.log('AuthContext: Clearing auth tokens from AsyncStorage');
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('refresh_token');

      // Then call the logout API
      console.log('AuthContext: Calling logout API');
      await authAPI.logout();

      // Clear the user data
      console.log('AuthContext: Clearing user data');
      setUser(null);

      // Reset the onboarding status
      console.log('AuthContext: Resetting onboarding status');
      setNeedsOnboarding(false);

      // Remove the token from the API instance
      console.log('AuthContext: Removing token from API headers');
      delete api.defaults.headers.common['Authorization'];

      // For debugging, let's check if the auth tokens were actually removed
      const token = await AsyncStorage.getItem('auth_token');
      console.log('AuthContext: After signOut - token exists:', !!token);

      // Verify that user data and onboarding status are still preserved
      const userData = await AsyncStorage.getItem('user_data');
      const onboardingCompleted = await AsyncStorage.getItem('onboarding_completed');
      console.log('AuthContext: After signOut - user_data exists:', !!userData);
      console.log('AuthContext: After signOut - onboarding_completed exists:', !!onboardingCompleted);

      console.log('AuthContext: User signed out successfully');
    } catch (e) {
      console.error('AuthContext: Error signing out:', e);
      // Even if there's an error, still clear the local state
      setUser(null);
      setNeedsOnboarding(false);
      delete api.defaults.headers.common['Authorization'];
    } finally {
      setIsLoading(false);
    }
  };

  // Compute isSignedIn based on user state
  const isSignedIn = !!user;

  // The value that will be provided to consumers of this context
  const value = {
    user,
    isLoading,
    isSignedIn,
    needsOnboarding,
    signIn,
    signUp,
    signOut,
    completeOnboarding,
    error,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export default AuthContext;
