# Start-Backend.ps1
# This script starts only the backend server

# Set execution policy for this process
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process

# Function to check if a port is in use
function Test-PortInUse {
    param (
        [int]$Port
    )

    $connections = Get-NetTCPConnection -State Listen -ErrorAction SilentlyContinue | Where-Object { $_.LocalPort -eq $Port }
    return ($null -ne $connections)
}

# Check if backend port is already in use
if (Test-PortInUse -Port 8000) {
    Write-Host "Port 8000 is already in use. Backend server may already be running." -ForegroundColor Yellow
    $startBackend = Read-Host "Do you want to try starting the backend server anyway? (y/n)"
    if ($startBackend -ne "y") {
        Write-Host "Skipping backend server startup." -ForegroundColor Yellow
        exit
    }
}

# Activate the virtual environment and start the backend server
Write-Host "Starting backend server..." -ForegroundColor Green
cd "$PSScriptRoot"
.\venv\Scripts\activate

# Set the PYTHONPATH environment variable to include the current directory
$env:PYTHONPATH = "$PSScriptRoot"
Write-Host "PYTHONPATH set to: $env:PYTHONPATH" -ForegroundColor Cyan

# Display startup information
Write-Host ""
Write-Host "🎉 KidVest Unified Server Starting..." -ForegroundColor Green
Write-Host "📊 API Documentation: http://localhost:8000/docs" -ForegroundColor Cyan
Write-Host "🎁 Gift Wall UI: http://localhost:8000/wall/test-child-165125" -ForegroundColor Cyan
Write-Host "📱 Mobile API Base: http://localhost:8000/api" -ForegroundColor Cyan
Write-Host ""

# Try different ways to start the backend
Write-Host "Attempting to start the backend server..." -ForegroundColor Green
try {
    # First attempt: Run as a module
    Write-Host "Attempt 1: Running as a module..." -ForegroundColor Cyan
    python -m app.main
} catch {
    Write-Host "First attempt failed: $_" -ForegroundColor Yellow

    try {
        # Second attempt: Run the file directly
        Write-Host "Attempt 2: Running the file directly..." -ForegroundColor Cyan
        python "$PSScriptRoot\app\main.py"
    } catch {
        Write-Host "Second attempt failed: $_" -ForegroundColor Yellow

        # Third attempt: Use uvicorn directly
        try {
            Write-Host "Attempt 3: Using uvicorn directly..." -ForegroundColor Cyan
            uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
        } catch {
            Write-Host "Third attempt failed: $_" -ForegroundColor Yellow

            # Fourth attempt: Use the simple_backend.py if it exists
            if (Test-Path "$PSScriptRoot\simple_backend.py") {
                Write-Host "Attempt 4: Running simple_backend.py..." -ForegroundColor Cyan
                python "$PSScriptRoot\simple_backend.py"
            } else {
                Write-Host "No simple_backend.py found. All attempts failed." -ForegroundColor Red
                Write-Host "Please check your Python installation and project structure." -ForegroundColor Red
            }
        }
    }
}
