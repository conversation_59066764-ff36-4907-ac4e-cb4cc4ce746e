// API Base URL
const API_BASE_URL = 'http://localhost:8080/api';

// For debugging
console.log('Dashboard UI script loaded');

// DOM Elements
const userNameElement = document.getElementById('user-name');
const logoutBtn = document.getElementById('logout-btn');
const manageProfilesBtn = document.getElementById('manage-profiles-btn');
const addChildProfileBtn = document.getElementById('add-child-profile-btn');

/**
 * Initialize the dashboard
 */
function initDashboard() {
    console.log('Dashboard UI: Initializing...');

    // Require authentication
    if (!KidVest.StateManager.requireAuth()) {
        return; // This will redirect to auth if not authenticated
    }

    // Load user profile
    loadUserProfile();

    // Set up event listeners
    setupEventListeners();
}

/**
 * Set up event listeners for buttons
 */
function setupEventListeners() {
    // Logout button
    logoutBtn.addEventListener('click', () => {
        console.log('Logout button clicked');
        KidVest.StateManager.clearToken();
        KidVest.StateManager.navigateTo('auth');
    });

    // Manage Profiles button
    if (manageProfilesBtn) {
        manageProfilesBtn.addEventListener('click', () => {
            console.log('Manage Profiles button clicked');
            KidVest.StateManager.navigateTo('child-profile');
        });
    }

    // Add Child Profile button
    if (addChildProfileBtn) {
        addChildProfileBtn.addEventListener('click', () => {
            console.log('Add Child Profile button clicked');
            KidVest.StateManager.navigateTo('child-profile');
        });
    }
}

/**
 * Load the user profile
 */
async function loadUserProfile() {
    try {
        console.log('Loading user profile...');

        const response = await KidVest.StateManager.apiRequest('users/me', {
            method: 'GET'
        });

        if (!response) {
            console.error('Failed to get response from API');
            return;
        }

        if (response.ok) {
            const userData = await response.json();
            console.log('User profile loaded:', userData);
            userNameElement.textContent = `Welcome, ${userData.name}`;
        } else {
            console.error('Failed to load user profile, status:', response.status);
        }
    } catch (error) {
        console.error('Error loading user profile:', error);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initDashboard);
