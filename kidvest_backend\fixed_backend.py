from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import OAuth2P<PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
from typing import Optional, List
import uvicorn
import uuid
from datetime import datetime, timedelta
import traceback
import sys

# Import app modules
from app.database import SessionLocal, engine
from app import models, schemas, crud, auth

# Create FastAPI app
app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    print(f"Global exception handler caught: {str(exc)}")
    traceback.print_exc(file=sys.stdout)
    return JSONResponse(
        status_code=500,
        content={"detail": f"Internal Server Error: {str(exc)}"}
    )

# Dependency for DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Root route
@app.get("/")
def read_root():
    print("Root endpoint called")
    return {"message": "Welcome to Fixed KidVest Backend!"}

# Register route
@app.post("/api/register", response_model=schemas.UserResponse)
def register_user(user_data: schemas.UserCreate, db: Session = Depends(get_db)):
    print(f"\n=== Registration Request ===\nData: {user_data}")
    
    try:
        # Check if user already exists
        db_user = crud.get_user_by_email(db, user_data.email)
        if db_user:
            print(f"Email already registered: {user_data.email}")
            raise HTTPException(status_code=400, detail="Email already registered")
        
        # Create new user with hashed password
        hashed_password = auth.get_password_hash(user_data.password)
        
        # Create user object
        db_user = models.User(
            name=user_data.name,
            email=user_data.email,
            hashed_password=hashed_password,
            user_type=user_data.user_type,
            phone_number=user_data.phone_number,
            address=user_data.address,
            city=user_data.city,
            state=user_data.state,
            postal_code=user_data.postal_code,
            country=user_data.country,
            is_active=True
        )
        
        # Add to database
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        
        print(f"User registered successfully: {db_user.id} ({db_user.email})")
        return db_user
    except Exception as e:
        db.rollback()
        print(f"Error registering user: {str(e)}")
        traceback.print_exc(file=sys.stdout)
        raise HTTPException(status_code=500, detail=f"Error registering user: {str(e)}")

# Token route
@app.post("/api/token")
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = auth.authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=401,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=auth.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = auth.create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )
    
    return {"access_token": access_token, "token_type": "bearer"}

# User profile route
@app.get("/api/users/me", response_model=schemas.UserResponse)
async def read_users_me(current_user: models.User = Depends(auth.get_current_active_user)):
    return current_user

if __name__ == "__main__":
    print("Starting fixed backend on port 8001...")
    uvicorn.run(app, host="0.0.0.0", port=8001)
