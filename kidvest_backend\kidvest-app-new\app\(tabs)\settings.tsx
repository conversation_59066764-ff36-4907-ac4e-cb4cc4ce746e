import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, Switch, Alert, ScrollView } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

import { Text, View } from '@/components/Themed';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';
import { useAuth } from '../../context/AuthContext';

export default function SettingsScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { user, signOut } = useAuth();

  const [pushNotifications, setPushNotifications] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(colorScheme === 'dark');

  // Simple direct logout function
  const handleDirectLogout = () => {
    console.log('Settings: Direct logout called');
    // Navigate to the logout screen
    router.replace('/auth/logout');
  };

  // Logout with confirmation
  const handleLogout = () => {
    console.log('Settings: handleLogout called');
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => console.log('Settings: Logout cancelled'),
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => {
            console.log('Settings: Logout confirmed, navigating to logout screen');
            router.replace('/auth/logout');
          },
        },
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            Alert.alert('Not Implemented', 'This feature is not yet implemented.');
          },
        },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Settings</Text>
        <TouchableOpacity
          style={[styles.logoutButtonSmall, { borderColor: Colors[colorScheme].primary }]}
          onPress={handleDirectLogout}
        >
          <FontAwesome5 name="sign-out-alt" size={16} color={Colors[colorScheme].primary} />
          <Text style={[styles.logoutButtonTextSmall, { color: Colors[colorScheme].primary }]}>Logout</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* Profile Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Profile</Text>
          <View style={styles.profileCard}>
            <View style={styles.profileInfo}>
              <View style={[styles.avatarPlaceholder, { backgroundColor: Colors[colorScheme].primary + '20' }]}>
                <Text style={[styles.avatarInitial, { color: Colors[colorScheme].primary }]}>
                  {user?.name ? user.name.charAt(0).toUpperCase() : 'U'}
                </Text>
              </View>
              <View style={styles.profileDetails}>
                <Text style={styles.profileName}>{user?.name || 'User'}</Text>
                <Text style={styles.profileEmail}>{user?.email || '<EMAIL>'}</Text>
              </View>
            </View>
            <TouchableOpacity style={styles.editProfileButton}>
              <Text style={[styles.editProfileText, { color: Colors[colorScheme].primary }]}>Edit</Text>
            </TouchableOpacity>
          </View>
        </View>

      {/* Preferences Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Preferences</Text>
        <View style={styles.settingsCard}>
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <FontAwesome5 name="bell" size={18} color="#666" style={styles.settingIcon} />
              <Text style={styles.settingLabel}>Push Notifications</Text>
            </View>
            <Switch
              value={pushNotifications}
              onValueChange={setPushNotifications}
              trackColor={{ false: '#E0E0E0', true: Colors[colorScheme].primary + '80' }}
              thumbColor={pushNotifications ? Colors[colorScheme].primary : '#F5F5F5'}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <FontAwesome5 name="envelope" size={18} color="#666" style={styles.settingIcon} />
              <Text style={styles.settingLabel}>Email Notifications</Text>
            </View>
            <Switch
              value={emailNotifications}
              onValueChange={setEmailNotifications}
              trackColor={{ false: '#E0E0E0', true: Colors[colorScheme].primary + '80' }}
              thumbColor={emailNotifications ? Colors[colorScheme].primary : '#F5F5F5'}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <FontAwesome5 name="moon" size={18} color="#666" style={styles.settingIcon} />
              <Text style={styles.settingLabel}>Dark Mode</Text>
            </View>
            <Switch
              value={darkMode}
              onValueChange={setDarkMode}
              trackColor={{ false: '#E0E0E0', true: Colors[colorScheme].primary + '80' }}
              thumbColor={darkMode ? Colors[colorScheme].primary : '#F5F5F5'}
            />
          </View>
        </View>
      </View>

      {/* Security Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Security</Text>
        <View style={styles.settingsCard}>
          <TouchableOpacity style={styles.settingButton}>
            <View style={styles.settingInfo}>
              <FontAwesome5 name="lock" size={18} color="#666" style={styles.settingIcon} />
              <Text style={styles.settingLabel}>Change Password</Text>
            </View>
            <FontAwesome5 name="chevron-right" size={16} color="#CCCCCC" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.settingButton}>
            <View style={styles.settingInfo}>
              <FontAwesome5 name="shield-alt" size={18} color="#666" style={styles.settingIcon} />
              <Text style={styles.settingLabel}>Two-Factor Authentication</Text>
            </View>
            <FontAwesome5 name="chevron-right" size={16} color="#CCCCCC" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Support Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Support</Text>
        <View style={styles.settingsCard}>
          <TouchableOpacity style={styles.settingButton}>
            <View style={styles.settingInfo}>
              <FontAwesome5 name="question-circle" size={18} color="#666" style={styles.settingIcon} />
              <Text style={styles.settingLabel}>Help Center</Text>
            </View>
            <FontAwesome5 name="chevron-right" size={16} color="#CCCCCC" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.settingButton}>
            <View style={styles.settingInfo}>
              <FontAwesome5 name="file-alt" size={18} color="#666" style={styles.settingIcon} />
              <Text style={styles.settingLabel}>Terms of Service</Text>
            </View>
            <FontAwesome5 name="chevron-right" size={16} color="#CCCCCC" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.settingButton}>
            <View style={styles.settingInfo}>
              <FontAwesome5 name="shield-alt" size={18} color="#666" style={styles.settingIcon} />
              <Text style={styles.settingLabel}>Privacy Policy</Text>
            </View>
            <FontAwesome5 name="chevron-right" size={16} color="#CCCCCC" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Account Actions */}
      <View style={styles.accountActions}>
        <TouchableOpacity
          style={[styles.logoutButton, { borderColor: Colors[colorScheme].primary }]}
          onPress={handleLogout}
        >
          <FontAwesome5 name="sign-out-alt" size={18} color={Colors[colorScheme].primary} />
          <Text style={[styles.logoutButtonText, { color: Colors[colorScheme].primary }]}>Logout</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.deleteAccountButton}
          onPress={handleDeleteAccount}
        >
          <Text style={styles.deleteAccountText}>Delete Account</Text>
        </TouchableOpacity>
      </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  logoutButtonSmall: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    borderWidth: 1,
  },
  logoutButtonTextSmall: {
    fontWeight: 'bold',
    marginLeft: 6,
    fontSize: 14,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    paddingHorizontal: 16,
  },
  profileCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginHorizontal: 16,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarInitial: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  profileDetails: {},
  profileName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    color: '#666',
  },
  editProfileButton: {
    padding: 8,
  },
  editProfileText: {
    fontWeight: '500',
  },
  settingsCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingIcon: {
    marginRight: 16,
    width: 24,
    textAlign: 'center',
  },
  settingLabel: {
    fontSize: 16,
  },
  settingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  accountActions: {
    padding: 16,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 16,
  },
  logoutButtonText: {
    fontWeight: 'bold',
    marginLeft: 8,
  },
  deleteAccountButton: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  deleteAccountText: {
    color: '#F44336',
    fontSize: 14,
  },
});
