# KidVest Multi-Step Onboarding UI

This is a user interface for testing the multi-step onboarding process for KidVest. It provides a clean, modern interface for creating Alpaca brokerage accounts through your FastAPI backend.

## Features

- **Progressive Multi-Step Form**: Breaks down the complex onboarding process into manageable steps
- **Visual Progress Tracking**: Shows users where they are in the process
- **Real-time API Feedback**: Displays API responses for debugging
- **Responsive Design**: Works on desktop and mobile devices
- **Form Validation**: Ensures all required fields are filled out

## Setup Instructions

### Prerequisites

- Python 3.7+
- FastAPI backend running

### Starting the Servers

1. **Using the Batch File (Windows)**:
   - Simply run `start_servers.bat` to start both the backend and frontend servers

2. **Manual Start**:
   - Start the FastAPI backend:
     ```
     uvicorn app.main:app --reload
     ```
   - Start the frontend server:
     ```
     cd onboarding-ui
     python server.py
     ```

3. **Access the UI**:
   - Open your browser and navigate to `http://localhost:8080`

## Using the UI

### Step 1: Basic Account Information
- Enter your full name
- Enter your email address
- Click "Next"

### Step 2: Identity Verification
- Enter your date of birth
- Enter your phone number
- Enter your address information
- Enter your SSN (use a valid format like ***********)
- Click "Next"

### Step 3: Financial Profile
- Select your employment status
- Select your income range
- Select your net worth range
- Select your funding source
- Select your investment experience
- Select your risk tolerance
- Click "Next"

### Step 4: Disclosures and Agreements
- Answer the disclosure questions
- Accept all agreements
- Click "Submit"

### Completion
- View your account details
- Click "Start Over" to begin again

## API Response Panel

The UI includes an API Response panel at the bottom of the page that shows the raw JSON response from the API for each step. This is useful for debugging and understanding the API behavior.

## Troubleshooting

- **CORS Errors**: If you see CORS errors in the console, make sure the FastAPI backend has CORS middleware enabled
- **Connection Errors**: Ensure both servers are running on the correct ports
- **Form Validation Errors**: Check the API Response panel for detailed error messages

## Customization

You can customize the UI by modifying the following files:

- `onboarding-ui/index.html`: HTML structure
- `onboarding-ui/styles.css`: Visual styling
- `onboarding-ui/script.js`: Form behavior and API interactions

## Next Steps

This UI is designed for testing and demonstration purposes. For production use, consider:

1. Adding more robust form validation
2. Implementing user authentication
3. Adding error recovery mechanisms
4. Enhancing the visual design
5. Adding analytics tracking
