import axios from 'axios';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Determine the appropriate base URL based on the environment
// For Expo Go on physical devices, use your computer's IP address
// For web or emulators, localhost should work
const getBaseUrl = () => {
  // Check if running in production
  if (Platform.OS !== 'web' && !__DEV__) {
    // Production environment
    return 'https://your-production-api.com'; // Replace with your production API URL
  } else if (Platform.OS === 'web') {
    // Web environment
    return 'http://localhost:8000';
  } else if (Platform.OS === 'android') {
    // Android emulator - ******** is a special IP that points to the host machine
    return 'http://********:8000';
  } else {
    // iOS simulator or physical device
    // For physical devices, replace with your computer's IP address
    return 'http://localhost:8000';
  }
};

// Log the base URL for debugging
console.log(`API configured with baseURL: ${getBaseUrl()}`);

// Create an axios instance
export const api = axios.create({
  baseURL: getBaseUrl(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to add the auth token to requests
api.interceptors.request.use(
  async (config) => {
    const token = await AsyncStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // If the error is 401 and we haven't already tried to refresh the token
    if (error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = await AsyncStorage.getItem('refresh_token');

        if (refreshToken) {
          // Call your token refresh endpoint
          const response = await axios.post('http://localhost:8000/api/token/refresh', {
            refresh_token: refreshToken,
          });

          // Save the new tokens
          await AsyncStorage.setItem('auth_token', response.data.access_token);
          await AsyncStorage.setItem('refresh_token', response.data.refresh_token || '');

          // Update the authorization header
          api.defaults.headers.common['Authorization'] = `Bearer ${response.data.access_token}`;
          originalRequest.headers.Authorization = `Bearer ${response.data.access_token}`;

          // Retry the original request
          return api(originalRequest);
        }
      } catch (refreshError) {
        console.error('Error refreshing token:', refreshError);
        // If refresh fails, sign out the user
        await AsyncStorage.removeItem('auth_token');
        await AsyncStorage.removeItem('refresh_token');
        // Redirect to login (this would be handled by your navigation)
      }
    }

    return Promise.reject(error);
  }
);

// Auth API calls
export const authAPI = {
  login: async (email: string, password: string) => {
    try {
      console.log('Attempting login with:', { email });

      // Backend uses OAuth2 password flow which expects username and password as form data
      const formData = new URLSearchParams();
      formData.append('username', email); // OAuth2 expects 'username' even though we're using email
      formData.append('password', password);

      console.log('Sending form data to /api/token');
      const response = await api.post('/api/token', formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      console.log('Login response:', response.data);

      // Save tokens to AsyncStorage
      await AsyncStorage.setItem('auth_token', response.data.access_token);
      if (response.data.refresh_token) {
        await AsyncStorage.setItem('refresh_token', response.data.refresh_token);
      }

      return response.data;
    } catch (error) {
      console.error('Login error:', error);

      // Try alternative login approaches if the first one fails
      try {
        // Try with direct JSON payload
        console.log('Trying alternative login approach with JSON payload');
        const jsonResponse = await api.post('/api/token', {
          username: email,
          password
        });

        console.log('Alternative login response:', jsonResponse.data);

        // Save tokens to AsyncStorage
        await AsyncStorage.setItem('auth_token', jsonResponse.data.access_token);
        if (jsonResponse.data.refresh_token) {
          await AsyncStorage.setItem('refresh_token', jsonResponse.data.refresh_token);
        }

        return jsonResponse.data;
      } catch (jsonError) {
        console.error('JSON login approach failed:', jsonError);

        // Try with email field instead of username
        try {
          console.log('Trying login with email field');
          const emailResponse = await api.post('/api/token', {
            email,
            password
          });

          console.log('Email login response:', emailResponse.data);

          // Save tokens to AsyncStorage
          await AsyncStorage.setItem('auth_token', emailResponse.data.access_token);
          if (emailResponse.data.refresh_token) {
            await AsyncStorage.setItem('refresh_token', emailResponse.data.refresh_token);
          }

          return emailResponse.data;
        } catch (emailError) {
          console.error('Email login approach failed:', emailError);
          throw error; // Throw the original error
        }
      }
    }
  },

  register: async (userData: any) => {
    try {
      console.log('Attempting registration with:', { email: userData.email });

      // Transform the user data to match backend expectations based on schemas.UserCreate
      const backendUserData = {
        name: `${userData.firstName} ${userData.lastName}`,
        email: userData.email,
        password: userData.password,
        user_type: 'parent',
        phone_number: userData.phone || '',
        address: userData.address || '',
        city: userData.city || '',
        state: userData.state || '',
        postal_code: userData.zipCode || '',
        country: 'USA'
      };

      console.log('Sending registration data to /api/register:', backendUserData);
      const response = await api.post('/api/register', backendUserData);
      console.log('Registration response:', response.data);

      return response.data;
    } catch (error) {
      console.error('Registration error:', error);

      // Try alternative registration approaches if the first one fails
      try {
        // Try with minimal data
        console.log('Trying alternative registration approach with minimal data');
        const minimalUserData = {
          name: `${userData.firstName} ${userData.lastName}`,
          email: userData.email,
          password: userData.password,
          user_type: 'parent'
        };

        const minimalResponse = await api.post('/api/register', minimalUserData);
        console.log('Minimal registration response:', minimalResponse.data);

        return minimalResponse.data;
      } catch (minimalError) {
        console.error('Minimal registration approach failed:', minimalError);

        // Try with different field names
        try {
          console.log('Trying registration with different field names');
          const alternativeUserData = {
            full_name: `${userData.firstName} ${userData.lastName}`,
            email: userData.email,
            password: userData.password,
            user_type: 'parent',
            phone: userData.phone || '',
            street_address: userData.address || '',
            city: userData.city || '',
            state: userData.state || '',
            postal_code: userData.zipCode || '',
            country: 'USA'
          };

          const altResponse = await api.post('/api/register', alternativeUserData);
          console.log('Alternative field names registration response:', altResponse.data);

          return altResponse.data;
        } catch (altError) {
          console.error('Alternative field names registration approach failed:', altError);
          throw error; // Throw the original error
        }
      }
    }
  },

  logout: async () => {
    try {
      console.log('API: Logging out user');

      // Try to call the backend logout endpoint if available
      try {
        console.log('API: Attempting to call backend logout endpoint');
        await api.post('/api/logout');
        console.log('API: Backend logout successful');
      } catch (backendError) {
        console.log('API: Backend logout failed or not available:', backendError);
        // Continue with local logout even if backend logout fails
      }

      // Clear tokens from AsyncStorage
      console.log('API: Clearing tokens from AsyncStorage');
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('refresh_token');

      // Clear authorization header
      console.log('API: Clearing authorization header');
      delete api.defaults.headers.common['Authorization'];

      console.log('API: Logout complete');
    } catch (error) {
      console.error('API: Error during logout:', error);
      // Ensure tokens are removed even if there's an error
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('refresh_token');
      delete api.defaults.headers.common['Authorization'];
    }
  },

  getCurrentUser: async () => {
    try {
      console.log('Fetching current user data from /api/users/me');
      const token = await AsyncStorage.getItem('auth_token');
      console.log('Using token:', token ? 'Token exists' : 'No token');

      const response = await api.get('/api/users/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('Current user data:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching current user:', error);

      // Try alternative endpoints if the first one fails
      try {
        console.log('Trying alternative endpoint /api/user');
        const altResponse = await api.get('/api/user');
        console.log('Alternative user data:', altResponse.data);
        return altResponse.data;
      } catch (altError) {
        console.error('Error fetching from /api/user:', altError);

        try {
          console.log('Trying alternative endpoint /api/me');
          const altResponse2 = await api.get('/api/me');
          console.log('Alternative user data from /api/me:', altResponse2.data);
          return altResponse2.data;
        } catch (altError2) {
          console.error('Error fetching from /api/me:', altError2);

          // If all endpoints fail, return a default user object
          console.log('All endpoints failed, returning default user object');
          return {
            id: 'default-id',
            name: 'Default User',
            email: '<EMAIL>',
            user_type: 'parent',
            is_active: true,
            created_at: new Date().toISOString()
          };
        }
      }
    }
  },
};

// Child profiles API
export const childProfilesAPI = {
  getProfiles: async () => {
    const response = await api.get('/api/child-profiles');
    return response.data;
  },

  getProfile: async (id: string) => {
    const response = await api.get(`/api/child-profiles/${id}`);
    return response.data;
  },

  createProfile: async (profileData: any) => {
    const response = await api.post('/api/child-profiles', profileData);
    return response.data;
  },

  updateProfile: async (id: string, profileData: any) => {
    const response = await api.put(`/api/child-profiles/${id}`, profileData);
    return response.data;
  },

  deleteProfile: async (id: string) => {
    const response = await api.delete(`/api/child-profiles/${id}`);
    return response.data;
  },
};

// Onboarding API
export const onboardingAPI = {
  submitStep1: async (data: any) => {
    const response = await api.post('/api/onboarding/step1', data);
    return response.data;
  },

  submitStep2: async (data: any) => {
    const response = await api.post('/api/onboarding/step2', data);
    return response.data;
  },

  submitStep3: async (data: any) => {
    const response = await api.post('/api/onboarding/step3', data);
    return response.data;
  },

  submitStep4: async (data: any) => {
    const response = await api.post('/api/onboarding/step4', data);
    return response.data;
  },

  submitToAlpaca: async (sessionToken: string) => {
    try {
      console.log('Submitting to Alpaca with session token:', sessionToken);
      // The correct endpoint is /api/onboarding/submit, not /api/onboarding/submit-to-alpaca
      const response = await api.post('/api/onboarding/submit', {
        session_token: sessionToken
      });
      console.log('Alpaca submission response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error submitting to Alpaca:', error);
      throw error;
    }
  },
};

// Gift API
export const giftAPI = {
  // Get all gifts
  getGifts: async () => {
    const response = await api.get('/api/gifts');
    return response.data;
  },

  // Get a specific gift by ID
  getGift: async (id: string) => {
    const response = await api.get(`/api/gifts/${id}`);
    return response.data;
  },

  // Create a gift (legacy endpoint)
  createGift: async (giftData: any) => {
    const response = await api.post('/api/gifts', giftData);
    return response.data;
  },

  // Create a checkout session (legacy endpoint)
  createCheckoutSession: async (giftData: any) => {
    const response = await api.post('/api/payments/create-checkout-session', giftData);
    return response.data;
  },

  // Get gift walls for the current user
  getGiftWalls: async () => {
    try {
      const response = await api.get('/api/child-profiles');
      // Transform child profiles into gift walls
      return response.data.map((profile: any) => ({
        id: profile.id,
        childName: profile.name,
        childAge: profile.age || 0,
        handle: profile.handle,
        totalGifts: profile.total_gifts || 0,
        totalAmount: profile.total_amount || 0,
        recentGifts: profile.recent_gifts || [],
        shareUrl: `https://kidvest.app/gift/${profile.handle}`,
      }));
    } catch (error) {
      console.error('Error fetching gift walls:', error);
      return [];
    }
  },

  // Get a specific gift wall by handle
  getGiftWall: async (handle: string) => {
    try {
      const response = await api.get(`/api/wall/${handle}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching gift wall for handle ${handle}:`, error);
      throw error;
    }
  },

  // Create a gift via the gift wall
  createGiftViaWall: async (data: {
    child_profile_handle: string;
    from_name: string;
    from_email: string;
    amount_usd: number;
    message?: string;
    is_anonymous?: boolean;
  }) => {
    try {
      console.log(`Creating gift for ${data.child_profile_handle}:`, data);
      const response = await api.post(`/api/wall/${data.child_profile_handle}/gift`, data);
      console.log('Gift creation response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating gift via wall:', error);
      throw error;
    }
  },
};

// Enhanced Portfolio API
export const portfolioAPI = {
  // Get comprehensive portfolio for a child
  getChildPortfolio: async (childProfileId: string) => {
    const response = await api.get(`/api/child-profiles/${childProfileId}/portfolio`);
    return response.data;
  },

  // Get gifts with investment status for a child
  getChildGiftsWithInvestments: async (childProfileId: string) => {
    const response = await api.get(`/api/child-profiles/${childProfileId}/gifts-with-investments`);
    return response.data;
  },

  // Create investment from a specific gift
  createInvestmentFromGift: async (giftId: string, data: {
    symbol: string;
    amount_usd: number;
  }) => {
    const response = await api.post(`/api/gifts/${giftId}/invest`, {
      gift_id: giftId,
      ...data
    });
    return response.data;
  },

  // Get dashboard portfolio overview
  getDashboardOverview: async () => {
    const response = await api.get('/api/dashboard/portfolio-overview');
    return response.data;
  },
};
