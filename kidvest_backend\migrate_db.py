import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv
import uuid
from datetime import datetime, timezone

# Load environment variables
load_dotenv()

# Add the current directory to the path so we can import app modules
sys.path.append('.')

# Import models
from app import models
from app.database import Base

# Import models_new with a different Base
import sys
sys.path.insert(0, '.')
from sqlalchemy.ext.declarative import declarative_base
NewBase = declarative_base()

# Import models_new with the new Base
import importlib.util
spec = importlib.util.spec_from_file_location("models_new", "app/models_new.py")
models_new_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(models_new_module)

# Replace Base in models_new with NewBase
models_new_module.Base = NewBase
models_new = models_new_module

# Database connection
DATABASE_URL = os.getenv("DATABASE_URL")
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

def migrate_users():
    """Migrate users from old schema to new schema"""
    print("Migrating users...")

    # Get all users from old schema
    old_users = db.query(models.User).all()

    # Create new users in new schema
    for old_user in old_users:
        # Map user type
        if old_user.user_type == "custodian":
            user_type = models_new.UserType.parent
        elif old_user.user_type == "kid":
            user_type = models_new.UserType.child
        else:
            user_type = old_user.user_type

        # Check if user already exists in new schema
        existing_user = db.query(models_new.User).filter(models_new.User.email == old_user.email).first()
        if existing_user:
            print(f"User {old_user.email} already exists in new schema, skipping...")
            continue

        # Create new user
        new_user = models_new.User(
            id=old_user.id,
            name=old_user.name,
            email=old_user.email,
            user_type=user_type,
            created_at=old_user.created_at,
            is_active=old_user.is_active
        )
        db.add(new_user)

    db.commit()
    print(f"Migrated {len(old_users)} users")

def migrate_kid_profiles():
    """Migrate kid profiles from old schema to new schema"""
    print("Migrating kid profiles...")

    # Get all kid profiles from old schema
    old_profiles = db.query(models.KidProfile).all()

    # Create new child profiles in new schema
    for old_profile in old_profiles:
        # Check if profile already exists in new schema
        existing_profile = db.query(models_new.ChildProfile).filter(models_new.ChildProfile.handle == old_profile.handle).first()
        if existing_profile:
            print(f"Profile {old_profile.handle} already exists in new schema, skipping...")
            continue

        # Create new child profile
        new_profile = models_new.ChildProfile(
            id=old_profile.id,
            parent_id=old_profile.parent_id,
            name=old_profile.name,
            age=old_profile.age,
            handle=old_profile.handle,
            is_public=old_profile.is_public,
            avatar=old_profile.avatar,
            bio=old_profile.bio,
            created_at=old_profile.created_at,
            updated_at=old_profile.updated_at
        )
        db.add(new_profile)

    db.commit()
    print(f"Migrated {len(old_profiles)} kid profiles")

def migrate_gifts():
    """Migrate gifts from old schema to new schema"""
    print("Migrating gifts...")

    # Get all gifts from old schema
    old_gifts = db.query(models.Gift).all()

    # Create new gifts in new schema
    for old_gift in old_gifts:
        # Check if gift already exists in new schema
        existing_gift = db.query(models_new.Gift).filter(models_new.Gift.id == old_gift.id).first()
        if existing_gift:
            print(f"Gift {old_gift.id} already exists in new schema, skipping...")
            continue

        # Use kid_profile_id if available, otherwise try to find the child profile by kid_id
        child_profile_id = old_gift.kid_profile_id
        if not child_profile_id and old_gift.kid_id:
            # Try to find a child profile for this kid
            child_profile = db.query(models_new.ChildProfile).join(models_new.User).filter(models_new.User.id == old_gift.kid_id).first()
            if child_profile:
                child_profile_id = child_profile.id

        # Skip if we can't find a child profile
        if not child_profile_id:
            print(f"Could not find child profile for gift {old_gift.id}, skipping...")
            continue

        # Create new gift
        new_gift = models_new.Gift(
            id=old_gift.id,
            child_profile_id=child_profile_id,
            from_name=old_gift.from_name,
            from_email=old_gift.from_email,
            amount_usd=old_gift.amount_usd,
            message=old_gift.message,
            payment_status=old_gift.payment_status,
            payment_intent_id=old_gift.payment_intent_id,
            checkout_session_id=old_gift.checkout_session_id,
            is_anonymous=old_gift.is_anonymous,
            created_at=old_gift.created_at,
            updated_at=old_gift.updated_at if hasattr(old_gift, 'updated_at') else old_gift.created_at
        )
        db.add(new_gift)

    db.commit()
    print(f"Migrated {len(old_gifts)} gifts")

def migrate_broker_accounts():
    """Migrate broker accounts from old schema to new schema"""
    print("Migrating broker accounts...")

    # Get all broker accounts from old schema
    old_accounts = db.query(models.BrokerAccount).all()

    # Create new broker accounts in new schema
    for old_account in old_accounts:
        # Check if account already exists in new schema
        existing_account = db.query(models_new.BrokerAccount).filter(models_new.BrokerAccount.id == old_account.id).first()
        if existing_account:
            print(f"Broker account {old_account.id} already exists in new schema, skipping...")
            continue

        # Create new broker account
        new_account = models_new.BrokerAccount(
            id=old_account.id,
            user_id=old_account.user_id,
            broker_type=old_account.broker_type,
            external_account_id=old_account.external_account_id,
            status=old_account.status,
            created_at=old_account.created_at,
            updated_at=old_account.created_at  # Use created_at as updated_at since it's not in the old schema
        )
        db.add(new_account)

    db.commit()
    print(f"Migrated {len(old_accounts)} broker accounts")

def create_tables():
    """Create tables for the new schema"""
    print("Creating tables for new schema...")

    # Create tables
    models_new.Base.metadata.create_all(bind=engine)

    print("Tables created")

def main():
    """Main migration function"""
    print("Starting database migration...")

    # Create tables for new schema
    create_tables()

    # Migrate data
    migrate_users()
    migrate_kid_profiles()
    migrate_gifts()
    migrate_broker_accounts()

    print("Migration completed successfully!")

if __name__ == "__main__":
    main()
