# Stripe Webhook Integration Guide

## 🎯 **Issue Analysis**

### **Current Problem:**
- ✅ Gifts are created in database with "pending" status
- ✅ Stripe checkout works and payments are processed
- ❌ Gift status remains "pending" (not updated to "completed")
- ❌ Gifts don't appear in child portfolios
- ❌ Webhook not receiving Stripe events

### **Root Cause:**
The Stripe webhook endpoint needs to be **publicly accessible** for <PERSON><PERSON> to send events. Your local server (`localhost:8000`) is not reachable from <PERSON>e's servers.

## 🛠️ **Solution: Integrated ngrok + Webhook Setup**

### **What I've Created:**
1. **`start-with-webhooks.ps1`** - Automated startup script with ngrok
2. **`test_webhook_integration.py`** - Comprehensive webhook testing
3. **Integrated webhook flow** - Automatic gift status updates

## 🚀 **Step-by-Step Setup**

### **Step 1: Install ngrok (One-time setup)**

1. **Download ngrok:**
   - Go to https://ngrok.com/download
   - Download for Windows
   - Extract to a folder (e.g., `C:\ngrok\`)

2. **Add to PATH:**
   - Add the ngrok folder to your Windows PATH
   - Or place `ngrok.exe` in your project folder

3. **Get ngrok token:**
   - Sign up at https://ngrok.com
   - Go to https://dashboard.ngrok.com/get-started/your-authtoken
   - Copy your authtoken

4. **Configure ngrok:**
   ```bash
   ngrok authtoken YOUR_TOKEN_HERE
   ```

### **Step 2: Start Backend with Webhook Support**

```powershell
# Use the new integrated script
.\start-with-webhooks.ps1
```

**This script will:**
- ✅ Start your FastAPI backend on port 8000
- ✅ Start ngrok tunnel to expose webhook endpoint
- ✅ Display the public webhook URL
- ✅ Save webhook URL to `webhook_url.txt`
- ✅ Show Stripe configuration instructions

### **Step 3: Configure Stripe Webhook**

1. **Go to Stripe Dashboard:**
   - https://dashboard.stripe.com/webhooks

2. **Add Webhook Endpoint:**
   - Click "Add endpoint"
   - Enter URL: `https://YOUR_NGROK_URL.ngrok-free.app/webhook/`
   - Select events:
     - `checkout.session.completed`
     - `checkout.session.expired`

3. **Get Webhook Secret:**
   - After creating the endpoint, click on it
   - Copy the "Signing secret" (starts with `whsec_`)

4. **Update .env File:**
   ```env
   STRIPE_WEBHOOK_SECRET=whsec_your_actual_secret_here
   ```

5. **Restart Backend:**
   - Stop the script (Ctrl+C)
   - Run `.\start-with-webhooks.ps1` again

### **Step 4: Test the Integration**

```powershell
# Test webhook integration
python test_webhook_integration.py
```

**Expected output:**
```
✅ Backend server is running
✅ ngrok tunnel is running
✅ Test gift created successfully!
✅ Webhook processed successfully!
✅ SUCCESS: Gift status updated to completed!
```

## 🧪 **Manual Testing Flow**

### **Complete End-to-End Test:**

1. **Start servers:**
   ```powershell
   .\start-with-webhooks.ps1
   ```

2. **Start React Native app:**
   ```bash
   cd kidvest-app-new
   npm start
   ```

3. **Create a gift:**
   - Open React Native app in browser: `http://localhost:8081`
   - Go to Gifts → Create Gift
   - Fill form and click "Send Gift"

4. **Complete payment:**
   - Use test card: `4242 4242 4242 4242`
   - Complete Stripe checkout

5. **Verify webhook:**
   - Check backend console for webhook logs
   - Verify gift status updated to "completed"
   - Check that gift appears in child portfolio

## 📊 **Expected Webhook Flow**

### **Complete Payment Process:**

```
1. User creates gift → Gift saved with status "pending"
2. User redirected to Stripe → Stripe checkout session
3. User completes payment → Stripe processes payment
4. Stripe sends webhook → POST to your ngrok URL/webhook/
5. Backend receives webhook → Verifies signature
6. Backend updates gift → Status changed to "completed"
7. Gift appears in portfolio → User sees the gift
```

### **Webhook Event Processing:**

```javascript
// Stripe sends this to your webhook:
{
  "type": "checkout.session.completed",
  "data": {
    "object": {
      "id": "cs_test_...",
      "payment_status": "paid",
      "metadata": {
        "gift_id": "your-gift-uuid"
      }
    }
  }
}

// Your backend processes it:
1. Verifies Stripe signature
2. Extracts gift_id from metadata
3. Updates gift status to "completed"
4. Saves payment_intent_id
```

## 🔧 **Troubleshooting**

### **Issue: ngrok not found**
```
❌ ngrok not found
```
**Solution:**
- Install ngrok from https://ngrok.com/download
- Add to PATH or place in project folder
- Run `ngrok authtoken YOUR_TOKEN`

### **Issue: Webhook signature verification failed**
```
❌ Signature verification failed
```
**Solution:**
- Check STRIPE_WEBHOOK_SECRET in .env file
- Make sure it starts with `whsec_`
- Restart backend after updating .env

### **Issue: Gift status not updating**
```
✅ Webhook received but gift status still pending
```
**Solution:**
- Check backend console for webhook processing logs
- Verify gift_id is in webhook metadata
- Check database connection
- Run `python test_webhook_integration.py`

### **Issue: ngrok tunnel disconnected**
```
❌ ngrok tunnel failed
```
**Solution:**
- Restart the `start-with-webhooks.ps1` script
- Update Stripe webhook URL with new ngrok URL
- Check ngrok account limits (free accounts have session limits)

## 📋 **Verification Checklist**

### **Before Testing:**
- [ ] ngrok installed and configured
- [ ] Backend server running on port 8000
- [ ] ngrok tunnel active and public URL available
- [ ] Stripe webhook endpoint configured
- [ ] STRIPE_WEBHOOK_SECRET in .env file
- [ ] React Native app running on port 8081

### **During Testing:**
- [ ] Gift creation works in React Native app
- [ ] Stripe checkout redirects properly
- [ ] Payment completes successfully
- [ ] Backend receives webhook event
- [ ] Gift status updates to "completed"
- [ ] Gift appears in child portfolio

### **Success Indicators:**
- [ ] Backend logs show: "✅ Updated gift {id} to completed"
- [ ] Database shows gift with payment_status = "completed"
- [ ] Child portfolio displays the gift
- [ ] Gift amount reflects in portfolio balance

## 🎉 **Benefits of This Setup**

### **✅ Automated Workflow:**
- **One command** starts everything (backend + ngrok)
- **Automatic webhook URL** generation and display
- **Clear setup instructions** for Stripe configuration
- **Integrated testing** to verify everything works

### **✅ Production Ready:**
- **Proper webhook signature verification**
- **Error handling** for failed payments
- **Database transaction safety**
- **Comprehensive logging** for debugging

### **✅ Developer Friendly:**
- **Easy testing** with automated scripts
- **Clear error messages** for troubleshooting
- **Webhook URL persistence** (saved to file)
- **Step-by-step guidance** for setup

## 🚀 **Quick Start Commands**

### **Start Everything:**
```powershell
# Start backend with webhook support
.\start-with-webhooks.ps1

# In another terminal, start React Native
cd kidvest-app-new
npm start
```

### **Test Webhook Integration:**
```powershell
# Test the webhook setup
python test_webhook_integration.py
```

### **Check Webhook URL:**
```powershell
# Get current webhook URL
Get-Content webhook_url.txt
```

## 📞 **Next Steps**

1. **Run the setup script:** `.\start-with-webhooks.ps1`
2. **Configure Stripe webhook** with the displayed URL
3. **Test the integration** with `python test_webhook_integration.py`
4. **Create a test gift** in React Native app
5. **Complete payment** and verify gift appears in portfolio

**Your webhook integration will be fully functional and gifts will automatically appear in child portfolios after payment completion!** 🎉
