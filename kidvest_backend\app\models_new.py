from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON><PERSON>an, DateT<PERSON>, Enum, <PERSON><PERSON><PERSON>, Float, Text, Integer
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, timezone
from .database import Base
from sqlalchemy.orm import relationship

# Helper function for timezone-aware UTC timestamps
def utc_now():
    return datetime.now(timezone.utc)

import enum

# Define user types
class UserType(str, enum.Enum):
    parent = "parent"  # Changed from "custodian" to "parent" for clarity
    child = "child"    # Changed from "kid" to "child" for consistency

# User model (Parent)
class User(Base):
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    email = Column(String, unique=True, nullable=False)
    user_type = Column(Enum(UserType), nullable=False)
    created_at = Column(DateTime, default=utc_now)
    is_active = Column(Boolean, default=True)
    
    # Profile information
    phone_number = Column(String, nullable=True)
    address = Column(String, nullable=True)
    city = Column(String, nullable=True)
    state = Column(String, nullable=True)
    postal_code = Column(String, nullable=True)
    country = Column(String, nullable=True)
    
    # Relationships
    children = relationship("ChildProfile", back_populates="parent")
    broker_accounts = relationship("BrokerAccount", back_populates="user")

# Child Profile model (replaces KidProfile)
class ChildProfile(Base):
    __tablename__ = "child_profiles"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    parent_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    name = Column(String, nullable=False)
    age = Column(Integer, nullable=True)
    handle = Column(String, unique=True, nullable=False)  # For social sharing
    is_public = Column(Boolean, default=True)
    avatar = Column(String, nullable=True)
    bio = Column(String, nullable=True)
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    # Relationships
    parent = relationship("User", back_populates="children")
    gifts = relationship("Gift", back_populates="child_profile")
    investments = relationship("Investment", back_populates="child_profile")

# Gift model
class Gift(Base):
    __tablename__ = "gifts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    child_profile_id = Column(UUID(as_uuid=True), ForeignKey("child_profiles.id"), nullable=False)
    from_name = Column(String, nullable=True)
    from_email = Column(String, nullable=True)
    amount_usd = Column(Float, nullable=False)
    message = Column(Text, nullable=True)
    payment_status = Column(String, default="pending")  # pending, completed, failed
    payment_intent_id = Column(String, nullable=True)
    checkout_session_id = Column(String, nullable=True)
    is_anonymous = Column(Boolean, default=False)
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    # Relationships
    child_profile = relationship("ChildProfile", back_populates="gifts")
    investments = relationship("Investment", back_populates="gift")

# Investment model (new)
class Investment(Base):
    __tablename__ = "investments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    child_profile_id = Column(UUID(as_uuid=True), ForeignKey("child_profiles.id"), nullable=False)
    gift_id = Column(UUID(as_uuid=True), ForeignKey("gifts.id"), nullable=True)
    amount_usd = Column(Float, nullable=False)
    symbol = Column(String, nullable=False)  # Stock symbol
    shares = Column(Float, nullable=False)
    purchase_price = Column(Float, nullable=False)
    status = Column(String, default="pending")  # pending, completed, failed
    transaction_id = Column(String, nullable=True)  # Broker transaction ID
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    # Relationships
    child_profile = relationship("ChildProfile", back_populates="investments")
    gift = relationship("Gift", back_populates="investments")

# BrokerAccount model
class BrokerAccount(Base):
    __tablename__ = "broker_accounts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    broker_type = Column(String, nullable=False)  # e.g. "alpaca", "drivewealth"
    external_account_id = Column(String, nullable=True)
    status = Column(String, nullable=False, default="pending")  # pending, active, rejected
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    user = relationship("User", back_populates="broker_accounts")
