import React, { useEffect } from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { FontAwesome5 } from '@expo/vector-icons';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';

export default function GiftSuccessScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { status = 'completed', gift_id, child_handle } = useLocalSearchParams();
  
  // Determine if the payment is completed or pending
  const isCompleted = status === 'completed';
  
  // Navigate back to the dashboard
  const handleContinue = () => {
    router.replace('/(tabs)');
  };
  
  // Navigate to the gifts tab
  const handleViewGifts = () => {
    router.replace('/(tabs)/gifts');
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={[
          styles.iconContainer, 
          { 
            backgroundColor: isCompleted 
              ? Colors[colorScheme].primary + '20' 
              : '#FFF8E1'
          }
        ]}>
          <FontAwesome5 
            name={isCompleted ? "check-circle" : "clock"} 
            size={64} 
            color={isCompleted ? Colors[colorScheme].primary : '#FFC107'} 
          />
        </View>
        
        <Text style={styles.title}>
          {isCompleted ? 'Gift Sent Successfully!' : 'Gift Processing'}
        </Text>
        
        <Text style={styles.description}>
          {isCompleted 
            ? 'Your gift has been successfully processed. Thank you for your generosity!'
            : 'Your gift is being processed. The recipient will be notified once the payment is completed.'}
        </Text>
        
        <View style={styles.infoContainer}>
          {isCompleted ? (
            <View style={styles.infoItem}>
              <View style={[styles.infoIconContainer, { backgroundColor: Colors[colorScheme].primary + '20' }]}>
                <FontAwesome5 name="gift" size={20} color={Colors[colorScheme].primary} />
              </View>
              <View style={styles.infoTextContainer}>
                <Text style={styles.infoTitle}>Gift Delivered</Text>
                <Text style={styles.infoDescription}>
                  Your gift has been delivered to the recipient's investment account.
                </Text>
              </View>
            </View>
          ) : (
            <View style={styles.infoItem}>
              <View style={[styles.infoIconContainer, { backgroundColor: '#FFF8E1' }]}>
                <FontAwesome5 name="clock" size={20} color="#FFC107" />
              </View>
              <View style={styles.infoTextContainer}>
                <Text style={styles.infoTitle}>Payment Processing</Text>
                <Text style={styles.infoDescription}>
                  Your payment is being processed. This may take a few moments.
                </Text>
              </View>
            </View>
          )}
          
          <View style={styles.infoItem}>
            <View style={[styles.infoIconContainer, { backgroundColor: Colors[colorScheme].primary + '20' }]}>
              <FontAwesome5 name="envelope" size={20} color={Colors[colorScheme].primary} />
            </View>
            <View style={styles.infoTextContainer}>
              <Text style={styles.infoTitle}>Notification Sent</Text>
              <Text style={styles.infoDescription}>
                The recipient will be notified about your gift.
              </Text>
            </View>
          </View>
        </View>
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.secondaryButton, { borderColor: Colors[colorScheme].primary }]}
            onPress={handleViewGifts}
          >
            <FontAwesome5 name="gift" size={16} color={Colors[colorScheme].primary} />
            <Text style={[styles.secondaryButtonText, { color: Colors[colorScheme].primary }]}>
              View Gifts
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: Colors[colorScheme].primary }]}
            onPress={handleContinue}
          >
            <Text style={styles.primaryButtonText}>Continue to Dashboard</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
  },
  content: {
    padding: 24,
    alignItems: 'center',
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 32,
  },
  infoContainer: {
    width: '100%',
    marginBottom: 32,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  infoIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  infoTextContainer: {
    flex: 1,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  infoDescription: {
    fontSize: 14,
    color: '#666',
  },
  buttonContainer: {
    width: '100%',
  },
  secondaryButton: {
    flexDirection: 'row',
    height: 48,
    borderRadius: 8,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  secondaryButtonText: {
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  primaryButton: {
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
});
