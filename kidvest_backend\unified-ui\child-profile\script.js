// API Base URL
const API_BASE_URL = 'http://localhost:8080/api';

// For debugging
console.log('Child Profile UI script loaded');

// DOM Elements
const profilesContainer = document.getElementById('profiles-container');
const loadingProfiles = document.getElementById('loading-profiles');
const noProfiles = document.getElementById('no-profiles');
const apiResponse = document.getElementById('api-response');
const userNameElement = document.getElementById('user-name');
const logoutBtn = document.getElementById('logout-btn');
const addProfileForm = document.getElementById('add-profile-form');
const saveProfileBtn = document.getElementById('save-profile-btn');
const editProfileForm = document.getElementById('edit-profile-form');
const updateProfileBtn = document.getElementById('update-profile-btn');
const confirmDeleteBtn = document.getElementById('confirm-delete-btn');

// Bootstrap Modals
const addProfileModal = new bootstrap.Modal(document.getElementById('addProfileModal'));
const editProfileModal = new bootstrap.Modal(document.getElementById('editProfileModal'));
const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));

/**
 * Initialize the child profile UI
 */
function initChildProfileUI() {
    console.log('Child Profile UI: Initializing...');

    // Require authentication
    if (!KidVest.StateManager.requireAuth()) {
        return; // This will redirect to auth if not authenticated
    }

    // Show the API response container for debugging
    document.getElementById('api-response').style.display = 'block';

    // Load user profile
    loadUserProfile();

    // Load child profiles
    loadChildProfiles();

    // Set up event listeners
    setupEventListeners();
}

/**
 * Set up event listeners
 */
function setupEventListeners() {
    // Logout button
    logoutBtn.addEventListener('click', () => {
        console.log('Logout button clicked');
        KidVest.StateManager.clearToken();
        KidVest.StateManager.navigateTo('auth');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Child Profile UI: DOM fully loaded');
    initChildProfileUI();
});

// Show API Response
function showResponse(response, isError = false) {
    apiResponse.style.display = 'block';
    apiResponse.style.backgroundColor = isError ? '#f8d7da' : '#d4edda';
    apiResponse.style.color = isError ? '#721c24' : '#155724';

    // Format the data
    const formattedData = typeof response === 'object' ? JSON.stringify(response, null, 2) : response;

    // Add timestamp
    const timestamp = new Date().toLocaleTimeString();
    const message = `[${timestamp}] ${formattedData}`;

    // Append to existing content instead of replacing
    apiResponse.innerHTML = message + '<hr>' + apiResponse.innerHTML;
}

// Clear debug information
document.getElementById('clear-debug-btn').addEventListener('click', function() {
    apiResponse.innerHTML = 'Debug information cleared.';
    apiResponse.style.backgroundColor = '#f8f9fa';
    apiResponse.style.color = '#212529';
});

/**
 * Load the user profile
 */
async function loadUserProfile() {
    try {
        console.log('Loading user profile...');

        const response = await KidVest.StateManager.apiRequest('users/me', {
            method: 'GET'
        });

        if (!response) {
            console.error('Failed to get response from API');
            return;
        }

        if (response.ok) {
            const userData = await response.json();
            console.log('User profile loaded:', userData);
            userNameElement.textContent = `Welcome, ${userData.name}`;
        } else {
            console.error('Failed to load user profile, status:', response.status);
        }
    } catch (error) {
        console.error('Error loading user profile:', error);
    }
}

/**
 * Load child profiles
 */
async function loadChildProfiles() {
    try {
        console.log('Loading child profiles...');

        // Show loading message
        showResponse('Loading child profiles...', false);

        // Make sure the loading indicator is visible and no-profiles is hidden
        loadingProfiles.classList.remove('d-none');
        noProfiles.classList.add('d-none');

        // Fetch profiles using the state manager
        const response = await KidVest.StateManager.apiRequest('children/', {
            method: 'GET'
        });

        // Hide loading indicator
        loadingProfiles.classList.add('d-none');

        if (!response) {
            console.error('Failed to get response from API');
            noProfiles.classList.remove('d-none');
            showResponse('Failed to load profiles. Please try again.', true);
            return;
        }

        if (response.ok) {
            const data = await response.json();
            console.log('Profiles loaded:', data);

            if (data.length === 0) {
                // Show no profiles message
                noProfiles.classList.remove('d-none');
                showResponse('No child profiles found. Create your first profile!', false);
            } else {
                // Render profiles
                renderProfiles(data);
                showResponse(`Found ${data.length} child profiles.`, false);
            }
        } else {
            console.error('Failed to load profiles, status:', response.status);
            noProfiles.classList.remove('d-none');
            showResponse('Failed to load profiles: ' + response.status, true);
        }
    } catch (error) {
        console.error('Error loading profiles:', error);
        loadingProfiles.classList.add('d-none');
        noProfiles.classList.remove('d-none');
        showResponse(`Error: ${error.message}`, true);
    }
}

// Render Profiles
function renderProfiles(profiles) {
    profilesContainer.innerHTML = '';

    profiles.forEach(profile => {
        const profileCard = document.createElement('div');
        profileCard.className = 'col-md-4 mb-4';
        profileCard.innerHTML = `
            <div class="profile-card">
                <div class="profile-header">
                    <h5 class="mb-0">${profile.name}</h5>
                </div>
                <div class="profile-body">
                    <div class="profile-avatar">
                        ${profile.name.charAt(0).toUpperCase()}
                    </div>
                    <p class="text-center mb-1"><strong>Handle:</strong> ${profile.handle}</p>
                    <p class="text-center mb-1"><strong>Age:</strong> ${calculateAge(profile.dob)}</p>
                    <p class="text-center mb-3"><strong>Status:</strong> ${profile.is_public ? 'Public' : 'Private'}</p>
                    <div class="profile-actions">
                        <button class="btn btn-sm btn-outline-primary view-wall-btn" data-handle="${profile.handle}">
                            <i class="bi bi-gift"></i> Gift Wall
                        </button>
                        <div>
                            <button class="btn btn-sm btn-outline-secondary edit-profile-btn" data-profile='${JSON.stringify(profile)}'>
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger delete-profile-btn" data-id="${profile.id}" data-name="${profile.name}">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        profilesContainer.appendChild(profileCard);
    });

    // Add event listeners to buttons
    document.querySelectorAll('.view-wall-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const handle = btn.getAttribute('data-handle');
            window.open(`/wall/${handle}`, '_blank');
        });
    });

    document.querySelectorAll('.edit-profile-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const profile = JSON.parse(btn.getAttribute('data-profile'));
            openEditModal(profile);
        });
    });

    document.querySelectorAll('.delete-profile-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const id = btn.getAttribute('data-id');
            const name = btn.getAttribute('data-name');
            openDeleteModal(id, name);
        });
    });
}

// Calculate Age
function calculateAge(dob) {
    if (!dob) return 'N/A';

    const birthDate = new Date(dob);

    // Check if date is valid
    if (isNaN(birthDate.getTime())) return 'N/A';

    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }

    return age;
}

// Open Edit Modal
function openEditModal(profile) {
    document.getElementById('edit-profile-id').value = profile.id;
    document.getElementById('edit-name').value = profile.name;
    document.getElementById('edit-handle').value = profile.handle;
    document.getElementById('edit-dob').value = profile.dob;
    document.getElementById('edit-bio').value = profile.bio || '';
    document.getElementById('edit-is_public').checked = profile.is_public;

    editProfileModal.show();
}

// Open Delete Modal
function openDeleteModal(id, name) {
    document.getElementById('delete-profile-id').value = id;
    document.getElementById('delete-profile-name').textContent = name;

    deleteConfirmModal.show();
}

// Save Profile
saveProfileBtn.addEventListener('click', async () => {
    const name = document.getElementById('name').value;
    const handle = document.getElementById('handle').value;
    const dob = document.getElementById('dob').value;
    const bio = document.getElementById('bio').value;
    const isPublic = document.getElementById('is_public').checked;

    // Validate handle format
    const handleRegex = /^[a-zA-Z0-9_]+$/;
    if (!handleRegex.test(handle)) {
        alert('Handle can only contain letters, numbers, and underscores. No spaces or special characters.');
        return;
    }

    try {
        // Calculate age from DOB
        const age = calculateAge(dob);

        console.log('Creating child profile with data:', { name, handle, age, bio, isPublic, dob });

        // Show that we're making the API call
        showResponse('Making API call to create child profile...', false);

        const requestBody = {
            name,
            handle,
            age: typeof age === 'number' ? age : null,
            bio,
            is_public: isPublic,
            dob: dob
        };

        // Use the state manager to make the API call
        const response = await KidVest.StateManager.apiRequest('children/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response) {
            console.error('Failed to get response from API');
            showResponse('Failed to create profile. Please try again.', true);
            return;
        }

        if (response.ok) {
            const data = await response.json();
            console.log('Profile created successfully:', data);

            showResponse({
                message: 'Child profile created successfully!',
                profile: data
            });

            // Close modal and reset form
            addProfileModal.hide();
            addProfileForm.reset();

            // Reload profiles
            loadChildProfiles();
        } else {
            // Try to parse the error response
            try {
                const errorText = await response.text();
                const errorData = JSON.parse(errorText);
                console.error('Error creating profile:', errorData);

                showResponse({
                    error: 'Failed to create profile',
                    details: errorData,
                    status: response.status
                }, true);
            } catch (e) {
                console.error('Error parsing error response:', e);
                showResponse(`Error: Failed to create profile (${response.status})`, true);
            }
        }
    } catch (error) {
        console.error('Exception creating profile:', error);
        showResponse(`Error: ${error.message}`, true);
    }
});

// Update Profile
updateProfileBtn.addEventListener('click', async () => {
    const id = document.getElementById('edit-profile-id').value;
    const name = document.getElementById('edit-name').value;
    const handle = document.getElementById('edit-handle').value;
    const dob = document.getElementById('edit-dob').value;
    const bio = document.getElementById('edit-bio').value;
    const isPublic = document.getElementById('edit-is_public').checked;

    // Validate handle format
    const handleRegex = /^[a-zA-Z0-9_]+$/;
    if (!handleRegex.test(handle)) {
        alert('Handle can only contain letters, numbers, and underscores. No spaces or special characters.');
        return;
    }

    try {
        // Calculate age from DOB
        const age = calculateAge(dob);

        console.log('Updating child profile with data:', { id, name, handle, age, bio, isPublic, dob });

        // Show that we're making the API call
        showResponse('Making API call to update child profile...', false);

        const requestBody = {
            name,
            handle,
            age: typeof age === 'number' ? age : null,
            bio,
            is_public: isPublic,
            dob: dob
        };

        // Use the state manager to make the API call
        const response = await KidVest.StateManager.apiRequest(`children/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response) {
            console.error('Failed to get response from API');
            showResponse('Failed to update profile. Please try again.', true);
            return;
        }

        if (response.ok) {
            const data = await response.json();
            console.log('Profile updated successfully:', data);

            showResponse({
                message: 'Child profile updated successfully!',
                profile: data
            });

            // Close modal
            editProfileModal.hide();

            // Reload profiles
            loadChildProfiles();
        } else {
            // Try to parse the error response
            try {
                const errorText = await response.text();
                const errorData = JSON.parse(errorText);
                console.error('Error updating profile:', errorData);

                showResponse({
                    error: 'Failed to update profile',
                    details: errorData,
                    status: response.status
                }, true);
            } catch (e) {
                console.error('Error parsing error response:', e);
                showResponse(`Error: Failed to update profile (${response.status})`, true);
            }
        }
    } catch (error) {
        console.error('Exception updating profile:', error);
        showResponse(`Error: ${error.message}`, true);
    }
});

// Delete Profile
confirmDeleteBtn.addEventListener('click', async () => {
    const id = document.getElementById('delete-profile-id').value;
    const name = document.getElementById('delete-profile-name').textContent;

    try {
        console.log(`Deleting child profile: ${name} (ID: ${id})`);

        // Show that we're making the API call
        showResponse(`Deleting child profile: ${name}...`, false);

        // Use the state manager to make the API call
        const response = await KidVest.StateManager.apiRequest(`children/${id}`, {
            method: 'DELETE'
        });

        if (!response) {
            console.error('Failed to get response from API');
            showResponse('Failed to delete profile. Please try again.', true);
            return;
        }

        if (response.ok) {
            console.log('Profile deleted successfully');

            showResponse({
                message: 'Child profile deleted successfully!'
            });

            // Close modal
            deleteConfirmModal.hide();

            // Reload profiles
            loadChildProfiles();
        } else {
            // Try to parse the error response
            try {
                const errorText = await response.text();
                const errorData = JSON.parse(errorText);
                console.error('Error deleting profile:', errorData);

                showResponse({
                    error: 'Failed to delete profile',
                    details: errorData,
                    status: response.status
                }, true);
            } catch (e) {
                console.error('Error parsing error response:', e);
                showResponse(`Error: Failed to delete profile (${response.status})`, true);
            }
        }
    } catch (error) {
        console.error('Exception deleting profile:', error);
        showResponse(`Error: ${error.message}`, true);
    }
});
