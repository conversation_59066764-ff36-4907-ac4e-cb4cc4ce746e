import React, { useState, useEffect } from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, RefreshControl } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

import { Text, View } from '@/components/Themed';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';
import { useAuth } from '../../context/AuthContext';
import { portfolioAPI } from '../../services/api';

export default function PortfolioOverviewScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { user } = useAuth();
  
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [overview, setOverview] = useState(null);
  const [error, setError] = useState(null);

  const loadOverview = async (showRefresh = false) => {
    try {
      if (showRefresh) setIsRefreshing(true);
      else setIsLoading(true);
      
      const overviewData = await portfolioAPI.getDashboardOverview();
      setOverview(overviewData);
      setError(null);
    } catch (error) {
      console.error('Error loading portfolio overview:', error);
      setError('Failed to load portfolio overview');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    loadOverview();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const handleChildPortfolio = (childId: string) => {
    router.push(`/portfolio/${childId}`);
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors[colorScheme].primary} />
        <Text style={styles.loadingText}>Loading portfolio overview...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <FontAwesome5 name="exclamation-triangle" size={48} color="#F44336" />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity 
          style={[styles.retryButton, { backgroundColor: Colors[colorScheme].primary }]}
          onPress={() => loadOverview()}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!overview) {
    return (
      <View style={styles.errorContainer}>
        <FontAwesome5 name="chart-pie" size={48} color="#999" />
        <Text style={styles.errorText}>No portfolio data available</Text>
      </View>
    );
  }

  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={() => loadOverview(true)}
          colors={[Colors[colorScheme].primary]}
        />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <FontAwesome5 name="arrow-left" size={20} color={Colors[colorScheme].text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Family Portfolio</Text>
        <View style={{ width: 40 }} />
      </View>

      {/* Overall Summary */}
      <View style={[styles.summaryCard, { backgroundColor: Colors[colorScheme].cardBackground }]}>
        <Text style={styles.summaryTitle}>Family Overview</Text>
        
        <View style={styles.summaryGrid}>
          <View style={styles.summaryItem}>
            <FontAwesome5 name="users" size={24} color={Colors[colorScheme].primary} />
            <Text style={styles.summaryNumber}>{overview.overall_summary.total_children}</Text>
            <Text style={styles.summaryLabel}>Children</Text>
          </View>
          
          <View style={styles.summaryItem}>
            <FontAwesome5 name="gift" size={24} color="#4CAF50" />
            <Text style={styles.summaryNumber}>
              {formatCurrency(overview.overall_summary.total_gifts_received)}
            </Text>
            <Text style={styles.summaryLabel}>Total Gifts</Text>
          </View>
          
          <View style={styles.summaryItem}>
            <FontAwesome5 name="chart-line" size={24} color="#FF9800" />
            <Text style={styles.summaryNumber}>
              {formatCurrency(overview.overall_summary.total_invested)}
            </Text>
            <Text style={styles.summaryLabel}>Invested</Text>
          </View>
          
          <View style={styles.summaryItem}>
            <FontAwesome5 name="wallet" size={24} color="#2196F3" />
            <Text style={styles.summaryNumber}>
              {formatCurrency(overview.overall_summary.total_available_balance)}
            </Text>
            <Text style={styles.summaryLabel}>Available</Text>
          </View>
        </View>
      </View>

      {/* Children Portfolios */}
      <View style={[styles.section, { backgroundColor: Colors[colorScheme].cardBackground }]}>
        <Text style={styles.sectionTitle}>Children's Portfolios</Text>
        
        {overview.children_portfolios.length === 0 ? (
          <View style={styles.emptyState}>
            <FontAwesome5 name="child" size={48} color="#999" />
            <Text style={styles.emptyStateText}>No children portfolios yet</Text>
            <Text style={styles.emptyStateSubtext}>
              Create child profiles and receive gifts to start building portfolios
            </Text>
            <TouchableOpacity
              style={[styles.addChildButton, { backgroundColor: Colors[colorScheme].primary }]}
              onPress={() => router.push('/onboarding/child-profile')}
            >
              <FontAwesome5 name="plus" size={16} color="white" />
              <Text style={styles.addChildButtonText}>Add Child Profile</Text>
            </TouchableOpacity>
          </View>
        ) : (
          overview.children_portfolios.map((child, index) => (
            <TouchableOpacity
              key={index}
              style={styles.childPortfolioCard}
              onPress={() => handleChildPortfolio(child.child_profile_id)}
            >
              <View style={styles.childPortfolioHeader}>
                <View style={styles.childInfo}>
                  <Text style={styles.childName}>{child.child_name}</Text>
                  <Text style={styles.childStats}>
                    {child.total_gifts_count} gifts • {child.total_investments_count} investments
                  </Text>
                </View>
                <FontAwesome5 name="chevron-right" size={16} color="#999" />
              </View>
              
              <View style={styles.childPortfolioStats}>
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>Gifts Received</Text>
                  <Text style={[styles.statValue, { color: '#4CAF50' }]}>
                    {formatCurrency(child.total_gifts_received)}
                  </Text>
                </View>
                
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>Invested</Text>
                  <Text style={styles.statValue}>
                    {formatCurrency(child.total_invested)}
                  </Text>
                </View>
                
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>Available</Text>
                  <Text style={[styles.statValue, { color: Colors[colorScheme].primary }]}>
                    {formatCurrency(child.available_balance)}
                  </Text>
                </View>
              </View>
              
              {/* Progress Bar */}
              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <View 
                    style={[
                      styles.progressFill, 
                      { 
                        width: `${child.total_gifts_received > 0 ? (child.total_invested / child.total_gifts_received) * 100 : 0}%`,
                        backgroundColor: Colors[colorScheme].primary 
                      }
                    ]} 
                  />
                </View>
                <Text style={styles.progressText}>
                  {child.total_gifts_received > 0 
                    ? `${((child.total_invested / child.total_gifts_received) * 100).toFixed(1)}% invested`
                    : 'No gifts yet'
                  }
                </Text>
              </View>
            </TouchableOpacity>
          ))
        )}
      </View>

      {/* Quick Actions */}
      <View style={[styles.section, { backgroundColor: Colors[colorScheme].cardBackground }]}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => router.push('/onboarding/child-profile')}
          >
            <FontAwesome5 name="user-plus" size={24} color={Colors[colorScheme].primary} />
            <Text style={styles.quickActionText}>Add Child</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => router.push('/(tabs)/gifts')}
          >
            <FontAwesome5 name="share-alt" size={24} color={Colors[colorScheme].primary} />
            <Text style={styles.quickActionText}>Share Gift Wall</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => router.push('/(tabs)/profiles')}
          >
            <FontAwesome5 name="cog" size={24} color={Colors[colorScheme].primary} />
            <Text style={styles.quickActionText}>Manage Profiles</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginVertical: 16,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 60,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  summaryCard: {
    margin: 16,
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  summaryItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 16,
  },
  summaryNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  section: {
    margin: 16,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  addChildButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  addChildButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  childPortfolioCard: {
    marginBottom: 16,
    padding: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eee',
  },
  childPortfolioHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  childInfo: {
    flex: 1,
  },
  childName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  childStats: {
    fontSize: 12,
    color: '#666',
  },
  childPortfolioStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 10,
    color: '#666',
    marginBottom: 2,
  },
  statValue: {
    fontSize: 12,
    fontWeight: '600',
  },
  progressContainer: {
    marginTop: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#eee',
    borderRadius: 2,
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 10,
    color: '#666',
    textAlign: 'center',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  quickActionButton: {
    alignItems: 'center',
    padding: 16,
  },
  quickActionText: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
});
