# Multi-Step Onboarding Process

This document describes the multi-step onboarding process for creating Alpaca brokerage accounts.

## Overview

The onboarding process is divided into 4 steps:

1. **Basic Account Creation**: Collect basic user information
2. **Identity Verification**: Collect identity and address information
3. **Financial Profile**: Collect financial information
4. **Disclosures & Agreements**: Collect regulatory disclosures and agreement acceptances

After completing all 4 steps, the data is submitted to the Alpaca Broker API to create a brokerage account.

## API Endpoints

### Step 1: Basic Account Creation

```
POST /api/onboarding/step1
```

**Request Body:**
```json
{
  "full_name": "<PERSON>",
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Step 1 completed successfully",
  "session_token": "99bb2c70-3b7f-43ec-8e3c-de57df82f676",
  "current_step": 1,
  "next_step": 2
}
```

### Step 2: Identity Verification

```
POST /api/onboarding/step2?session_token={session_token}
```

**Request Body:**
```json
{
  "dob": "1992-03-15",
  "phone_number": "************",
  "street_address": "789 Pine Street",
  "city": "Boston",
  "state": "MA",
  "postal_code": "02108",
  "country": "USA",
  "ssn": "***********"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Step 2 completed successfully",
  "session_token": "99bb2c70-3b7f-43ec-8e3c-de57df82f676",
  "current_step": 2,
  "next_step": 3
}
```

### Step 3: Financial Profile

```
POST /api/onboarding/step3?session_token={session_token}
```

**Request Body:**
```json
{
  "employment_status": "EMPLOYED",
  "income_range": "50k_100k",
  "net_worth_range": "50k_100k",
  "funding_source": "employment_income",
  "investment_experience": "some",
  "risk_tolerance": "moderate"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Step 3 completed successfully",
  "session_token": "99bb2c70-3b7f-43ec-8e3c-de57df82f676",
  "current_step": 3,
  "next_step": 4
}
```

### Step 4: Disclosures & Agreements

```
POST /api/onboarding/step4?session_token={session_token}
```

**Request Body:**
```json
{
  "is_control_person": false,
  "is_affiliated_exchange_or_finra": false,
  "is_politically_exposed": false,
  "immediate_family_exposed": false,
  "customer_agreement_accepted": true,
  "margin_agreement_accepted": true,
  "account_agreement_accepted": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Step 4 completed successfully",
  "session_token": "99bb2c70-3b7f-43ec-8e3c-de57df82f676",
  "current_step": 4,
  "next_step": 5
}
```

### Final Submission

```
POST /api/onboarding/submit?session_token={session_token}
```

**Response:**
```json
{
  "success": true,
  "message": "Alpaca onboarding submitted",
  "account_id": "09e897ac-a5d0-4a4b-93f9-aadb5b412891",
  "status": "SUBMITTED"
}
```

## Error Handling

All endpoints return a consistent error format:

```json
{
  "success": false,
  "message": "Error message",
  "details": "Detailed error information (optional)"
}
```

## Session Management

- Sessions are created in Step 1 and expire after 24 hours
- Each step must be completed in order
- The session token must be passed to each subsequent step
- All data is stored securely in the database

## Implementation Details

The multi-step onboarding process is implemented using:

1. **Session Tokens**: To track the user's progress through the steps
2. **Database Storage**: To store the data collected at each step
3. **Smart Defaults**: To map simplified inputs to Alpaca's required format
4. **Error Handling**: To provide clear feedback on any issues

## Testing

Use the provided test scripts to test the onboarding process:

- `test_step1.py`: Test Step 1
- `test_step2.py`: Test Step 2
- `debug_onboarding.py`: Test the entire process

## Swagger UI Testing

You can also test the API using Swagger UI at `/docs`. Sample payloads are provided in the `swagger_ui_payloads` directory.
