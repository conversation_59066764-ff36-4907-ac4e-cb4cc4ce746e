#!/usr/bin/env python3

"""
Comprehensive diagnostic script for investment issues
"""

import sys
import requests
import json
from datetime import datetime, timedelta
from sqlalchemy import text
from app.database import SessionLocal

BASE_URL = "http://localhost:8000"
AUSTIN_EMAIL = "<EMAIL>"

def check_database_investments():
    """Check all investments in database"""
    print("💾 Checking investments in database...")
    
    db = SessionLocal()
    try:
        # Check all investments
        result = db.execute(text("""
            SELECT i.id, i.child_profile_id, i.parent_id, i.amount_usd, i.symbol, 
                   i.shares, i.purchase_price, i.status, i.transaction_id, 
                   i.executed_at, i.created_at,
                   cp.name as child_name, u.name as parent_name
            FROM investments i
            JOIN child_profiles cp ON i.child_profile_id = cp.id
            JOIN users u ON i.parent_id = u.id
            ORDER BY i.created_at DESC
            LIMIT 10
        """))
        
        investments = result.fetchall()
        
        if not investments:
            print("   ℹ️ No investments found in database")
            return []
        
        print(f"   ✅ Found {len(investments)} investments:")
        for inv in investments:
            print(f"   - ID: {inv[0]}")
            print(f"     Parent: {inv[12]} | Child: {inv[11]}")
            print(f"     Amount: ${inv[3]} {inv[4]} | Shares: {inv[5]}")
            print(f"     Status: {inv[7]} | Transaction: {inv[8]}")
            print(f"     Created: {inv[10]} | Executed: {inv[9]}")
            print()
        
        return investments
        
    except Exception as e:
        print(f"   ❌ Database error: {str(e)}")
        return []
    finally:
        db.close()

def check_austin_gifts_and_balances():
    """Check Austin's children's gift balances"""
    print("\n💰 Checking Austin's children's gift balances...")
    
    db = SessionLocal()
    try:
        # Find Austin's children and their gifts
        result = db.execute(text("""
            SELECT cp.id, cp.name, cp.handle,
                   COALESCE(SUM(CASE WHEN g.payment_status = 'completed' THEN g.amount_usd ELSE 0 END), 0) as total_gifts,
                   COALESCE(SUM(CASE WHEN i.status = 'completed' THEN i.amount_usd ELSE 0 END), 0) as total_invested,
                   COALESCE(SUM(CASE WHEN g.payment_status = 'completed' THEN g.amount_usd ELSE 0 END), 0) - 
                   COALESCE(SUM(CASE WHEN i.status = 'completed' THEN i.amount_usd ELSE 0 END), 0) as available_balance
            FROM child_profiles cp
            JOIN users u ON cp.parent_id = u.id
            LEFT JOIN gifts g ON cp.id = g.child_profile_id
            LEFT JOIN investments i ON cp.id = i.child_profile_id
            WHERE u.email = :email
            GROUP BY cp.id, cp.name, cp.handle
        """), {"email": AUSTIN_EMAIL})
        
        children = result.fetchall()
        
        if not children:
            print("   ❌ No children found for Austin")
            return []
        
        print(f"   ✅ Found {len(children)} children:")
        for child in children:
            print(f"   - {child[1]} (@{child[2]})")
            print(f"     Total Gifts: ${child[3]}")
            print(f"     Total Invested: ${child[4]}")
            print(f"     Available Balance: ${child[5]}")
            print()
        
        return children
        
    except Exception as e:
        print(f"   ❌ Database error: {str(e)}")
        return []
    finally:
        db.close()

def check_recent_gifts():
    """Check recent gifts created"""
    print("\n🎁 Checking recent gifts...")
    
    db = SessionLocal()
    try:
        # Check recent gifts
        result = db.execute(text("""
            SELECT g.id, g.child_profile_id, g.amount_usd, g.from_name, 
                   g.payment_status, g.created_at, cp.name as child_name
            FROM gifts g
            JOIN child_profiles cp ON g.child_profile_id = cp.id
            WHERE g.created_at > NOW() - INTERVAL '1 day'
            ORDER BY g.created_at DESC
        """))
        
        gifts = result.fetchall()
        
        if not gifts:
            print("   ℹ️ No recent gifts found")
            return []
        
        print(f"   ✅ Found {len(gifts)} recent gifts:")
        for gift in gifts:
            print(f"   - ${gift[2]} for {gift[6]} from {gift[3]}")
            print(f"     Status: {gift[4]} | Created: {gift[5]}")
            print()
        
        return gifts
        
    except Exception as e:
        print(f"   ❌ Database error: {str(e)}")
        return []
    finally:
        db.close()

def test_investment_api_with_mock_auth():
    """Test investment API with detailed logging"""
    print("\n🧪 Testing investment API with detailed logging...")
    
    # Get Austin's child ID
    db = SessionLocal()
    try:
        result = db.execute(text("""
            SELECT cp.id, cp.name
            FROM child_profiles cp
            JOIN users u ON cp.parent_id = u.id
            WHERE u.email = :email
            LIMIT 1
        """), {"email": AUSTIN_EMAIL})
        
        child_row = result.fetchone()
        if not child_row:
            print("   ❌ No child found for Austin")
            return False
        
        child_id = str(child_row[0])
        child_name = child_row[1]
        
        print(f"   Testing with child: {child_name} (ID: {child_id})")
        
    finally:
        db.close()
    
    # Test investment request
    investment_request = {
        "child_profile_id": child_id,
        "stock_symbol": "SPY",
        "amount_usd": 10.0
    }
    
    print(f"   Request payload: {json.dumps(investment_request, indent=2)}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/invest/",
            json=investment_request,
            timeout=30
        )
        
        print(f"   Response Status: {response.status_code}")
        print(f"   Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"   Response Body: {json.dumps(response_data, indent=2)}")
        except:
            print(f"   Response Text: {response.text}")
        
        return response.status_code in [200, 201]
        
    except Exception as e:
        print(f"   ❌ API test failed: {str(e)}")
        return False

def check_backend_logs():
    """Check if backend is logging properly"""
    print("\n📋 Backend logging check...")
    print("   💡 Check your backend terminal for these logs:")
    print("   - 🚀 Starting investment...")
    print("   - ✅ Child profile validated...")
    print("   - ✅ Broker account found...")
    print("   - 📡 Placing order...")
    print("   - ✅ Investment created...")
    print("   - ❌ Any error messages")

def check_alpaca_market_status():
    """Check Alpaca market status"""
    print("\n📈 Checking Alpaca market status...")
    
    try:
        from app.alpaca_service import get_alpaca_market_data
        
        # Test market data
        market_data, error = get_alpaca_market_data("SPY")
        if error:
            print(f"   ⚠️ Market data error: {error}")
            if "403" in str(error) or "4033" in str(error):
                print("   💡 This suggests market is closed or rate limited")
            return False
        else:
            print(f"   ✅ Market data available: ${market_data.get('last_price', 'N/A')}")
            return True
            
    except Exception as e:
        print(f"   ❌ Market check failed: {str(e)}")
        return False

def check_frontend_api_calls():
    """Check if frontend is making correct API calls"""
    print("\n📱 Frontend API call diagnostics...")
    print("   💡 Check browser developer tools:")
    print("   1. Open browser dev tools (F12)")
    print("   2. Go to Network tab")
    print("   3. Try investment from frontend")
    print("   4. Look for POST request to /api/invest/")
    print("   5. Check request payload and response")
    print("   6. Look for any CORS or authentication errors")

def provide_troubleshooting_steps():
    """Provide step-by-step troubleshooting"""
    print("\n" + "=" * 60)
    print("🔧 TROUBLESHOOTING STEPS")
    print("=" * 60)
    
    print("\n1. 🔍 CHECK DATABASE RECORDING:")
    print("   - Investment should be created regardless of market hours")
    print("   - Check if investment appears in database immediately")
    print("   - Status might be 'pending' if market closed")
    
    print("\n2. 🚀 CHECK BACKEND LOGS:")
    print("   - Start backend with: uvicorn app.main:app --reload")
    print("   - Watch terminal for investment logs")
    print("   - Look for error messages or exceptions")
    
    print("\n3. 📱 CHECK FRONTEND INTEGRATION:")
    print("   - Open browser dev tools (F12)")
    print("   - Check Network tab for API calls")
    print("   - Verify authentication tokens")
    print("   - Check for JavaScript errors in Console")
    
    print("\n4. 🕐 MARKET HOURS CONSIDERATION:")
    print("   - US market hours: 9:30 AM - 4:00 PM ET")
    print("   - Orders placed outside hours are queued")
    print("   - Database should still record the investment")
    
    print("\n5. 🧪 TEST WITH CURL:")
    print("   - Test API directly with curl or Postman")
    print("   - Bypass frontend to isolate issues")
    print("   - Check authentication and payload format")

def main():
    """Main diagnostic function"""
    print("🔍 Investment Issues Diagnostic")
    print("=" * 50)
    print(f"⏰ Diagnostic started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all diagnostics
    print("\n🔍 RUNNING COMPREHENSIVE DIAGNOSTICS...")
    
    investments = check_database_investments()
    children = check_austin_gifts_and_balances()
    gifts = check_recent_gifts()
    
    api_working = test_investment_api_with_mock_auth()
    market_working = check_alpaca_market_status()
    
    check_backend_logs()
    check_frontend_api_calls()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    print(f"\n✅ FOUND IN DATABASE:")
    print(f"   - Investments: {len(investments)}")
    print(f"   - Austin's children: {len(children)}")
    print(f"   - Recent gifts: {len(gifts)}")
    
    print(f"\n🔧 API STATUS:")
    print(f"   - Investment API: {'✅ Working' if api_working else '❌ Issues'}")
    print(f"   - Market data: {'✅ Available' if market_working else '⚠️ Limited'}")
    
    if len(investments) == 0:
        print(f"\n❌ ISSUE IDENTIFIED: No investments in database")
        print(f"   This suggests the investment creation is failing")
        print(f"   Check backend logs for error messages")
    
    if not market_working:
        print(f"\n⚠️ MARKET ISSUE: Market data unavailable")
        print(f"   This might prevent order execution")
        print(f"   But database recording should still work")
    
    provide_troubleshooting_steps()
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ Diagnostic interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Diagnostic failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
