#!/usr/bin/env python3

"""
Script to enable trading for existing broker accounts
"""

import sys
from sqlalchemy import text
from app.database import SessionLocal

def update_account_status_to_active():
    """Update SUBMITTED accounts to ACTIVE for testing"""
    print("🔄 Updating account status to ACTIVE...")
    
    db = SessionLocal()
    try:
        # Update SUBMITTED accounts to ACTIVE
        result = db.execute(text("""
            UPDATE broker_accounts 
            SET status = 'active' 
            WHERE status = 'SUBMITTED'
        """))
        
        updated_count = result.rowcount
        db.commit()
        
        print(f"   ✅ Updated {updated_count} accounts from SUBMITTED to active")
        return updated_count > 0
        
    except Exception as e:
        print(f"   ❌ Error updating account status: {str(e)}")
        db.rollback()
        return False
    finally:
        db.close()

def enable_trading_for_all_active_accounts():
    """Enable trading for all active accounts"""
    print("\n🔧 Enabling trading for active accounts...")
    
    db = SessionLocal()
    try:
        # Enable trading for active accounts
        result = db.execute(text("""
            UPDATE broker_accounts 
            SET trading_enabled = TRUE 
            WHERE status = 'active' AND (trading_enabled IS NULL OR trading_enabled = FALSE)
        """))
        
        updated_count = result.rowcount
        db.commit()
        
        print(f"   ✅ Enabled trading for {updated_count} accounts")
        return updated_count > 0
        
    except Exception as e:
        print(f"   ❌ Error enabling trading: {str(e)}")
        db.rollback()
        return False
    finally:
        db.close()

def show_account_status():
    """Show current account status"""
    print("\n📊 Current account status:")
    
    db = SessionLocal()
    try:
        result = db.execute(text("""
            SELECT external_account_id, status, trading_enabled,
                   CASE WHEN api_key_id IS NOT NULL THEN 'Yes' ELSE 'No' END as has_api_key
            FROM broker_accounts
            ORDER BY created_at DESC
        """))
        
        accounts = result.fetchall()
        
        if not accounts:
            print("   ℹ️ No accounts found")
            return
        
        print(f"   Found {len(accounts)} accounts:")
        for account in accounts:
            print(f"   - {account[0]} | Status: {account[1]} | Trading: {account[2]} | API Key: {account[3]}")
        
        # Count ready accounts
        result = db.execute(text("""
            SELECT COUNT(*) FROM broker_accounts 
            WHERE status = 'active' AND trading_enabled = TRUE
        """))
        
        ready_count = result.scalar()
        print(f"\n   🎯 {ready_count} accounts ready for trading")
        
    except Exception as e:
        print(f"   ❌ Error checking accounts: {str(e)}")
    finally:
        db.close()

def main():
    """Main function"""
    print("🚀 Enable Trading for Broker Accounts")
    print("=" * 50)
    
    # Show current status
    show_account_status()
    
    # Ask user if they want to proceed
    print("\n⚠️ This will:")
    print("1. Update SUBMITTED accounts to 'active' status")
    print("2. Enable trading for all active accounts")
    print("3. Allow manual investment testing")
    
    response = input("\nProceed? (y/N): ").strip().lower()
    
    if response != 'y':
        print("❌ Operation cancelled")
        return False
    
    success = True
    
    # Step 1: Update status
    if not update_account_status_to_active():
        success = False
    
    # Step 2: Enable trading
    if not enable_trading_for_all_active_accounts():
        success = False
    
    # Show final status
    show_account_status()
    
    if success:
        print("\n✅ Trading enabled successfully!")
        print("\n📋 Next steps:")
        print("1. Restart backend server")
        print("2. Test manual investment in frontend")
        print("3. Monitor backend logs for trade execution")
    else:
        print("\n❌ Some operations failed")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Operation interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
