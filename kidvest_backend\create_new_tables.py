import os
import sys
from sqlalchemy import create_engine
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to the path so we can import app modules
sys.path.append('.')

# Import models
from app import models
from app.database import Base

# Database connection
DATABASE_URL = os.getenv("DATABASE_URL")
engine = create_engine(DATABASE_URL)

def create_tables():
    """Create tables for the new schema"""
    print("Creating tables for new schema...")
    
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    print("Tables created successfully!")

if __name__ == "__main__":
    create_tables()
    print("Done.")
