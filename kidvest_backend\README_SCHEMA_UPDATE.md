# KidVest Database Schema and API Update

This document outlines the steps to update the KidVest application with a new database schema and API that implements a clear parent-child relationship.

## Overview of Changes

1. **Database Schema Changes**:
   - Renamed models for clarity (`KidProfile` → `ChildProfile`)
   - Simplified Gift model (removed redundant fields)
   - Added Investment model for tracking investments
   - Enhanced User model with profile information
   - Improved relationships between models

2. **API Changes**:
   - Updated endpoints to use the new schema
   - Added new endpoints for investment management
   - Improved error handling and logging

3. **Frontend Changes**:
   - Updated gift wall UI to work with the new API
   - Added support for investment tracking

## Implementation Steps

### 1. Update Database Schema

Run the database schema update script to add new columns to existing tables:

```bash
python update_db_schema_new.py
```

### 2. Migrate Data to New Schema

Run the data migration script to migrate data from the old schema to the new schema:

```bash
python migrate_db.py
```

### 3. Update API Code

Replace the existing files with the new versions:

1. Copy `app/models_new.py` to `app/models.py`
2. Copy `app/schemas_new.py` to `app/schemas.py`
3. Copy `app/crud_new.py` to `app/crud.py`
4. Copy `app/main_new.py` to `app/main.py`

### 4. Update Frontend Code

Replace the existing frontend JavaScript with the new version:

1. Copy `gift-wall-ui/script_new.js` to `gift-wall-ui/script.js`

### 5. Restart Services

Restart the backend and frontend services:

```bash
# Restart backend
uvicorn app.main:app --reload

# Restart frontend
cd gift-wall-ui
python server.py
```

## New Database Schema

### User (Parent)

```
id: UUID (primary key)
name: String
email: String (unique)
user_type: Enum ("parent", "child")
created_at: DateTime
is_active: Boolean
phone_number: String (optional)
address: String (optional)
city: String (optional)
state: String (optional)
postal_code: String (optional)
country: String (optional)
```

### ChildProfile

```
id: UUID (primary key)
parent_id: UUID (foreign key to User)
name: String
age: Integer (optional)
handle: String (unique)
is_public: Boolean
avatar: String (optional)
bio: String (optional)
created_at: DateTime
updated_at: DateTime
```

### Gift

```
id: UUID (primary key)
child_profile_id: UUID (foreign key to ChildProfile)
from_name: String (optional)
from_email: String (optional)
amount_usd: Float
message: Text (optional)
payment_status: String ("pending", "completed", "failed")
payment_intent_id: String (optional)
checkout_session_id: String (optional)
is_anonymous: Boolean
created_at: DateTime
updated_at: DateTime
```

### Investment

```
id: UUID (primary key)
child_profile_id: UUID (foreign key to ChildProfile)
gift_id: UUID (foreign key to Gift, optional)
amount_usd: Float
symbol: String
shares: Float
purchase_price: Float
status: String ("pending", "completed", "failed")
transaction_id: String (optional)
created_at: DateTime
updated_at: DateTime
```

### BrokerAccount

```
id: UUID (primary key)
user_id: UUID (foreign key to User)
broker_type: String
external_account_id: String (optional)
status: String ("pending", "active", "rejected")
created_at: DateTime
updated_at: DateTime
```

## API Endpoints

### User Endpoints

- `POST /api/users/` - Create a new user
- `GET /api/users/me` - Get current user
- `PUT /api/users/me` - Update current user

### Child Profile Endpoints

- `POST /api/children/` - Create a new child profile
- `GET /api/children/` - Get all child profiles for the current user
- `GET /api/children/{profile_id}` - Get a specific child profile
- `PUT /api/children/{profile_id}` - Update a child profile
- `DELETE /api/children/{profile_id}` - Delete a child profile

### Gift Wall Endpoints

- `GET /api/wall/{handle}` - Get a gift wall by handle
- `POST /api/wall/{handle}/gift` - Create a new gift from the gift wall

### Gift Endpoints

- `GET /api/gifts/` - Get gifts for a child profile or all child profiles
- `GET /api/gifts/{gift_id}` - Get a specific gift

### Investment Endpoints

- `POST /api/investments/` - Create a new investment
- `GET /api/investments/` - Get investments for a child profile or all child profiles
- `GET /api/investments/{investment_id}` - Get a specific investment
- `PUT /api/investments/{investment_id}` - Update an investment

### Webhook Endpoint

- `POST /webhook/` - Handle Stripe webhook events

## Testing

After implementing the changes, test the following flows:

1. **Gift Wall Flow**:
   - Access a child's gift wall using their handle
   - Create a gift
   - Complete the Stripe checkout process
   - Verify the gift appears on the gift wall

2. **Parent Dashboard Flow**:
   - Log in as a parent
   - View child profiles
   - View gifts for each child
   - Create investments from gifts
   - Track investment performance

## Troubleshooting

If you encounter issues:

1. Check the backend logs for errors
2. Verify database connections
3. Check Stripe API keys and webhook configuration
4. Ensure frontend is correctly configured to use the API
