from sqlalchemy.orm import Session
from app.database import SessionLocal
from app import models
import uuid

def get_latest_gift():
    """Get the latest gift from the database"""
    print("=== Getting Latest Gift ===")
    
    db = SessionLocal()
    
    try:
        # Get the latest gift
        gift = db.query(models.Gift).order_by(models.Gift.created_at.desc()).first()
        
        if gift:
            print(f"Latest gift: {gift.id}")
            print(f"From: {gift.from_name} ({gift.from_email})")
            print(f"Amount: ${gift.amount_usd}")
            print(f"Payment status: {gift.payment_status}")
            print(f"Created at: {gift.created_at}")
            
            return gift.id
        else:
            print("No gifts found in the database")
            return None
        
    except Exception as e:
        print(f"Error getting latest gift: {e}")
        return None
    finally:
        db.close()

if __name__ == "__main__":
    gift_id = get_latest_gift()
    if gift_id:
        print(f"\nUse this gift ID for testing: {gift_id}")
