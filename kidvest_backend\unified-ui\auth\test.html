<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Registration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4a6cf7;
            color: white;
            border: none;
            cursor: pointer;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Test Registration</h1>
    
    <form id="registerForm">
        <div class="form-group">
            <label for="name">Full Name:</label>
            <input type="text" id="name" value="Test User" required>
        </div>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="password123" required>
        </div>
        
        <button type="submit">Register</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = 'Sending request...';
            
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            const data = {
                name,
                email,
                password,
                user_type: 'parent'
            };
            
            try {
                console.log('Sending registration request...');
                resultDiv.textContent += '\nRequest data: ' + JSON.stringify(data, null, 2);
                
                const response = await fetch('http://localhost:8000/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                console.log('Response status:', response.status);
                resultDiv.textContent += '\nResponse status: ' + response.status;
                
                const responseText = await response.text();
                console.log('Response text:', responseText);
                resultDiv.textContent += '\nResponse text: ' + responseText;
                
                try {
                    const responseData = JSON.parse(responseText);
                    console.log('Response data:', responseData);
                    resultDiv.textContent += '\nParsed response: ' + JSON.stringify(responseData, null, 2);
                } catch (e) {
                    console.error('Error parsing response:', e);
                    resultDiv.textContent += '\nError parsing response: ' + e.message;
                }
            } catch (error) {
                console.error('Error:', error);
                resultDiv.textContent += '\nError: ' + error.message;
            }
        });
    </script>
</body>
</html>
