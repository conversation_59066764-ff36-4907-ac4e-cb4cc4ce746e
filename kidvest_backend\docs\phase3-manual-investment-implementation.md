# Phase 3: Manual Investment Flow - Implementation Guide

## 🎯 **Overview**

Phase 3 implements the Manual Investment Flow where parents can manually invest gift money for their children using their Alpaca broker accounts. The system supports investing in TSLA or SPY stocks with real-time market data and order execution.

## 🔄 **Changes Summary**

### **Database Changes**
- **Extended Investment model** with `parent_id` and `executed_at` fields
- **Added relationship tracking** between parent, child, and investments
- **Enhanced balance calculation** to track available vs invested amounts

### **Backend Changes**
- **New Alpaca trading integration** for market data and order placement
- **Manual investment endpoint** `/api/invest/` with comprehensive validation
- **Parent dashboard endpoint** `/api/parent/dashboard` with children and balances
- **Enhanced CRUD operations** for investment tracking

### **Frontend Changes**
- **Manual investment screen** with child selection, stock picker, and amount input
- **Real-time balance validation** and investment confirmation
- **Integration with parent dashboard** for portfolio management

## 📊 **Database Schema Updates**

### **Investment Table (Extended)**
```sql
CREATE TABLE investments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    child_profile_id UUID NOT NULL REFERENCES child_profiles(id),
    parent_id UUID NOT NULL REFERENCES users(id),  -- NEW
    gift_id UUID REFERENCES gifts(id),
    amount_usd FLOAT NOT NULL,
    symbol VARCHAR NOT NULL,
    shares FLOAT NOT NULL,
    purchase_price FLOAT NOT NULL,
    status VARCHAR DEFAULT 'pending',
    transaction_id VARCHAR,
    executed_at TIMESTAMP,  -- NEW
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### **Migration Required**
```bash
python migrate_investment_table.py
```

## 🔌 **API Endpoints**

### **1. Manual Investment**
```http
POST /api/invest/
Authorization: Bearer {parent_token}
Content-Type: application/json

{
    "child_profile_id": "uuid",
    "stock_symbol": "TSLA|SPY",
    "amount_usd": 25.50
}
```

**Response:**
```json
{
    "success": true,
    "message": "Successfully invested $25.50 in TSLA for Child Name",
    "investment_id": "uuid",
    "transaction_id": "alpaca_order_id",
    "shares_purchased": 0.1234,
    "purchase_price": 206.75,
    "executed_at": "2024-01-15T10:30:00Z"
}
```

### **2. Parent Dashboard**
```http
GET /api/parent/dashboard
Authorization: Bearer {parent_token}
```

**Response:**
```json
{
    "parent_info": {
        "id": "uuid",
        "name": "Parent Name",
        "email": "<EMAIL>"
    },
    "summary": {
        "total_children": 2,
        "total_available_balance": 150.00,
        "total_investments": 5,
        "total_invested": 75.25
    },
    "children": [
        {
            "profile": {
                "id": "uuid",
                "name": "Child Name",
                "handle": "child-handle"
            },
            "available_balance": 50.00
        }
    ],
    "recent_investments": [...]
}
```

## 🏗️ **Architecture Components**

### **1. Alpaca Service (`alpaca_service.py`)**
```python
# Market data retrieval
get_alpaca_market_data(symbol: str) -> (data, error)

# Order placement
place_alpaca_order(account_id: str, symbol: str, amount_usd: float) -> (result, error)
```

### **2. Investment CRUD (`crud.py`)**
```python
# Balance calculation
get_available_gift_balance(db, child_profile_id) -> float

# Broker account lookup
get_broker_account_by_user(db, user_id) -> BrokerAccount

# Investment tracking
get_investments_by_parent(db, parent_id) -> List[Investment]
```

### **3. Manual Investment Flow (`main.py`)**
```python
@app.post("/api/invest/")
def manual_invest(investment_request, current_user):
    # 1. Validate child ownership
    # 2. Check available balance
    # 3. Verify broker account
    # 4. Execute Alpaca order
    # 5. Record investment
    # 6. Return confirmation
```

## 🔒 **Security & Validation**

### **Investment Validations**
- ✅ **Parent ownership** - Only invest for own children
- ✅ **Sufficient balance** - Cannot exceed available gift money
- ✅ **Active broker account** - Parent must have completed KYC
- ✅ **Stock symbol validation** - Only TSLA or SPY allowed
- ✅ **Amount validation** - Must be positive and reasonable

### **Authentication**
- ✅ **JWT token required** for all investment endpoints
- ✅ **Parent role verification** via `get_current_parent_user`
- ✅ **Child profile ownership** validation

## 📱 **Frontend Implementation**

### **Manual Investment Screen**
- **Child Selection** - List of children with available balances
- **Stock Picker** - TSLA or SPY selection with company info
- **Amount Input** - Dollar amount with balance validation
- **Investment Confirmation** - Real-time feedback and success handling

### **Navigation Flow**
```
Parent Dashboard → Select Child → Manual Investment Screen
                                      ↓
                              Investment Confirmation
                                      ↓
                              Portfolio View (Updated)
```

## 🧪 **Testing**

### **Test Scripts**
```bash
# Database migration
python migrate_investment_table.py

# Manual investment flow
python test_manual_investment.py

# Alpaca integration
python test_alpaca_integration.py
```

### **Test Scenarios**
- ✅ **Complete investment flow** - Parent to child investment
- ✅ **Balance validation** - Insufficient funds handling
- ✅ **Stock validation** - Invalid symbol rejection
- ✅ **Authentication** - Unauthorized access prevention
- ✅ **Alpaca integration** - Market data and order placement

## 🚀 **Deployment Steps**

### **1. Database Migration**
```bash
cd kidvest_backend
python migrate_investment_table.py
```

### **2. Environment Variables**
```env
# Alpaca API (already configured)
ALPACA_API_KEY=your_alpaca_key
ALPACA_SECRET_KEY=your_alpaca_secret

# Database (already configured)
DATABASE_URL=postgresql://...
```

### **3. Backend Restart**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### **4. Frontend Integration**
```bash
cd kidvest-app-new
npm start
```

## 📈 **Usage Flow**

### **Parent Perspective**
1. **Login** to KidVest app
2. **View Dashboard** with children and gift balances
3. **Select Child** for investment
4. **Choose Stock** (TSLA or SPY)
5. **Enter Amount** (validated against available balance)
6. **Confirm Investment** and receive confirmation
7. **View Portfolio** with updated holdings

### **System Processing**
1. **Validate Request** - Ownership, balance, broker account
2. **Get Market Data** - Current stock price from Alpaca
3. **Calculate Shares** - Amount ÷ Current Price
4. **Place Order** - Market order via Alpaca Broker API
5. **Record Investment** - Database entry with transaction details
6. **Update Balances** - Reduce available gift balance

## 🔍 **Monitoring & Logs**

### **Investment Logs**
```
==== MANUAL INVESTMENT REQUEST ====
Parent: uuid (Parent Name)
Child: uuid
Symbol: TSLA
Amount: $25.50
✅ Child profile validated: Child Name
✅ Sufficient balance: $50.00
✅ Broker account found: alpaca_account_id
✅ Alpaca order successful: {...}
✅ Investment created: investment_uuid
```

### **Error Handling**
- **Insufficient Balance** - Clear error message with available amount
- **Invalid Stock** - Validation error for non-TSLA/SPY symbols
- **Alpaca Errors** - Market closed, account issues, etc.
- **Database Errors** - Transaction rollback and error logging

## 🎉 **Success Metrics**

### **Phase 3 Completion Criteria**
- ✅ **Database schema** extended with parent tracking
- ✅ **Alpaca integration** for real-time trading
- ✅ **Manual investment endpoint** with full validation
- ✅ **Parent dashboard** with children and balances
- ✅ **Frontend UI** for investment flow
- ✅ **Comprehensive testing** and error handling

### **Ready for Production**
- ✅ **Security validations** implemented
- ✅ **Error handling** comprehensive
- ✅ **Logging** detailed for debugging
- ✅ **Testing** covers all scenarios
- ✅ **Documentation** complete

## 🔮 **Future Enhancements**

### **Phase 4 Potential Features**
- **More Stock Options** - Expand beyond TSLA/SPY
- **Fractional Shares** - More precise investment amounts
- **Investment Scheduling** - Recurring investments
- **Portfolio Analytics** - Performance tracking and charts
- **Investment Recommendations** - AI-powered suggestions

---

**Phase 3: Manual Investment Flow is now complete and ready for production use!** 🚀
