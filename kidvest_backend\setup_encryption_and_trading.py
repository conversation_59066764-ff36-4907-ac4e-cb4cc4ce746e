#!/usr/bin/env python3

"""
Setup script for encryption and trading functionality
"""

import os
import sys
from sqlalchemy import text
from app.database import SessionLocal

def setup_encryption_key():
    """Setup encryption key for the application"""
    print("🔑 Setting up encryption key...")
    
    try:
        from app.encryption_service import setup_encryption_key
        key = setup_encryption_key()
        
        if key:
            # Check if .env file exists
            env_file = ".env"
            env_exists = os.path.exists(env_file)
            
            if env_exists:
                # Read existing .env file
                with open(env_file, 'r') as f:
                    lines = f.readlines()
                
                # Check if key already exists
                key_exists = any(line.startswith("KIDVEST_ENCRYPTION_KEY=") for line in lines)
                
                if not key_exists:
                    # Append the key
                    with open(env_file, 'a') as f:
                        f.write(f"\n# KidVest Encryption Key\nKIDVEST_ENCRYPTION_KEY={key}\n")
                    print(f"✅ Added encryption key to {env_file}")
                else:
                    print(f"⚠️ Encryption key already exists in {env_file}")
            else:
                # Create new .env file
                with open(env_file, 'w') as f:
                    f.write(f"# KidVest Encryption Key\nKIDVEST_ENCRYPTION_KEY={key}\n")
                print(f"✅ Created {env_file} with encryption key")
            
            return True
        else:
            print("❌ Failed to generate encryption key")
            return False
            
    except ImportError:
        print("❌ cryptography library not installed")
        print("💡 Install with: pip install cryptography==41.0.7")
        return False
    except Exception as e:
        print(f"❌ Error setting up encryption: {str(e)}")
        return False

def enable_trading_for_existing_accounts():
    """Enable trading for existing active broker accounts"""
    print("\n🔧 Enabling trading for existing accounts...")
    
    db = SessionLocal()
    try:
        # Find active broker accounts without trading enabled
        result = db.execute(text("""
            SELECT id, user_id, external_account_id, status, trading_enabled
            FROM broker_accounts 
            WHERE status = 'active' AND (trading_enabled IS NULL OR trading_enabled = FALSE)
        """))
        
        accounts = result.fetchall()
        
        if not accounts:
            print("   ℹ️ No accounts need trading enablement")
            return True
        
        print(f"   Found {len(accounts)} accounts to enable trading for:")
        
        for account in accounts:
            print(f"   - Account ID: {account[2]} (Status: {account[3]})")
        
        # Enable trading for these accounts
        updated = db.execute(text("""
            UPDATE broker_accounts 
            SET trading_enabled = TRUE 
            WHERE status = 'active' AND (trading_enabled IS NULL OR trading_enabled = FALSE)
        """))
        
        db.commit()
        print(f"   ✅ Enabled trading for {updated.rowcount} accounts")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error enabling trading: {str(e)}")
        db.rollback()
        return False
    finally:
        db.close()

def test_encryption():
    """Test encryption functionality"""
    print("\n🧪 Testing encryption...")
    
    try:
        from app.encryption_service import encrypt_secret, decrypt_secret, is_encryption_enabled
        
        test_secret = "test_api_secret_12345"
        print(f"   Original: {test_secret}")
        
        encrypted = encrypt_secret(test_secret)
        print(f"   Encrypted: {encrypted[:20]}...")
        
        decrypted = decrypt_secret(encrypted)
        print(f"   Decrypted: {decrypted}")
        
        success = test_secret == decrypted
        encryption_enabled = is_encryption_enabled()
        
        print(f"   Encryption enabled: {encryption_enabled}")
        print(f"   Test passed: {success}")
        
        return success
        
    except Exception as e:
        print(f"   ❌ Encryption test failed: {str(e)}")
        return False

def verify_database_schema():
    """Verify database schema has required fields"""
    print("\n📊 Verifying database schema...")
    
    db = SessionLocal()
    try:
        # Check broker_accounts table structure
        result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'broker_accounts'
        """))
        
        columns = [row[0] for row in result.fetchall()]
        required_columns = [
            'id', 'user_id', 'broker_type', 'external_account_id',
            'bearer_token', 'api_key_id', 'api_secret_key', 'trading_enabled',
            'status', 'created_at', 'updated_at'
        ]
        
        missing_columns = [col for col in required_columns if col not in columns]
        
        if missing_columns:
            print(f"   ❌ Missing columns: {missing_columns}")
            print("   💡 Run: python migrate_broker_account_credentials.py")
            return False
        else:
            print("   ✅ All required columns present")
        
        # Check if we have any broker accounts
        result = db.execute(text("SELECT COUNT(*) FROM broker_accounts"))
        count = result.scalar()
        print(f"   ℹ️ Found {count} broker accounts")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Schema verification failed: {str(e)}")
        return False
    finally:
        db.close()

def show_next_steps():
    """Show next steps for complete setup"""
    print("\n" + "=" * 60)
    print("🎯 SETUP COMPLETE - NEXT STEPS")
    print("=" * 60)
    
    print("\n1. 🔄 RESTART BACKEND SERVER:")
    print("   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    
    print("\n2. 🧪 TEST ENCRYPTION:")
    print("   python app/encryption_service.py")
    
    print("\n3. 🔐 ENABLE TRADING FOR EXISTING ACCOUNTS:")
    print("   For each active account, call:")
    print("   POST /api/broker-accounts/{account_id}/enable-trading")
    
    print("\n4. 📱 TEST MANUAL INVESTMENT:")
    print("   - Start frontend: cd kidvest-app-new && npm start")
    print("   - Navigate to manual investment screen")
    print("   - Select child, stock (TSLA/SPY), amount")
    print("   - Click 'Invest' button")
    
    print("\n5. 📊 MONITOR LOGS:")
    print("   Watch backend logs for:")
    print("   - 🔐 Using account-specific API credentials")
    print("   - ✅ Order successful")
    print("   - ✅ Investment created")
    
    print("\n🔒 SECURITY REMINDERS:")
    print("- Keep KIDVEST_ENCRYPTION_KEY secure and backed up")
    print("- Never commit .env file to version control")
    print("- Rotate encryption keys periodically")
    print("- Monitor API key usage in Alpaca dashboard")

def main():
    """Main setup function"""
    print("🚀 KidVest Encryption & Trading Setup")
    print("=" * 50)
    
    success = True
    
    # Step 1: Verify database schema
    if not verify_database_schema():
        print("\n❌ Database schema verification failed")
        print("💡 Run migration first: python migrate_broker_account_credentials.py")
        return False
    
    # Step 2: Setup encryption
    if not setup_encryption_key():
        print("\n❌ Encryption setup failed")
        success = False
    
    # Step 3: Test encryption
    if not test_encryption():
        print("\n❌ Encryption test failed")
        success = False
    
    # Step 4: Enable trading for existing accounts
    if not enable_trading_for_existing_accounts():
        print("\n❌ Trading enablement failed")
        success = False
    
    if success:
        print("\n✅ Setup completed successfully!")
        show_next_steps()
    else:
        print("\n❌ Setup completed with errors")
        print("💡 Check the errors above and retry")
    
    return success

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help"]:
        print(__doc__)
        sys.exit(0)
    
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Setup failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
