import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# Add the current directory to the path so we can import app modules
sys.path.append('.')

# Load environment variables
load_dotenv()

# Import models after setting up the path
from app.models import KidProfile
from app.database import Base

# Database connection
DATABASE_URL = os.getenv("DATABASE_URL")
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

# Check if the profile exists
def check_profile(handle):
    profile = db.query(KidProfile).filter(KidProfile.handle == handle).first()
    if profile:
        print(f"Profile found: {profile.name} (@{profile.handle})")
        print(f"ID: {profile.id}")
        print(f"Age: {profile.age}")
        print(f"Bio: {profile.bio}")
        print(f"Is public: {profile.is_public}")
        return True
    else:
        print(f"No profile found with handle: {handle}")
        return False

# Create a test profile if it doesn't exist
def create_test_profile():
    from app.models import User, KidProfile
    import uuid
    
    # First, check if we have a custodian user
    user = db.query(User).filter(User.user_type == "custodian").first()
    if not user:
        print("Creating a custodian user...")
        user = User(
            id=uuid.uuid4(),
            name="Test Parent",
            email="<EMAIL>",
            user_type="custodian"
        )
        db.add(user)
        db.commit()
        print(f"Created user with ID: {user.id}")
    
    # Now create the profile
    profile = KidProfile(
        id=uuid.uuid4(),
        name="Zohaib Ali",
        handle="zohaib_ali",
        age=10,
        bio="I love investing and learning about money!",
        is_public=True,
        parent_id=user.id
    )
    db.add(profile)
    db.commit()
    print(f"Created profile with ID: {profile.id}")
    return profile

if __name__ == "__main__":
    print("Checking for profile 'zohaib_ali'...")
    if not check_profile("zohaib_ali"):
        print("Creating test profile...")
        profile = create_test_profile()
        print("Test profile created.")
    
    print("Done.")
