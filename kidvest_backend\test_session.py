import requests
import json
import time

# Base URL for API
BASE_URL = "http://localhost:8080/api/onboarding"

def test_session_flow():
    print("Testing complete onboarding flow with session token...")
    
    # Step 1: Create a session with unique email
    unique_email = f"test_{int(time.time())}@example.com"
    step1_data = {
        "full_name": "<PERSON> Doe",
        "email": unique_email
    }
    
    print(f"\nStep 1: Creating session with email {unique_email}...")
    response = requests.post(f"{BASE_URL}/step1", json=step1_data)
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code != 200:
        print("Failed to create session")
        return
    
    data = response.json()
    if not data.get("success"):
        print(f"Error: {data.get('message')}")
        return
    
    session_token = data.get("session_token")
    print(f"Session token: {session_token}")
    
    # Step 2: Identity verification
    step2_data = {
        "dob": "1990-01-01",
        "phone_number": "************",
        "street_address": "123 Main St",
        "city": "New York",
        "state": "NY",
        "postal_code": "10001",
        "country": "USA",
        "ssn": "***********",
        "session_token": session_token  # Include in body
    }
    
    print("\nStep 2: Identity verification...")
    url = f"{BASE_URL}/step2?session_token={session_token}"  # Include in URL
    print(f"URL: {url}")
    print(f"Request body: {json.dumps(step2_data)}")
    
    response = requests.post(url, json=step2_data)
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code != 200:
        print("Failed to complete step 2")
        return
    
    data = response.json()
    if not data.get("success"):
        print(f"Error: {data.get('message')}")
        return
    
    print("Step 2 completed successfully")
    
    # Step 3: Financial profile
    step3_data = {
        "employment_status": "EMPLOYED",
        "income_range": "50k_100k",
        "net_worth_range": "50k_100k",
        "funding_source": "employment_income",
        "investment_experience": "some",
        "risk_tolerance": "moderate",
        "session_token": session_token  # Include in body
    }
    
    print("\nStep 3: Financial profile...")
    url = f"{BASE_URL}/step3?session_token={session_token}"  # Include in URL
    print(f"URL: {url}")
    print(f"Request body: {json.dumps(step3_data)}")
    
    response = requests.post(url, json=step3_data)
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code != 200:
        print("Failed to complete step 3")
        return
    
    data = response.json()
    if not data.get("success"):
        print(f"Error: {data.get('message')}")
        return
    
    print("Step 3 completed successfully")
    
    # Step 4: Disclosures and agreements
    step4_data = {
        "is_control_person": False,
        "is_affiliated_exchange_or_finra": False,
        "is_politically_exposed": False,
        "immediate_family_exposed": False,
        "customer_agreement_accepted": True,
        "margin_agreement_accepted": True,
        "account_agreement_accepted": True,
        "session_token": session_token  # Include in body
    }
    
    print("\nStep 4: Disclosures and agreements...")
    url = f"{BASE_URL}/step4?session_token={session_token}"  # Include in URL
    print(f"URL: {url}")
    print(f"Request body: {json.dumps(step4_data)}")
    
    response = requests.post(url, json=step4_data)
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code != 200:
        print("Failed to complete step 4")
        return
    
    data = response.json()
    if not data.get("success"):
        print(f"Error: {data.get('message')}")
        return
    
    print("Step 4 completed successfully")
    
    # Submit to Alpaca
    submit_data = {
        "session_token": session_token  # Include in body
    }
    
    print("\nSubmitting to Alpaca...")
    url = f"{BASE_URL}/submit?session_token={session_token}"  # Include in URL
    print(f"URL: {url}")
    print(f"Request body: {json.dumps(submit_data)}")
    
    response = requests.post(url, json=submit_data)
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code != 200:
        print("Failed to submit to Alpaca")
        return
    
    data = response.json()
    if not data.get("success"):
        print(f"Error: {data.get('message')}")
        return
    
    print("Submission to Alpaca completed successfully")
    print(f"Account ID: {data.get('account_id')}")
    print(f"Status: {data.get('status')}")

if __name__ == "__main__":
    test_session_flow()
