#!/usr/bin/env python3

"""
Test Austin's authentication and investment flow
"""

import requests
import json
import sys
from datetime import datetime
from sqlalchemy import text
from app.database import SessionLocal

BASE_URL = "http://localhost:8000"
AUSTIN_EMAIL = "<EMAIL>"
AUSTIN_PASSWORD = "austinglusac"

def test_austin_login():
    """Test Austin's login"""
    print("🔐 Testing Austin's login...")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/token",
            data={
                "username": AUSTIN_EMAIL,
                "password": AUSTIN_PASSWORD
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            timeout=10
        )
        
        print(f"   Login response status: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get("access_token")
            print(f"   ✅ Login successful!")
            print(f"   Token type: {token_data.get('token_type')}")
            print(f"   Token: {access_token[:30]}...")
            return access_token
        else:
            print(f"   ❌ Login failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ Login error: {str(e)}")
        return None

def test_authenticated_investment(token):
    """Test investment with Austin's token"""
    print(f"\n💰 Testing authenticated investment...")
    
    # Get Austin's child ID
    db = SessionLocal()
    try:
        result = db.execute(text("""
            SELECT cp.id, cp.name
            FROM child_profiles cp
            JOIN users u ON cp.parent_id = u.id
            WHERE u.email = :email
            LIMIT 1
        """), {"email": AUSTIN_EMAIL})
        
        child_row = result.fetchone()
        if not child_row:
            print("   ❌ No child found for Austin")
            return False
        
        child_id = str(child_row[0])
        child_name = child_row[1]
        
        print(f"   Child: {child_name} (ID: {child_id})")
        
    finally:
        db.close()
    
    # Test investment
    investment_request = {
        "child_profile_id": child_id,
        "stock_symbol": "SPY",
        "amount_usd": 20.0
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print(f"   Investment request: {json.dumps(investment_request, indent=2)}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/invest/",
            json=investment_request,
            headers=headers,
            timeout=60
        )
        
        print(f"   Response Status: {response.status_code}")
        print(f"   Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"   Response Body: {json.dumps(response_data, indent=2)}")
            
            if response.status_code in [200, 201]:
                print(f"   ✅ Investment successful!")
                return response_data
            else:
                print(f"   ❌ Investment failed: {response_data}")
                return None
                
        except json.JSONDecodeError:
            print(f"   Response Text: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ Investment request failed: {str(e)}")
        return None

def check_database_after_investment():
    """Check database for new investment"""
    print(f"\n💾 Checking database for new investment...")
    
    db = SessionLocal()
    try:
        # Check recent investments
        result = db.execute(text("""
            SELECT i.id, i.amount_usd, i.symbol, i.shares, i.status, 
                   i.transaction_id, i.created_at, cp.name as child_name,
                   u.name as parent_name
            FROM investments i
            JOIN child_profiles cp ON i.child_profile_id = cp.id
            JOIN users u ON i.parent_id = u.id
            WHERE i.created_at > NOW() - INTERVAL '5 minutes'
            ORDER BY i.created_at DESC
        """))
        
        investments = result.fetchall()
        
        if not investments:
            print("   ❌ No recent investments found")
            return False
        
        print(f"   ✅ Found {len(investments)} recent investments:")
        for inv in investments:
            print(f"   - ${inv[1]} {inv[2]} for {inv[7]} by {inv[8]}")
            print(f"     Status: {inv[4]} | Shares: {inv[5]}")
            print(f"     Transaction: {inv[5]} | Created: {inv[6]}")
            print()
        
        return True
        
    except Exception as e:
        print(f"   ❌ Database check failed: {str(e)}")
        return False
    finally:
        db.close()

def main():
    """Main test function"""
    print("🧪 Austin Authentication & Investment Test")
    print("=" * 50)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Test login
    token = test_austin_login()
    if not token:
        print("\n❌ Authentication failed - cannot proceed")
        print("💡 Check Austin's password or backend auth system")
        return False
    
    # Step 2: Test investment
    investment_result = test_authenticated_investment(token)
    
    # Step 3: Check database
    db_success = check_database_after_investment()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    print(f"✅ AUTHENTICATION: {'Working' if token else 'Failed'}")
    print(f"✅ INVESTMENT API: {'Working' if investment_result else 'Failed'}")
    print(f"✅ DATABASE RECORDING: {'Working' if db_success else 'Failed'}")
    
    if token and investment_result and db_success:
        print(f"\n🎉 COMPLETE SUCCESS!")
        print(f"   - Austin can authenticate ✅")
        print(f"   - Investment API works ✅")
        print(f"   - Database records investments ✅")
        print(f"   - Frontend should now work! ✅")
        
        print(f"\n📱 FRONTEND TESTING:")
        print(f"   1. Start frontend: cd kidvest-app-new && npm start")
        print(f"   2. Navigate to manual investment screen")
        print(f"   3. Investment button should now work!")
        
    elif token and not investment_result:
        print(f"\n⚠️ PARTIAL SUCCESS:")
        print(f"   - Authentication works ✅")
        print(f"   - Investment API has issues ❌")
        print(f"   - Check backend logs for investment errors")
        
    else:
        print(f"\n❌ AUTHENTICATION ISSUE:")
        print(f"   - Check Austin's password")
        print(f"   - Check backend auth system")
    
    return token and investment_result and db_success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
