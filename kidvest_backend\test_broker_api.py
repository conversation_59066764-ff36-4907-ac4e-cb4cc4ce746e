import os
import requests
import json
import base64
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_broker_api_auth():
    """Test if the Broker API credentials are working by making a simple GET request."""
    
    # Get API credentials
    api_key = os.getenv("ALPACA_API_KEY")
    api_secret = os.getenv("ALPACA_SECRET_KEY")
    
    if not api_key or not api_secret:
        print("ERROR: Alpaca API keys not found in environment variables")
        return
    
    # Create HTTP Basic Authentication header
    auth_string = f"{api_key}:{api_secret}"
    encoded_auth = base64.b64encode(auth_string.encode()).decode()
    
    headers = {
        "Authorization": f"Basic {encoded_auth}",
        "Content-Type": "application/json"
    }
    
    # Try a simple GET request to check authentication
    url = "https://broker-api.sandbox.alpaca.markets/v1/assets"
    
    print("\n==== TESTING BROKER API AUTHENTICATION ====")
    print("Request URL:", url)
    print("Request Headers (masked):", json.dumps({k: (v[:10] + "..." if k.lower() == "authorization" else v) for k, v in headers.items()}, indent=2))
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        
        print("\n==== BROKER API RESPONSE ====")
        print("Response Status Code:", response.status_code)
        print("Response Headers:", json.dumps(dict(response.headers), indent=2))
        
        if response.status_code == 200:
            print("\n✅ SUCCESS: Your Broker API credentials are working correctly!")
            # Just print the first asset to avoid overwhelming output
            assets = response.json()
            if assets and len(assets) > 0:
                print(f"\nSample asset: {json.dumps(assets[0], indent=2)}")
                print(f"Total assets available: {len(assets)}")
        else:
            try:
                error_json = response.json()
                print("\n❌ ERROR: Authentication failed")
                print("Response Body:", json.dumps(error_json, indent=2))
            except:
                print("\n❌ ERROR: Authentication failed")
                print("Response Body:", response.text)
                
    except Exception as e:
        print("\n==== BROKER API ERROR ====")
        print(f"Error: {str(e)}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    test_broker_api_auth()
