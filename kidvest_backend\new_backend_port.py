from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, EmailStr
from typing import Optional
import uvicorn
import traceback
import sys

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# User model
class UserCreate(BaseModel):
    name: str
    email: EmailStr
    password: str
    user_type: str
    phone_number: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None

class UserResponse(BaseModel):
    id: str
    name: str
    email: str
    user_type: str
    is_active: bool = True
    created_at: str

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    print(f"Global exception handler caught: {str(exc)}")
    traceback.print_exc(file=sys.stdout)
    return JSONResponse(
        status_code=500,
        content={"detail": f"Internal Server Error: {str(exc)}"}
    )

# Root route
@app.get("/")
def read_root():
    print("Root endpoint called")
    return {"message": "Welcome to New Test Backend on Port 8001!"}

# Register route
@app.post("/api/register", response_model=UserResponse)
async def register_user(user_data: UserCreate):
    print(f"Registration request received: {user_data.name}, {user_data.email}")
    
    try:
        # Simulate successful registration
        return {
            "id": "123e4567-e89b-12d3-a456-426614174000",
            "name": user_data.name,
            "email": user_data.email,
            "user_type": user_data.user_type,
            "is_active": True,
            "created_at": "2023-04-19T12:00:00"
        }
    except Exception as e:
        print(f"Error in register_user: {str(e)}")
        traceback.print_exc(file=sys.stdout)
        raise HTTPException(status_code=500, detail=f"Registration failed: {str(e)}")

if __name__ == "__main__":
    print("Starting new test backend on port 8001...")
    uvicorn.run(app, host="0.0.0.0", port=8001)
