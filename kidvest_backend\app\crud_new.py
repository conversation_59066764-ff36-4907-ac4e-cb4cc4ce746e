from sqlalchemy.orm import Session
from sqlalchemy import desc
from . import models_new as models
from . import schemas_new as schemas
from uuid import UUID
from typing import List, Optional

# User CRUD operations
def create_user(db: Session, user: schemas.UserCreate) -> models.User:
    """Create a new user"""
    db_user = models.User(
        name=user.name,
        email=user.email,
        user_type=user.user_type,
        phone_number=user.phone_number,
        address=user.address,
        city=user.city,
        state=user.state,
        postal_code=user.postal_code,
        country=user.country
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def get_user(db: Session, user_id: UUID) -> Optional[models.User]:
    """Get a user by ID"""
    return db.query(models.User).filter(models.User.id == user_id).first()

def get_user_by_email(db: Session, email: str) -> Optional[models.User]:
    """Get a user by email"""
    return db.query(models.User).filter(models.User.email == email).first()

def update_user(db: Session, user_id: UUID, user_update: schemas.UserUpdate) -> Optional[models.User]:
    """Update a user"""
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    # Update only the fields that are provided
    update_data = user_update.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_user, key, value)
    
    db.commit()
    db.refresh(db_user)
    return db_user

# Child Profile CRUD operations
def create_child_profile(db: Session, profile: schemas.ChildProfileCreate, parent_id: UUID) -> models.ChildProfile:
    """Create a new child profile"""
    db_profile = models.ChildProfile(
        parent_id=parent_id,
        name=profile.name,
        age=profile.age,
        handle=profile.handle,
        is_public=profile.is_public,
        bio=profile.bio
    )
    db.add(db_profile)
    db.commit()
    db.refresh(db_profile)
    return db_profile

def get_child_profile(db: Session, profile_id: UUID) -> Optional[models.ChildProfile]:
    """Get a child profile by ID"""
    return db.query(models.ChildProfile).filter(models.ChildProfile.id == profile_id).first()

def get_child_profile_by_handle(db: Session, handle: str) -> Optional[models.ChildProfile]:
    """Get a child profile by handle"""
    return db.query(models.ChildProfile).filter(models.ChildProfile.handle == handle).first()

def get_child_profiles_by_parent(db: Session, parent_id: UUID, skip: int = 0, limit: int = 100) -> List[models.ChildProfile]:
    """Get all child profiles for a parent"""
    return db.query(models.ChildProfile)\
        .filter(models.ChildProfile.parent_id == parent_id)\
        .order_by(desc(models.ChildProfile.created_at))\
        .offset(skip).limit(limit).all()

def update_child_profile(db: Session, profile_id: UUID, profile_update: schemas.ChildProfileUpdate) -> Optional[models.ChildProfile]:
    """Update a child profile"""
    db_profile = get_child_profile(db, profile_id)
    if not db_profile:
        return None
    
    # Update only the fields that are provided
    update_data = profile_update.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_profile, key, value)
    
    db.commit()
    db.refresh(db_profile)
    return db_profile

def delete_child_profile(db: Session, profile_id: UUID) -> bool:
    """Delete a child profile"""
    db_profile = get_child_profile(db, profile_id)
    if not db_profile:
        return False
    
    db.delete(db_profile)
    db.commit()
    return True

# Gift CRUD operations
def create_gift(db: Session, gift: schemas.GiftCreate) -> models.Gift:
    """Create a new gift"""
    db_gift = models.Gift(
        child_profile_id=gift.child_profile_id,
        from_name=gift.from_name,
        from_email=gift.from_email,
        amount_usd=gift.amount_usd,
        message=gift.message,
        is_anonymous=gift.is_anonymous
    )
    db.add(db_gift)
    db.commit()
    db.refresh(db_gift)
    return db_gift

def create_gift_from_wall(db: Session, gift: schemas.GiftWallCreate) -> Optional[models.Gift]:
    """Create a gift from the gift wall"""
    # Get the child profile by handle
    child_profile = get_child_profile_by_handle(db, gift.child_profile_handle)
    if not child_profile:
        return None
    
    # Create the gift
    db_gift = models.Gift(
        child_profile_id=child_profile.id,
        from_name=gift.from_name,
        from_email=gift.from_email,
        amount_usd=gift.amount_usd,
        message=gift.message,
        is_anonymous=gift.is_anonymous
    )
    db.add(db_gift)
    db.commit()
    db.refresh(db_gift)
    return db_gift

def get_gift(db: Session, gift_id: UUID) -> Optional[models.Gift]:
    """Get a gift by ID"""
    return db.query(models.Gift).filter(models.Gift.id == gift_id).first()

def get_gifts_by_child_profile(db: Session, child_profile_id: UUID, skip: int = 0, limit: int = 100) -> List[models.Gift]:
    """Get all gifts for a child profile"""
    return db.query(models.Gift)\
        .filter(models.Gift.child_profile_id == child_profile_id)\
        .filter(models.Gift.payment_status == "completed")\
        .order_by(desc(models.Gift.created_at))\
        .offset(skip).limit(limit).all()

def update_gift_payment_status(db: Session, gift_id: UUID, payment_status: str, payment_intent_id: str = None, checkout_session_id: str = None) -> Optional[models.Gift]:
    """Update a gift's payment status"""
    db_gift = get_gift(db, gift_id)
    if not db_gift:
        return None
    
    db_gift.payment_status = payment_status
    if payment_intent_id:
        db_gift.payment_intent_id = payment_intent_id
    if checkout_session_id:
        db_gift.checkout_session_id = checkout_session_id
    
    db.commit()
    db.refresh(db_gift)
    return db_gift

# Investment CRUD operations
def create_investment(db: Session, investment: schemas.InvestmentCreate) -> models.Investment:
    """Create a new investment"""
    db_investment = models.Investment(
        child_profile_id=investment.child_profile_id,
        gift_id=investment.gift_id,
        amount_usd=investment.amount_usd,
        symbol=investment.symbol,
        shares=investment.shares,
        purchase_price=investment.purchase_price
    )
    db.add(db_investment)
    db.commit()
    db.refresh(db_investment)
    return db_investment

def get_investment(db: Session, investment_id: UUID) -> Optional[models.Investment]:
    """Get an investment by ID"""
    return db.query(models.Investment).filter(models.Investment.id == investment_id).first()

def get_investments_by_child_profile(db: Session, child_profile_id: UUID, skip: int = 0, limit: int = 100) -> List[models.Investment]:
    """Get all investments for a child profile"""
    return db.query(models.Investment)\
        .filter(models.Investment.child_profile_id == child_profile_id)\
        .order_by(desc(models.Investment.created_at))\
        .offset(skip).limit(limit).all()

def get_investments_by_gift(db: Session, gift_id: UUID) -> List[models.Investment]:
    """Get all investments for a gift"""
    return db.query(models.Investment)\
        .filter(models.Investment.gift_id == gift_id)\
        .order_by(desc(models.Investment.created_at))\
        .all()

def update_investment_status(db: Session, investment_id: UUID, status: str, transaction_id: str = None) -> Optional[models.Investment]:
    """Update an investment's status"""
    db_investment = get_investment(db, investment_id)
    if not db_investment:
        return None
    
    db_investment.status = status
    if transaction_id:
        db_investment.transaction_id = transaction_id
    
    db.commit()
    db.refresh(db_investment)
    return db_investment
