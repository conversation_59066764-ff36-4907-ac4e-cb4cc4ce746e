@echo off
echo Starting KidVest Gift Wall...

echo.
echo Starting FastAPI Backend...
start cmd /k "powershell -Command "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process && .\venv\Scripts\activate && uvicorn app.main:app --reload""

echo.
echo Starting Gift Wall UI...
start cmd /k "powershell -Command "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process && .\venv\Scripts\activate && cd gift-wall-ui && python server.py""

echo.
echo Servers started!
echo Backend: http://localhost:8000
echo Gift Wall UI: http://localhost:8082/wall/zohaib_ali
echo.
