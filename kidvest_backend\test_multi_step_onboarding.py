import requests
import json
import uuid
import datetime

# Base URL for API
BASE_URL = "http://localhost:8000/api/onboarding"

# Generate unique data for testing
timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
unique_id = str(uuid.uuid4())[:8]
unique_email = f"test.user.{timestamp}.{unique_id}@example.com"

# Step 1: Basic account creation
step1_data = {
    "full_name": "<PERSON>",
    "email": unique_email
}

print(f"Step 1: Using unique email: {unique_email}")
response = requests.post(f"{BASE_URL}/step1", json=step1_data)
print(f"Status Code: {response.status_code}")
step1_response = response.json()
print(f"Response: {json.dumps(step1_response, indent=2)}")

if not step1_response.get("success"):
    print("Step 1 failed. Exiting.")
    exit(1)

# Get session token for subsequent steps
session_token = step1_response.get("session_token")
print(f"Session token: {session_token}")

# Step 2: Identity verification
step2_data = {
    "dob": "1992-03-15",
    "phone_number": "************",
    "street_address": "789 Pine Street",
    "city": "Boston",
    "state": "MA",
    "postal_code": "02108",
    "country": "USA",
    "ssn": "***********"
}

print("\nStep 2: Identity verification")
response = requests.post(f"{BASE_URL}/step2?session_token={session_token}", json=step2_data)
print(f"Status Code: {response.status_code}")
step2_response = response.json()
print(f"Response: {json.dumps(step2_response, indent=2)}")

if not step2_response.get("success"):
    print("Step 2 failed. Exiting.")
    exit(1)

# Step 3: Financial profile
step3_data = {
    "employment_status": "EMPLOYED",
    "income_range": "50k_100k",
    "net_worth_range": "50k_100k",
    "funding_source": "employment_income",
    "investment_experience": "some",
    "risk_tolerance": "moderate"
}

print("\nStep 3: Financial profile")
response = requests.post(f"{BASE_URL}/step3?session_token={session_token}", json=step3_data)
print(f"Status Code: {response.status_code}")
step3_response = response.json()
print(f"Response: {json.dumps(step3_response, indent=2)}")

if not step3_response.get("success"):
    print("Step 3 failed. Exiting.")
    exit(1)

# Step 4: Disclosures and agreements
step4_data = {
    "is_control_person": False,
    "is_affiliated_exchange_or_finra": False,
    "is_politically_exposed": False,
    "immediate_family_exposed": False,
    "customer_agreement_accepted": True,
    "margin_agreement_accepted": True,
    "account_agreement_accepted": True
}

print("\nStep 4: Disclosures and agreements")
response = requests.post(f"{BASE_URL}/step4?session_token={session_token}", json=step4_data)
print(f"Status Code: {response.status_code}")
step4_response = response.json()
print(f"Response: {json.dumps(step4_response, indent=2)}")

if not step4_response.get("success"):
    print("Step 4 failed. Exiting.")
    exit(1)

# Final submission to Alpaca
print("\nFinal submission to Alpaca")
response = requests.post(f"{BASE_URL}/submit?session_token={session_token}")
print(f"Status Code: {response.status_code}")
final_response = response.json()
print(f"Response: {json.dumps(final_response, indent=2)}")

if final_response.get("success"):
    print("\n✅ Multi-step onboarding completed successfully!")
    print(f"Account ID: {final_response.get('account_id')}")
    print(f"Status: {final_response.get('status')}")
else:
    print("\n❌ Final submission failed.")
    print(f"Error: {final_response.get('message')}")
