import os
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_alpaca_api():
    # Try different endpoint variations
    endpoints = [
        "https://broker-api.sandbox.alpaca.markets/v1/accounts",
        "https://broker-api.sandbox.alpaca.markets/v1/accounts/",
        "https://broker-api.sandbox.alpaca.markets/accounts",
        "https://broker-api.sandbox.alpaca.markets/accounts/"
    ]

    api_key = os.getenv("ALPACA_API_KEY")
    api_secret = os.getenv("ALPACA_SECRET_KEY")

    if not api_key or not api_secret:
        print("ERROR: Alpaca API keys not found in environment variables")
        return

    headers = {
        "APCA-API-KEY-ID": api_key,
        "APCA-API-SECRET-KEY": api_secret,
        "Content-Type": "application/json"
    }

    # Sample payload for testing
    payload = {
        "contact": {
            "email_address": "<EMAIL>",
            "phone_number": "************",
            "street_address": ["123 Main St"],
            "city": "New York",
            "state": "NY",
            "postal_code": "10001",
            "country": "USA"
        },
        "identity": {
            "given_name": "John",
            "family_name": "Doe",
            "date_of_birth": "1990-01-01",
            "tax_id": "***********",
            "tax_id_type": "USA_SSN",
            "country_of_citizenship": "USA",
            "country_of_birth": "USA",
            "country_of_tax_residence": "USA",
            "funding_source": ["employment_income"]
        },
        "disclosures": {
            "is_control_person": False,
            "is_affiliated_exchange_or_finra": False,
            "is_politically_exposed": False,
            "immediate_family_exposed": False
        },
        "agreements": [
            {
                "agreement": "customer_agreement",
                "signed_at": "2024-04-15T00:00:00Z",
                "ip_address": "127.0.0.1"
            },
            {
                "agreement": "margin_agreement",
                "signed_at": "2024-04-15T00:00:00Z",
                "ip_address": "127.0.0.1"
            },
            {
                "agreement": "account_agreement",
                "signed_at": "2024-04-15T00:00:00Z",
                "ip_address": "127.0.0.1"
            }
        ],
        "documents": [],
        "trusted_contact": {
            "given_name": "Jane",
            "family_name": "Doe",
            "email_address": "<EMAIL>"
        },
        "enabled_assets": ["us_equity"]
    }

    # Try all endpoints
    success = False
    for url in endpoints:
        print(f"\n\n==== TESTING ENDPOINT: {url} ====")
        print("\n==== ALPACA API TEST REQUEST ====")
        print("Request URL:", url)
        print("Request Headers (masked):", json.dumps({k: (v[:5] + "..." if k.lower().find("key") >= 0 else v) for k, v in headers.items()}, indent=2))
        print("Request Payload:", json.dumps(payload, indent=2))

        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)

            print("\n==== ALPACA API TEST RESPONSE ====")
            print("Response Status Code:", response.status_code)
            print("Response Headers:", json.dumps(dict(response.headers), indent=2))

            try:
                response_json = response.json()
                print("Response Body:", json.dumps(response_json, indent=2))

                # If successful, no need to try other endpoints
                if response.status_code in [200, 201]:
                    print(f"\n✅ SUCCESS with endpoint: {url}")
                    success = True
                    break

            except:
                print("Response Body (not JSON):", response.text)

        except Exception as e:
            print("\n==== ALPACA API TEST ERROR ====")
            print(f"Error: {str(e)}")
            import traceback
            print(traceback.format_exc())
            
    if not success:
        print("\n❌ All endpoints failed. Please check the Alpaca API documentation for the correct endpoint.")

if __name__ == "__main__":
    test_alpaca_api()
