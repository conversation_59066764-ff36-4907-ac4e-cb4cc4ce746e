# KidVest Kid Profiles UI

This is a UI for managing kid profiles in the KidVest platform. It allows parents to create, view, edit, and delete profiles for their children.

## Features

- Create new kid profiles with name, age, handle, bio, and visibility settings
- View a list of all kid profiles
- View detailed information about each profile
- Edit existing profiles
- Delete profiles
- View the gift wall URL for sharing

## Setup

1. Make sure your FastAPI backend is running on `http://localhost:8000`
2. Start the UI server:

```bash
python server.py
```

3. Open your browser and navigate to `http://localhost:8081`

## Using the Kid Profiles UI

### Creating a Profile

1. Click the "+ New Profile" button
2. Fill in the required information:
   - Name: Your child's name
   - Age: Your child's age (optional)
   - Handle: A unique username for your child's profile (will be used in the gift wall URL)
   - Bio: A short description (optional)
   - Public/Private: Whether the profile is visible to others
3. Click "Save Profile"

### Editing a Profile

1. Select a profile from the list on the left
2. Click the "Edit Profile" button
3. Make your changes
4. Click "Save Profile"

### Deleting a Profile

1. Select a profile from the list on the left
2. Click the "Delete" button
3. Confirm the deletion

### Viewing the Gift Wall

1. Select a profile from the list on the left
2. Click the "View Gift Wall" button
3. The gift wall will open in a new tab

## API Integration

This UI integrates with the following API endpoints:

- `GET /api/profiles/`: Get all profiles
- `POST /api/profiles/`: Create a new profile
- `GET /api/profiles/{profile_id}`: Get a specific profile
- `PUT /api/profiles/{profile_id}`: Update a profile
- `DELETE /api/profiles/{profile_id}`: Delete a profile

## Troubleshooting

- If you see "Failed to load profiles", check that your FastAPI backend is running
- If you encounter CORS issues, make sure your FastAPI backend has CORS enabled
- If you can't create a profile with a specific handle, it may already be in use
