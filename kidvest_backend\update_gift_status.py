from sqlalchemy.orm import Session
from app.database import SessionLocal
from app import models, crud
from uuid import UUID

def update_gift_status():
    """Directly update the gift payment status in the database"""
    print("=== Updating Gift Payment Status ===")
    
    # Get the gift ID from the user
    gift_id_str = input("Enter the gift ID to update: ")
    
    try:
        # Convert to UUID
        gift_id = UUID(gift_id_str)
        
        # Open a database session
        db = SessionLocal()
        
        try:
            # Get the gift
            gift = db.query(models.Gift).filter(models.Gift.id == gift_id).first()
            
            if gift:
                print(f"Found gift: {gift.id}")
                print(f"Current payment status: {gift.payment_status}")
                
                # Update the payment status
                gift.payment_status = "completed"
                gift.payment_intent_id = f"pi_manual_update_{gift_id.hex[:8]}"
                
                # Commit the changes
                db.commit()
                
                # Verify the update
                db.refresh(gift)
                print(f"Updated payment status: {gift.payment_status}")
                print(f"Payment intent ID: {gift.payment_intent_id}")
                
                print("\n✅ Gift payment status updated successfully!")
            else:
                print(f"❌ Gift not found with ID: {gift_id}")
        except Exception as e:
            print(f"❌ Error updating gift: {str(e)}")
            db.rollback()
        finally:
            db.close()
    except ValueError:
        print(f"❌ Invalid UUID format: {gift_id_str}")

if __name__ == "__main__":
    update_gift_status()
