<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KidVest - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
        }
        .container {
            max-width: 1200px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            background-color: #4a6cf7;
            color: white;
            font-weight: bold;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }
        .btn-primary {
            background-color: #4a6cf7;
            border-color: #4a6cf7;
        }
        .navbar {
            background-color: #4a6cf7;
            margin-bottom: 30px;
        }
        .navbar-brand {
            color: white;
            font-weight: bold;
        }
        .nav-link {
            color: rgba(255, 255, 255, 0.8);
        }
        .nav-link:hover {
            color: white;
        }
        .feature-icon {
            font-size: 2.5rem;
            color: #4a6cf7;
            margin-bottom: 15px;
        }
        .welcome-card {
            background-color: #4a6cf7;
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .welcome-card h2 {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">KidVest</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/dashboard">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/child-profile">Child Profiles</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/gift-wall">Gift Walls</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <span class="navbar-text me-3" id="user-name">Welcome, User</span>
                    <button class="btn btn-outline-light btn-sm" id="logout-btn">Logout</button>
                </div>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- Welcome Card -->
        <div class="welcome-card">
            <h2>Welcome to KidVest!</h2>
            <p class="mb-0">Your platform for building your child's financial future through investments and gifts.</p>
        </div>

        <!-- Main Features -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="bi bi-person-plus"></i>
                        </div>
                        <h5 class="card-title">Child Profiles</h5>
                        <p class="card-text">Create and manage profiles for your children to start their investment journey.</p>
                        <a href="javascript:void(0)" class="btn btn-primary" id="manage-profiles-btn">Manage Profiles</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="bi bi-gift"></i>
                        </div>
                        <h5 class="card-title">Gift Walls</h5>
                        <p class="card-text">Share your child's gift wall with friends and family to receive monetary gifts.</p>
                        <a href="/gift-wall" class="btn btn-primary">View Gift Walls</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="bi bi-graph-up"></i>
                        </div>
                        <h5 class="card-title">Investments</h5>
                        <p class="card-text">Invest gifts in stocks and ETFs to grow your child's financial portfolio.</p>
                        <a href="#" class="btn btn-primary">Manage Investments</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-grid gap-2">
                                    <a href="javascript:void(0)" class="btn btn-outline-primary" id="add-child-profile-btn">
                                        <i class="bi bi-person-plus me-2"></i> Add Child Profile
                                    </a>
                                    <a href="/onboarding" class="btn btn-outline-primary">
                                        <i class="bi bi-bank me-2"></i> Complete KYC Onboarding
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-grid gap-2">
                                    <a href="#" class="btn btn-outline-primary">
                                        <i class="bi bi-cash-coin me-2"></i> Make an Investment
                                    </a>
                                    <a href="#" class="btn btn-outline-primary">
                                        <i class="bi bi-share me-2"></i> Share Gift Wall
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/shared/state-manager.js"></script>
    <script src="script.js"></script>
</body>
</html>
