<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KidVest Gift Wall</title>
    <link rel="stylesheet" href="/static/styles.css">
</head>
<body>
    <div class="app-container">
        <header>
            <div class="logo">
                <h1>KidVest</h1>
            </div>
        </header>

        <main>
            <div class="profile-container" id="profile-container">
                <div class="loading">Loading profile...</div>
            </div>

            <div class="gifts-container">
                <div class="gifts-header">
                    <h2>Gift Wall</h2>
                    <button id="send-gift-btn" class="btn btn-primary">Send a Gift</button>
                </div>
                <div class="gifts-list" id="gifts-list">
                    <div class="loading">Loading gifts...</div>
                </div>
            </div>
        </main>

        <div class="modal" id="gift-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Send a Gift</h3>
                    <button class="close-btn" id="close-modal">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="gift-form">
                        <div class="gift-form-header">
                            <div class="profile-avatar" id="modal-avatar"></div>
                            <div class="profile-info">
                                <div class="profile-name" id="modal-name"></div>
                                <div class="profile-handle" id="modal-handle"></div>
                            </div>
                            <button type="button" class="gift-btn" disabled>Gift</button>
                        </div>

                        <div class="form-group message-group">
                            <textarea id="message" name="message" rows="2" placeholder="Write a message"></textarea>
                        </div>

                        <div class="form-group amount-group">
                            <label for="amount">Amount</label>
                            <input type="text" id="amount" name="amount" placeholder="$50" value="50">
                        </div>

                        <div class="form-group">
                            <label for="from_name">Your Name</label>
                            <input type="text" id="from_name" name="from_name" required>
                        </div>

                        <div class="form-group">
                            <label for="from_email">Your Email</label>
                            <input type="email" id="from_email" name="from_email" required>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="is_anonymous" name="is_anonymous">
                                Make this gift anonymous
                            </label>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" id="cancel-btn">Cancel</button>
                            <button type="submit" class="btn btn-primary" id="submit-gift-btn">Post & Gift</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="toast" id="toast">
            <div class="toast-content">
                <span id="toast-message"></span>
                <button class="close-toast">&times;</button>
            </div>
        </div>
    </div>

    <script src="/static/mock_data.js"></script>
    <script src="/static/script.js"></script>
</body>
</html>
