from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
from dotenv import load_dotenv
import sys

# Add the current directory to the path so we can import app modules
sys.path.append('.')

# Load environment variables
load_dotenv()

# Database connection
DATABASE_URL = os.getenv("DATABASE_URL")
print(f"Using database URL: {DATABASE_URL}")

# Create engine
engine = create_engine(DATABASE_URL)

# Import models
from app import models

def reset_database():
    """Reset the database by dropping all tables and recreating them"""
    print("=== Database Reset Script ===")
    
    try:
        # Drop all tables
        print("Dropping all tables...")
        models.Base.metadata.drop_all(engine)
        print("All tables dropped successfully")
        
        # Create all tables
        print("Creating all tables...")
        models.Base.metadata.create_all(engine)
        print("All tables created successfully")
        
        print("=== Database reset completed successfully! ===")
    except Exception as e:
        print(f"Error during database reset: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Ask for confirmation
    confirm = input("This will DELETE ALL DATA in the database. Are you sure? (y/n): ")
    if confirm.lower() == 'y':
        reset_database()
        print("\nDone.")
    else:
        print("Operation cancelled.")
