import requests
import json

def test_register_direct():
    url = "http://localhost:8000/api/register"
    data = {
        "name": "Test User Direct",
        "email": "<EMAIL>",
        "password": "password123",
        "user_type": "parent",
        "phone_number": None,
        "address": None,
        "city": None,
        "state": None,
        "postal_code": None,
        "country": None
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"Sending direct request to {url}")
        print(f"Request data: {json.dumps(data)}")
        
        response = requests.post(url, json=data, headers=headers)
        
        print(f"Response status code: {response.status_code}")
        print(f"Response headers: {response.headers}")
        
        try:
            print(f"Response body: {response.json()}")
        except:
            print(f"Response body (raw): {response.text}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("Testing direct API connection...")
    success = test_register_direct()
    print(f"Test {'succeeded' if success else 'failed'}")
