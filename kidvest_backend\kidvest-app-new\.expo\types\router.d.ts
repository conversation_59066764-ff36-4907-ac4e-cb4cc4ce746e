/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/modal`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/gifts` | `/gifts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profiles` | `/profiles`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/relationships` | `/relationships`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/two` | `/two`; params?: Router.UnknownInputParams; } | { pathname: `/auth/login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/logout`; params?: Router.UnknownInputParams; } | { pathname: `/auth/register`; params?: Router.UnknownInputParams; } | { pathname: `/gifts/cancel`; params?: Router.UnknownInputParams; } | { pathname: `/gifts/create`; params?: Router.UnknownInputParams; } | { pathname: `/gifts/success`; params?: Router.UnknownInputParams; } | { pathname: `/invest/manual-simple`; params?: Router.UnknownInputParams; } | { pathname: `/investments/create`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/child-profile`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/complete`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/kyc`; params?: Router.UnknownInputParams; } | { pathname: `/portfolio/overview`; params?: Router.UnknownInputParams; } | { pathname: `/relationships`; params?: Router.UnknownInputParams; } | { pathname: `/relationships/request`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/portfolio/[childId]`, params: Router.UnknownInputParams & { childId: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/modal`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/gifts` | `/gifts`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profiles` | `/profiles`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/relationships` | `/relationships`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/two` | `/two`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/login`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/logout`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/register`; params?: Router.UnknownOutputParams; } | { pathname: `/gifts/cancel`; params?: Router.UnknownOutputParams; } | { pathname: `/gifts/create`; params?: Router.UnknownOutputParams; } | { pathname: `/gifts/success`; params?: Router.UnknownOutputParams; } | { pathname: `/invest/manual-simple`; params?: Router.UnknownOutputParams; } | { pathname: `/investments/create`; params?: Router.UnknownOutputParams; } | { pathname: `/onboarding/child-profile`; params?: Router.UnknownOutputParams; } | { pathname: `/onboarding/complete`; params?: Router.UnknownOutputParams; } | { pathname: `/onboarding/kyc`; params?: Router.UnknownOutputParams; } | { pathname: `/portfolio/overview`; params?: Router.UnknownOutputParams; } | { pathname: `/relationships`; params?: Router.UnknownOutputParams; } | { pathname: `/relationships/request`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } } | { pathname: `/portfolio/[childId]`, params: Router.UnknownOutputParams & { childId: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/modal${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/gifts${`?${string}` | `#${string}` | ''}` | `/gifts${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profiles${`?${string}` | `#${string}` | ''}` | `/profiles${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/relationships${`?${string}` | `#${string}` | ''}` | `/relationships${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/settings${`?${string}` | `#${string}` | ''}` | `/settings${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/two${`?${string}` | `#${string}` | ''}` | `/two${`?${string}` | `#${string}` | ''}` | `/auth/login${`?${string}` | `#${string}` | ''}` | `/auth/logout${`?${string}` | `#${string}` | ''}` | `/auth/register${`?${string}` | `#${string}` | ''}` | `/gifts/cancel${`?${string}` | `#${string}` | ''}` | `/gifts/create${`?${string}` | `#${string}` | ''}` | `/gifts/success${`?${string}` | `#${string}` | ''}` | `/invest/manual-simple${`?${string}` | `#${string}` | ''}` | `/investments/create${`?${string}` | `#${string}` | ''}` | `/onboarding/child-profile${`?${string}` | `#${string}` | ''}` | `/onboarding/complete${`?${string}` | `#${string}` | ''}` | `/onboarding/kyc${`?${string}` | `#${string}` | ''}` | `/portfolio/overview${`?${string}` | `#${string}` | ''}` | `/relationships${`?${string}` | `#${string}` | ''}` | `/relationships/request${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/modal`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/gifts` | `/gifts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profiles` | `/profiles`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/relationships` | `/relationships`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/two` | `/two`; params?: Router.UnknownInputParams; } | { pathname: `/auth/login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/logout`; params?: Router.UnknownInputParams; } | { pathname: `/auth/register`; params?: Router.UnknownInputParams; } | { pathname: `/gifts/cancel`; params?: Router.UnknownInputParams; } | { pathname: `/gifts/create`; params?: Router.UnknownInputParams; } | { pathname: `/gifts/success`; params?: Router.UnknownInputParams; } | { pathname: `/invest/manual-simple`; params?: Router.UnknownInputParams; } | { pathname: `/investments/create`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/child-profile`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/complete`; params?: Router.UnknownInputParams; } | { pathname: `/onboarding/kyc`; params?: Router.UnknownInputParams; } | { pathname: `/portfolio/overview`; params?: Router.UnknownInputParams; } | { pathname: `/relationships`; params?: Router.UnknownInputParams; } | { pathname: `/relationships/request`; params?: Router.UnknownInputParams; } | `/+not-found` | `/portfolio/${Router.SingleRoutePart<T>}` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/portfolio/[childId]`, params: Router.UnknownInputParams & { childId: string | number; } };
    }
  }
}
