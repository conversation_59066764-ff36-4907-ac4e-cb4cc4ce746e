import React, { useState, useEffect } from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, TextInput, ActivityIndicator, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useRouter, useLocalSearchParams } from 'expo-router';

import { Text, View } from '@/components/Themed';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';
import { useAuth } from '../../context/AuthContext';
import { portfolioAPI } from '../../services/api';

const POPULAR_STOCKS = [
  { symbol: 'AAPL', name: 'Apple Inc.' },
  { symbol: 'GOOGL', name: 'Alphabet Inc.' },
  { symbol: 'MSFT', name: 'Microsoft Corp.' },
  { symbol: 'TSLA', name: 'Tesla Inc.' },
  { symbol: 'AMZN', name: 'Amazon.com Inc.' },
  { symbol: 'NVDA', name: 'NVIDIA Corp.' },
  { symbol: 'META', name: 'Meta Platforms Inc.' },
  { symbol: 'NFLX', name: 'Netflix Inc.' },
];

export default function CreateInvestmentScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { giftId, maxAmount, childId } = useLocalSearchParams();
  const { user } = useAuth();
  
  const [isLoading, setIsLoading] = useState(false);
  const [symbol, setSymbol] = useState('');
  const [amount, setAmount] = useState('');
  const [errors, setErrors] = useState({});

  const maxAmountNum = parseFloat(maxAmount as string) || 0;

  const validateForm = () => {
    const newErrors = {};
    
    if (!symbol.trim()) {
      newErrors.symbol = 'Stock symbol is required';
    } else if (!/^[A-Z]{1,5}$/.test(symbol.trim().toUpperCase())) {
      newErrors.symbol = 'Please enter a valid stock symbol (1-5 letters)';
    }
    
    if (!amount.trim()) {
      newErrors.amount = 'Investment amount is required';
    } else {
      const amountNum = parseFloat(amount);
      if (isNaN(amountNum) || amountNum <= 0) {
        newErrors.amount = 'Please enter a valid amount';
      } else if (amountNum > maxAmountNum) {
        newErrors.amount = `Amount cannot exceed $${maxAmountNum.toFixed(2)}`;
      } else if (amountNum < 1) {
        newErrors.amount = 'Minimum investment is $1.00';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    
    try {
      await portfolioAPI.createInvestmentFromGift(giftId as string, {
        symbol: symbol.trim().toUpperCase(),
        amount_usd: parseFloat(amount),
      });

      Alert.alert(
        'Investment Created!',
        `Successfully invested $${amount} in ${symbol.toUpperCase()}`,
        [
          {
            text: 'View Portfolio',
            onPress: () => router.replace(`/portfolio/${childId}`)
          }
        ]
      );
    } catch (error) {
      console.error('Error creating investment:', error);
      Alert.alert(
        'Investment Failed',
        error.response?.data?.detail || 'Failed to create investment. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleStockSelect = (stockSymbol: string) => {
    setSymbol(stockSymbol);
    setErrors({ ...errors, symbol: undefined });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <FontAwesome5 name="arrow-left" size={20} color={Colors[colorScheme].text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Create Investment</Text>
          <View style={{ width: 40 }} />
        </View>

        {/* Available Amount */}
        <View style={[styles.availableCard, { backgroundColor: Colors[colorScheme].cardBackground }]}>
          <FontAwesome5 name="wallet" size={24} color={Colors[colorScheme].primary} />
          <Text style={styles.availableLabel}>Available to Invest</Text>
          <Text style={[styles.availableAmount, { color: Colors[colorScheme].primary }]}>
            {formatCurrency(maxAmountNum)}
          </Text>
        </View>

        {/* Stock Symbol Input */}
        <View style={[styles.inputSection, { backgroundColor: Colors[colorScheme].cardBackground }]}>
          <Text style={styles.inputLabel}>Stock Symbol</Text>
          <TextInput
            style={[
              styles.input,
              { borderColor: errors.symbol ? '#F44336' : '#ddd' }
            ]}
            placeholder="e.g., AAPL, GOOGL, TSLA"
            value={symbol}
            onChangeText={(text) => {
              setSymbol(text.toUpperCase());
              if (errors.symbol) {
                setErrors({ ...errors, symbol: undefined });
              }
            }}
            autoCapitalize="characters"
            maxLength={5}
          />
          {errors.symbol && (
            <Text style={styles.errorText}>{errors.symbol}</Text>
          )}
        </View>

        {/* Popular Stocks */}
        <View style={[styles.popularSection, { backgroundColor: Colors[colorScheme].cardBackground }]}>
          <Text style={styles.popularTitle}>Popular Stocks</Text>
          <View style={styles.stockGrid}>
            {POPULAR_STOCKS.map((stock, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.stockButton,
                  { 
                    backgroundColor: symbol === stock.symbol ? Colors[colorScheme].primary : '#f5f5f5',
                    borderColor: symbol === stock.symbol ? Colors[colorScheme].primary : '#ddd'
                  }
                ]}
                onPress={() => handleStockSelect(stock.symbol)}
              >
                <Text style={[
                  styles.stockSymbol,
                  { color: symbol === stock.symbol ? 'white' : Colors[colorScheme].text }
                ]}>
                  {stock.symbol}
                </Text>
                <Text style={[
                  styles.stockName,
                  { color: symbol === stock.symbol ? 'white' : '#666' }
                ]}>
                  {stock.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Amount Input */}
        <View style={[styles.inputSection, { backgroundColor: Colors[colorScheme].cardBackground }]}>
          <Text style={styles.inputLabel}>Investment Amount</Text>
          <View style={styles.amountInputContainer}>
            <Text style={styles.dollarSign}>$</Text>
            <TextInput
              style={[
                styles.amountInput,
                { borderColor: errors.amount ? '#F44336' : '#ddd' }
              ]}
              placeholder="0.00"
              value={amount}
              onChangeText={(text) => {
                // Only allow numbers and decimal point
                const cleanText = text.replace(/[^0-9.]/g, '');
                // Ensure only one decimal point
                const parts = cleanText.split('.');
                if (parts.length > 2) {
                  return;
                }
                setAmount(cleanText);
                if (errors.amount) {
                  setErrors({ ...errors, amount: undefined });
                }
              }}
              keyboardType="decimal-pad"
              maxLength={10}
            />
          </View>
          {errors.amount && (
            <Text style={styles.errorText}>{errors.amount}</Text>
          )}
          
          {/* Quick Amount Buttons */}
          <View style={styles.quickAmounts}>
            {[25, 50, 100].filter(amt => amt <= maxAmountNum).map((amt) => (
              <TouchableOpacity
                key={amt}
                style={[styles.quickAmountButton, { borderColor: Colors[colorScheme].primary }]}
                onPress={() => {
                  setAmount(amt.toString());
                  if (errors.amount) {
                    setErrors({ ...errors, amount: undefined });
                  }
                }}
              >
                <Text style={[styles.quickAmountText, { color: Colors[colorScheme].primary }]}>
                  ${amt}
                </Text>
              </TouchableOpacity>
            ))}
            {maxAmountNum > 0 && (
              <TouchableOpacity
                style={[styles.quickAmountButton, { borderColor: Colors[colorScheme].primary }]}
                onPress={() => {
                  setAmount(maxAmountNum.toFixed(2));
                  if (errors.amount) {
                    setErrors({ ...errors, amount: undefined });
                  }
                }}
              >
                <Text style={[styles.quickAmountText, { color: Colors[colorScheme].primary }]}>
                  Max
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Investment Summary */}
        {symbol && amount && !errors.symbol && !errors.amount && (
          <View style={[styles.summarySection, { backgroundColor: Colors[colorScheme].cardBackground }]}>
            <Text style={styles.summaryTitle}>Investment Summary</Text>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Stock Symbol:</Text>
              <Text style={styles.summaryValue}>{symbol.toUpperCase()}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Investment Amount:</Text>
              <Text style={styles.summaryValue}>{formatCurrency(parseFloat(amount))}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Remaining Balance:</Text>
              <Text style={styles.summaryValue}>
                {formatCurrency(maxAmountNum - parseFloat(amount))}
              </Text>
            </View>
          </View>
        )}

        {/* Submit Button */}
        <TouchableOpacity
          style={[
            styles.submitButton,
            { 
              backgroundColor: Colors[colorScheme].primary,
              opacity: isLoading ? 0.7 : 1
            }
          ]}
          onPress={handleSubmit}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="white" />
          ) : (
            <>
              <FontAwesome5 name="chart-line" size={16} color="white" />
              <Text style={styles.submitButtonText}>Create Investment</Text>
            </>
          )}
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 60,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  availableCard: {
    margin: 16,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  availableLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    marginBottom: 4,
  },
  availableAmount: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  inputSection: {
    margin: 16,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: 'white',
  },
  errorText: {
    color: '#F44336',
    fontSize: 12,
    marginTop: 4,
  },
  popularSection: {
    margin: 16,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  popularTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  stockGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  stockButton: {
    width: '48%',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
    alignItems: 'center',
  },
  stockSymbol: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  stockName: {
    fontSize: 10,
    textAlign: 'center',
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    backgroundColor: 'white',
  },
  dollarSign: {
    fontSize: 16,
    fontWeight: 'bold',
    paddingLeft: 12,
    color: '#666',
  },
  amountInput: {
    flex: 1,
    padding: 12,
    fontSize: 16,
    borderWidth: 0,
  },
  quickAmounts: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 12,
  },
  quickAmountButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderRadius: 6,
    backgroundColor: 'white',
  },
  quickAmountText: {
    fontSize: 12,
    fontWeight: '600',
  },
  summarySection: {
    margin: 16,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});
