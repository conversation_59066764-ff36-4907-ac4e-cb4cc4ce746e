import http.server
import socketserver
import os
import re

PORT = 8082
DIRECTORY = os.path.dirname(os.path.abspath(__file__))

class GiftWallHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)

    def do_GET(self):
        # Check if the path is for a static file
        if self.path.endswith('.css') or self.path.endswith('.js'):
            # Strip any directory path and serve from root
            self.path = '/' + os.path.basename(self.path)
        else:
            # Check if the path matches a gift wall pattern
            wall_pattern = r'^/wall/([a-zA-Z0-9_-]+)/?$'
            success_pattern = r'^/wall/([a-zA-Z0-9_-]+)/success'
            cancel_pattern = r'^/wall/([a-zA-Z0-9_-]+)/cancel'

            if re.match(wall_pattern, self.path) or re.match(success_pattern, self.path) or re.match(cancel_pattern, self.path):
                # Serve the index.html file for all gift wall paths
                self.path = '/index.html'

        return super().do_GET()

    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

if __name__ == "__main__":
    # Use 0.0.0.0 to listen on all interfaces
    with socketserver.TCPServer(("0.0.0.0", PORT), GiftWallHandler) as httpd:
        print(f"Serving Gift Wall UI at http://localhost:{PORT}")
        print(f"Example gift wall: http://localhost:{PORT}/wall/zohaib_ali")
        print(f"Also accessible at http://127.0.0.1:{PORT}/wall/zohaib_ali")
        print("\nIf you're using ngrok, make sure it's running in a separate window.")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nShutting down server...")
            httpd.server_close()
