<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gift API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            cursor: pointer;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Gift API Test</h1>
    
    <form id="giftForm">
        <div class="form-group">
            <label for="handle">Child Profile Handle:</label>
            <input type="text" id="handle" value="jane_doe" required>
        </div>
        
        <div class="form-group">
            <label for="from_name">From Name:</label>
            <input type="text" id="from_name" value="Test Gifter" required>
        </div>
        
        <div class="form-group">
            <label for="from_email">From Email:</label>
            <input type="email" id="from_email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="amount">Amount (USD):</label>
            <input type="number" id="amount" value="50" required>
        </div>
        
        <div class="form-group">
            <label for="message">Message:</label>
            <textarea id="message">Test gift message</textarea>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="is_anonymous">
                Anonymous Gift
            </label>
        </div>
        
        <button type="submit">Create Gift</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('giftForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Processing...';
            
            const handle = document.getElementById('handle').value;
            
            const formData = {
                child_profile_handle: handle,
                from_name: document.getElementById('from_name').value,
                from_email: document.getElementById('from_email').value,
                amount_usd: parseInt(document.getElementById('amount').value),
                message: document.getElementById('message').value,
                is_anonymous: document.getElementById('is_anonymous').checked
            };
            
            try {
                const apiUrl = `http://127.0.0.1:8000/api/wall/${handle}/gift`;
                
                resultDiv.innerHTML += `\nSending request to: ${apiUrl}`;
                resultDiv.innerHTML += `\nWith data: ${JSON.stringify(formData, null, 2)}`;
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                resultDiv.innerHTML += `\n\nResponse status: ${response.status}`;
                
                const responseText = await response.text();
                resultDiv.innerHTML += `\nResponse text: ${responseText}`;
                
                try {
                    const data = JSON.parse(responseText);
                    resultDiv.innerHTML += `\n\nParsed response: ${JSON.stringify(data, null, 2)}`;
                    
                    if (data.checkout_url) {
                        resultDiv.innerHTML += `\n\nCheckout URL found! Redirecting in 3 seconds...`;
                        setTimeout(() => {
                            window.location.href = data.checkout_url;
                        }, 3000);
                    }
                } catch (e) {
                    resultDiv.innerHTML += `\n\nError parsing response: ${e.message}`;
                }
            } catch (error) {
                resultDiv.innerHTML += `\n\nError: ${error.message}`;
            }
        });
    </script>
</body>
</html>
