import os
import json
import stripe
from uuid import UUID
from typing import List, Optional, Dict, Any, Union
from datetime import <PERSON><PERSON><PERSON>
from dotenv import load_dotenv
from fastapi import FastAPI, Depends, Request, HTTPException, Query, status
from fastapi.responses import JSONResponse, HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from app.database import SessionLocal, engine, Base
from app import models, schemas, crud, auth
from app.alpaca_service import onboard_alpaca_user
from app.onboarding_service import (
    process_step1, process_step2, process_step3, process_step4, submit_to_alpaca
)
from uuid import uuid4, UUID
from typing import List, Optional
import traceback
import sys
# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    print(f"Global exception handler caught: {str(exc)}")
    traceback.print_exc(file=sys.stdout)
    return JSONResponse(
        status_code=500,
        content={"detail": f"Internal Server Error: {str(exc)}"}
    )

# Dependency for DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Authentication endpoints
@app.post("/api/register", response_model=schemas.UserResponse)
def register_user(user_data: schemas.UserCreate, db: Session = Depends(get_db)):
    print(f"\n=== Registration Request ===\nData: {user_data}")

    # Check if user already exists
    db_user = db.query(models.User).filter(models.User.email == user_data.email).first()
    if db_user:
        print(f"Email already registered: {user_data.email}")
        raise HTTPException(status_code=400, detail="Email already registered")

    # Create new user with hashed password
    hashed_password = auth.get_password_hash(user_data.password)
    db_user = models.User(
        name=user_data.name,
        email=user_data.email,
        hashed_password=hashed_password,
        user_type=user_data.user_type,
        phone_number=user_data.phone_number,
        address=user_data.address,
        city=user_data.city,
        state=user_data.state,
        postal_code=user_data.postal_code,
        country=user_data.country,
        is_active=True
    )

    try:
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        print(f"User registered successfully: {db_user.id} ({db_user.email})")
        return db_user
    except Exception as e:
        db.rollback()
        print(f"Error registering user: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error registering user: {str(e)}")

@app.post("/api/token", response_model=auth.Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = auth.authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=auth.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = auth.create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )

    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/api/users/me", response_model=schemas.UserResponse)
async def read_users_me(current_user: models.User = Depends(auth.get_current_active_user)):
    return current_user

# Setup Stripe
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
print("✅ Stripe Key Loaded:", stripe.api_key)

# ✅ Ensure all tables are created (including broker_accounts)
Base.metadata.create_all(bind=engine)

# Mount static files for gift wall UI
gift_wall_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "gift-wall-ui")
if os.path.exists(gift_wall_path):
    app.mount("/static", StaticFiles(directory=gift_wall_path), name="static")
    print(f"✅ Mounted gift wall static files from: {gift_wall_path}")
else:
    print(f"⚠️ Gift wall directory not found: {gift_wall_path}")

# Gift Wall Routes
@app.get("/wall/{handle}", response_class=HTMLResponse)
async def serve_gift_wall(handle: str):
    """Serve the gift wall HTML page"""
    gift_wall_html = os.path.join(gift_wall_path, "index.html")
    if os.path.exists(gift_wall_html):
        with open(gift_wall_html, "r", encoding="utf-8") as f:
            html_content = f.read()
        return HTMLResponse(content=html_content)
    else:
        raise HTTPException(status_code=404, detail="Gift wall not found")

@app.get("/wall/{handle}/success", response_class=HTMLResponse)
async def serve_gift_wall_success(handle: str):
    """Serve the gift wall success page"""
    return HTMLResponse(content=f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Payment Successful - KidVest</title>
        <style>
            body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }}
            .container {{ max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .success-icon {{ font-size: 64px; color: #4CAF50; margin-bottom: 20px; }}
            h1 {{ color: #333; margin-bottom: 20px; }}
            p {{ color: #666; margin-bottom: 30px; line-height: 1.6; }}
            .btn {{ background: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; }}
            .btn:hover {{ background: #45a049; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="success-icon">✓</div>
            <h1>Thank You!</h1>
            <p>Your gift has been successfully processed and will appear on the gift wall shortly.</p>
            <a href="/wall/{handle}" class="btn">Return to Gift Wall</a>
        </div>
    </body>
    </html>
    """)

@app.get("/wall/{handle}/cancel", response_class=HTMLResponse)
async def serve_gift_wall_cancel(handle: str):
    """Serve the gift wall cancel page"""
    return HTMLResponse(content=f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Payment Cancelled - KidVest</title>
        <style>
            body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }}
            .container {{ max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .cancel-icon {{ font-size: 64px; color: #f44336; margin-bottom: 20px; }}
            h1 {{ color: #333; margin-bottom: 20px; }}
            p {{ color: #666; margin-bottom: 30px; line-height: 1.6; }}
            .btn {{ background: #2196F3; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; }}
            .btn:hover {{ background: #1976D2; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="cancel-icon">×</div>
            <h1>Payment Cancelled</h1>
            <p>Your payment was cancelled. No charges were made to your card.</p>
            <a href="/wall/{handle}" class="btn">Return to Gift Wall</a>
        </div>
    </body>
    </html>
    """)

# This function has been moved to the top of the file

# Use the authentication functions from auth.py
# The old temporary function has been replaced with proper authentication

# Root route
@app.get("/")
def read_root():
    return {"message": "Welcome to Kidvest Backend!"}

# Create user
@app.post("/users/", response_model=schemas.UserResponse)
def create_user(user: schemas.UserCreate, db: Session = Depends(get_db)):
    return crud.create_user(db=db, user=user)

# Gift API Endpoints
@app.post("/gifts/", response_model=schemas.GiftResponse)
def create_gift(gift: schemas.GiftCreate, db: Session = Depends(get_db)):
    """Create a new gift (admin endpoint)"""
    return crud.create_gift(db=db, gift=gift)

@app.get("/gifts/{gift_id}", response_model=schemas.GiftResponse)
def get_gift(gift_id: UUID, db: Session = Depends(get_db)):
    """Get a gift by ID"""
    db_gift = crud.get_gift(db, gift_id)
    if not db_gift:
        raise HTTPException(status_code=404, detail="Gift not found")
    return db_gift

# Gift Wall API Endpoints
@app.get("/api/wall/{handle}", response_model=schemas.GiftWallProfileResponse)
def get_gift_wall(handle: str, db: Session = Depends(get_db)):
    """Get a child's gift wall"""
    # Get the child profile
    child_profile = crud.get_child_profile_by_handle(db, handle)
    if not child_profile:
        raise HTTPException(status_code=404, detail="Profile not found")

    # Check if the profile is public
    if not child_profile.is_public:
        raise HTTPException(status_code=404, detail="Profile not found")

    # Get the gifts for this profile
    gifts = crud.get_gifts_by_child_profile(db, child_profile.id)

    # Format the gifts for the wall
    gift_wall_items = []
    for gift in gifts:
        # Skip gifts that aren't completed
        if gift.payment_status != "completed":
            continue

        gift_item = {
            "id": gift.id,
            "amount_usd": gift.amount_usd,
            "message": gift.message,
            "created_at": gift.created_at
        }

        # Only include name if not anonymous
        if not gift.is_anonymous:
            gift_item["from_name"] = gift.from_name

        gift_wall_items.append(gift_item)

    return {
        "profile": child_profile,
        "gifts": gift_wall_items,
        "total_gifts": len(gift_wall_items),
        "total_amount": sum(gift.amount_usd for gift in gifts)
    }

@app.post("/api/wall/{handle}/gift", response_model=schemas.CheckoutSessionResponse)
async def create_wall_gift(handle: str, gift: schemas.GiftWallCreate, request: Request, db: Session = Depends(get_db)):
    """Create a new gift from the gift wall"""
    try:
        # Log detailed request information
        print(f"\n=== Gift Creation Request ===")
        print(f"Method: {request.method}")
        print(f"URL: {request.url}")
        print(f"Headers: {dict(request.headers)}")

        # Log the request body
        body = await request.body()
        print(f"Raw body: {body}")

        print(f"Parsed gift data: {gift.model_dump() if hasattr(gift, 'model_dump') else gift}")
        print(f"Handle parameter: {handle}")

        # Override the handle from the URL
        gift.child_profile_handle = handle

        # Get the child profile
        child_profile = crud.get_child_profile_by_handle(db, handle)
        if not child_profile:
            print(f"Child profile not found for handle: {handle}")
            raise HTTPException(status_code=404, detail=f"Profile not found for handle: {handle}")

        print(f"Found child profile: {child_profile.name} (ID: {child_profile.id})")

        # TODO: Phase 2 - Add relationship checking here
        # For now, allow all gifts. In Phase 2, we'll check if the gifter has an active relationship
        # with the child before allowing gift creation.
        #
        # Future implementation:
        # if gift.from_email:
        #     gifter = crud.get_user_by_email(db, gift.from_email)
        #     if gifter:
        #         has_relationship = crud.check_user_child_relationship(db, gifter.id, child_profile.id)
        #         if not has_relationship:
        #             raise HTTPException(status_code=403, detail="You must have an approved relationship with this child to send gifts")

        # Create the gift
        db_gift = models.Gift(
            child_profile_id=child_profile.id,
            from_name=gift.from_name,
            from_email=gift.from_email,
            amount_usd=gift.amount_usd,
            message=gift.message,
            is_anonymous=gift.is_anonymous,
            payment_status="pending"
        )

        db.add(db_gift)
        db.commit()
        db.refresh(db_gift)

        print(f"Gift created with ID: {db_gift.id}")

        # Create a Stripe checkout session
        stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
        print(f"Using Stripe API key: {stripe.api_key[:5] if stripe.api_key else 'None'}...")

        try:
            # Validate Stripe API key
            if not stripe.api_key or stripe.api_key.strip() == "":
                raise ValueError("Stripe API key is missing or empty")

            print(f"Creating Stripe checkout session with amount: {int(gift.amount_usd * 100)} cents")

            checkout_session = stripe.checkout.Session.create(
                payment_method_types=["card"],
                line_items=[{
                    "price_data": {
                        "currency": "usd",
                        "unit_amount": int(gift.amount_usd * 100),
                        "product_data": {
                            "name": f"Gift to {child_profile.name}",
                            "description": gift.message or "No message"
                        },
                    },
                    "quantity": 1,
                }],
                mode="payment",
                success_url=f"{os.getenv('FRONTEND_URL', 'http://localhost:8081')}/gifts/success?gift_id={db_gift.id}&child_handle={handle}",
                cancel_url=f"{os.getenv('FRONTEND_URL', 'http://localhost:8081')}/gifts/cancel?child_handle={handle}",
                metadata={"gift_id": str(db_gift.id)},
            )

            print(f"Stripe checkout session created: {checkout_session.id}")
            print(f"Checkout URL: {checkout_session.url}")

            # Update the gift with the checkout session ID
            db_gift.checkout_session_id = checkout_session.id
            db.commit()

            return {
                "success": True,
                "gift_id": str(db_gift.id),
                "checkout_url": checkout_session.url
            }
        except ValueError as ve:
            print(f"Validation error: {str(ve)}")
            db.delete(db_gift)
            db.commit()
            raise HTTPException(status_code=500, detail=f"Configuration error: {str(ve)}")
        except stripe.error.StripeError as se:
            print(f"Stripe error: {str(se)}")
            db.delete(db_gift)
            db.commit()
            raise HTTPException(status_code=500, detail=f"Stripe error: {str(se)}")
        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            print(f"Error type: {type(e).__name__}")
            import traceback
            traceback.print_exc()
            db.delete(db_gift)
            db.commit()
            raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        print(f"Error in create_wall_gift: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Server error: {str(e)}")

# Create Stripe checkout session
@app.post("/create-checkout-session/")
def create_checkout_session(gift_id: str, db: Session = Depends(get_db)):
    gift = db.query(models.Gift).filter(models.Gift.id == gift_id).first()

    if not gift:
        raise HTTPException(status_code=404, detail="Gift not found")

    if gift.payment_status == "completed":
        raise HTTPException(status_code=400, detail="Gift already paid")

    try:
        checkout_session = stripe.checkout.Session.create(
            payment_method_types=["card"],
            line_items=[{
                "price_data": {
                    "currency": "usd",
                    "unit_amount": int(gift.amount_usd * 100),
                    "product_data": {
                        "name": f"Gift to {gift.kid_id}",
                        "description": gift.message or "No message"
                    },
                },
                "quantity": 1,
            }],
            mode="payment",
            success_url="http://localhost:8000/success",
            cancel_url="http://localhost:8000/cancel",
            metadata={"gift_id": str(gift.id)},
        )

        return JSONResponse({"checkout_url": checkout_session.url})

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Webhook handler for Stripe events
@app.post("/webhook/")
async def stripe_webhook(request: Request, db: Session = Depends(get_db)):
    """Handle Stripe webhook events"""
    print("\n=== Stripe Webhook Received ===")

    # Get the request details
    payload = await request.body()
    sig_header = request.headers.get("stripe-signature")
    endpoint_secret = os.getenv("STRIPE_WEBHOOK_SECRET")

    print(f"Signature header: {sig_header}")
    print(f"Endpoint secret: {endpoint_secret[:5]}... (truncated)")

    # Log the raw payload for debugging
    print(f"Raw payload: {payload.decode('utf-8')[:100]}... (truncated)")

    try:
        # Verify the event
        if not endpoint_secret or endpoint_secret == "whsec_YOUR_NEW_SECRET_FROM_STRIPE_DASHBOARD":
            print("WARNING: No webhook secret configured or using placeholder, skipping signature verification")
            # Parse the payload manually for testing
            event_data = json.loads(payload)
            event = {
                "type": event_data.get("type"),
                "data": {"object": event_data.get("data", {}).get("object", {})}
            }
        else:
            # Verify the signature
            try:
                event = stripe.Webhook.construct_event(payload, sig_header, endpoint_secret)
                print("✅ Signature verified successfully")
            except stripe.error.SignatureVerificationError as e:
                print(f"❌ Signature verification failed: {str(e)}")
                print(f"⚠️ Falling back to manual parsing for testing...")
                # For development, fall back to manual parsing
                event_data = json.loads(payload)
                event = {
                    "type": event_data.get("type"),
                    "data": {"object": event_data.get("data", {}).get("object", {})}
                }
    except Exception as e:
        print(f"❌ Error parsing webhook: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=400, detail=f"Webhook error: {str(e)}")

    # Process the event
    print(f"Received Stripe webhook event: {event['type']}")

    try:
        if event["type"] == "checkout.session.completed":
            print("🎯 Checkout session completed!")
            session = event["data"]["object"]

            # Print session details for debugging
            print(f"Session ID: {session.get('id')}")
            print(f"Payment status: {session.get('payment_status')}")
            print(f"Metadata: {session.get('metadata')}")

            # Get the gift ID from the metadata
            gift_id = session.get("metadata", {}).get("gift_id")
            if not gift_id:
                print("❌ No gift ID found in metadata")
                return {"status": "error", "message": "No gift ID found in metadata"}

            print(f"Gift ID from metadata: {gift_id}")

            # Get the payment intent ID
            payment_intent_id = session.get("payment_intent")
            print(f"Payment intent ID: {payment_intent_id}")

            # Update the gift status
            try:
                gift = crud.get_gift(db, UUID(gift_id))
                if gift:
                    print(f"Found gift: {gift.id} (current status: {gift.payment_status})")
                    crud.update_gift_payment_status(
                        db,
                        UUID(gift_id),
                        "completed",
                        payment_intent_id=payment_intent_id
                    )
                    print(f"✅ Updated gift {gift_id} to completed")
                else:
                    print(f"❌ Gift {gift_id} not found in database")
            except Exception as e:
                print(f"❌ Error updating gift: {str(e)}")
                import traceback
                traceback.print_exc()

        elif event["type"] == "checkout.session.expired":
            print("🕒 Checkout session expired!")
            session = event["data"]["object"]

            # Get the gift ID from the metadata
            gift_id = session.get("metadata", {}).get("gift_id")
            if not gift_id:
                print("❌ No gift ID found in metadata")
                return {"status": "error", "message": "No gift ID found in metadata"}

            # Update the gift status
            try:
                gift = crud.get_gift(db, UUID(gift_id))
                if gift:
                    crud.update_gift_payment_status(db, UUID(gift_id), "failed")
                    print(f"✅ Updated gift {gift_id} to failed")
                else:
                    print(f"❌ Gift {gift_id} not found in database")
            except Exception as e:
                print(f"❌ Error updating gift: {str(e)}")
                import traceback
                traceback.print_exc()

        elif event["type"] == "payment_intent.succeeded":
            print("💰 Payment intent succeeded!")
            # Additional handling if needed

        elif event["type"] == "payment_intent.payment_failed":
            print("❌ Payment intent failed!")
            # Additional handling if needed

        else:
            print(f"⚠️ Unhandled event type: {event['type']}")

        return {"status": "success"}

    except Exception as e:
        print(f"❌ Error processing webhook: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "message": str(e)}


# Multi-step onboarding endpoints
@app.post("/api/onboarding/step1")
def onboarding_step1(data: schemas.OnboardingStep1, db: Session = Depends(get_db)):
    """Step 1: Basic account creation"""
    try:
        print(f"Processing step 1 with data: {data}")

        session, error = process_step1(db, data)

        if error:
            print(f"Error in step 1: {error}")
            return {
                "success": False,
                "message": error
            }

        print(f"Step 1 completed successfully for session: {session.session_token}")
        return {
            "success": True,
            "message": "Step 1 completed successfully",
            "session_token": session.session_token,
            "current_step": session.current_step - 1,
            "next_step": session.current_step
        }
    except Exception as e:
        import traceback
        print(f"Exception in step 1: {str(e)}")
        print(traceback.format_exc())
        return {
            "success": False,
            "message": f"Internal server error: {str(e)}",
            "details": traceback.format_exc()
        }

@app.post("/api/onboarding/step2")
def onboarding_step2(data: schemas.OnboardingStep2, session_token: str = None, db: Session = Depends(get_db)):
    """Step 2: Identity verification"""
    # Try to get session token from request body if not provided as query parameter
    if not session_token and hasattr(data, "session_token"):
        session_token = data.session_token
    try:
        print(f"Processing step 2 with session token: {session_token}")
        print(f"Data: {data}")

        session, error = process_step2(db, data, session_token)

        if error:
            print(f"Error in step 2: {error}")
            return {
                "success": False,
                "message": error
            }

        print(f"Step 2 completed successfully for session: {session.session_token}")
        return {
            "success": True,
            "message": "Step 2 completed successfully",
            "session_token": session.session_token,
            "current_step": session.current_step - 1,
            "next_step": session.current_step
        }
    except Exception as e:
        import traceback
        print(f"Exception in step 2: {str(e)}")
        print(traceback.format_exc())
        return {
            "success": False,
            "message": f"Internal server error: {str(e)}",
            "details": traceback.format_exc()
        }

@app.post("/api/onboarding/step3")
def onboarding_step3(data: schemas.OnboardingStep3, session_token: str = None, db: Session = Depends(get_db)):
    """Step 3: Financial profile"""
    # Try to get session token from request body if not provided as query parameter
    if not session_token and hasattr(data, "session_token"):
        session_token = data.session_token
    try:
        print(f"Processing step 3 with session token: {session_token}")
        print(f"Data: {data}")

        session, error = process_step3(db, data, session_token)

        if error:
            print(f"Error in step 3: {error}")
            return {
                "success": False,
                "message": error
            }

        print(f"Step 3 completed successfully for session: {session.session_token}")
        return {
            "success": True,
            "message": "Step 3 completed successfully",
            "session_token": session.session_token,
            "current_step": session.current_step - 1,
            "next_step": session.current_step
        }
    except Exception as e:
        import traceback
        print(f"Exception in step 3: {str(e)}")
        print(traceback.format_exc())
        return {
            "success": False,
            "message": f"Internal server error: {str(e)}",
            "details": traceback.format_exc()
        }

@app.post("/api/onboarding/step4")
def onboarding_step4(data: schemas.OnboardingStep4, session_token: str = None, db: Session = Depends(get_db)):
    """Step 4: Disclosures and agreements"""
    # Try to get session token from request body if not provided as query parameter
    if not session_token and hasattr(data, "session_token"):
        session_token = data.session_token
    try:
        print(f"Processing step 4 with session token: {session_token}")
        print(f"Data: {data}")

        session, error = process_step4(db, data, session_token)

        if error:
            print(f"Error in step 4: {error}")
            return {
                "success": False,
                "message": error
            }

        print(f"Step 4 completed successfully for session: {session.session_token}")
        return {
            "success": True,
            "message": "Step 4 completed successfully",
            "session_token": session.session_token,
            "current_step": session.current_step - 1,
            "next_step": session.current_step
        }
    except Exception as e:
        import traceback
        print(f"Exception in step 4: {str(e)}")
        print(traceback.format_exc())
        return {
            "success": False,
            "message": f"Internal server error: {str(e)}",
            "details": traceback.format_exc()
        }

@app.post("/api/onboarding/submit")
def onboarding_submit(session_token: str = None, request_data: dict = None, db: Session = Depends(get_db)):
    """Submit completed onboarding data to Alpaca"""
    # Try to get session token from request body if not provided as query parameter
    if not session_token and request_data and "session_token" in request_data:
        session_token = request_data["session_token"]
    try:
        print(f"Processing submission with session token: {session_token}")

        result, error = submit_to_alpaca(db, session_token)

        if error:
            print(f"Error in submission: {error}")
            return {
                "success": False,
                "message": error.get("error", "Unknown error"),
                "details": error
            }

        print(f"Submission completed successfully: {result}")
        return {
            "success": True,
            "message": "Account created successfully",
            **result
        }
    except Exception as e:
        import traceback
        print(f"Exception in submission: {str(e)}")
        print(traceback.format_exc())
        return {
            "success": False,
            "message": f"Internal server error: {str(e)}",
            "details": traceback.format_exc()
        }


# Test endpoint for debugging
@app.post("/api/test/child-profile")
def test_create_child_profile(profile: schemas.ChildProfileCreate, db: Session = Depends(get_db)):
    """Test endpoint for creating a child profile without authentication"""
    print(f"\n==== TEST CREATE CHILD PROFILE ====\nProfile data: {profile}")

    # Create a test user if it doesn't exist
    test_user = db.query(models.User).filter(models.User.email == "<EMAIL>").first()
    if not test_user:
        from app.auth import get_password_hash
        test_user = models.User(
            name="Test User",
            email="<EMAIL>",
            hashed_password=get_password_hash("password"),
            user_type=models.UserType.parent,
            is_active=True
        )
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        print(f"Created test user: {test_user.id}")
    else:
        print(f"Using existing test user: {test_user.id}")

    # Check if handle already exists
    existing_profile = crud.get_child_profile_by_handle(db, profile.handle)
    if existing_profile:
        print(f"Handle already in use: {profile.handle}")
        return {"success": False, "message": "Handle already in use"}

    try:
        # Create the child profile
        child_profile = crud.create_child_profile(db, profile, test_user.id)
        print(f"Created child profile: {child_profile.id}")
        return {"success": True, "profile": child_profile}
    except Exception as e:
        import traceback
        print(f"Error creating child profile: {str(e)}")
        print(traceback.format_exc())
        return {"success": False, "message": str(e)}

# Child Profile API Endpoints
@app.post("/api/children/", response_model=schemas.ChildProfileResponse)
def create_child_profile(profile: schemas.ChildProfileCreate, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_parent_user)):
    """Create a new child profile"""
    print(f"\n==== CREATE CHILD PROFILE ====\nUser: {current_user.id}, Profile data: {profile}")

    # Check if handle already exists
    existing_profile = crud.get_child_profile_by_handle(db, profile.handle)
    if existing_profile:
        print(f"Handle already in use: {profile.handle}")
        raise HTTPException(status_code=400, detail="Handle already in use")

    return crud.create_child_profile(db, profile, current_user.id)

@app.get("/api/children/", response_model=List[schemas.ChildProfileResponse])
def get_child_profiles(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_parent_user)):
    """Get all child profiles for the current user"""
    return crud.get_child_profiles_by_parent(db, current_user.id, skip, limit)

@app.get("/api/children/{profile_id}", response_model=schemas.ChildProfileResponse)
def get_child_profile(profile_id: UUID, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_parent_user)):
    """Get a specific child profile"""
    db_profile = crud.get_child_profile(db, profile_id)
    if not db_profile:
        raise HTTPException(status_code=404, detail="Profile not found")

    # Check if the profile belongs to the current user
    if db_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this profile")

    return db_profile

@app.get("/api/children/handle/{handle}", response_model=schemas.ChildProfileResponse)
def get_child_profile_by_handle(handle: str, db: Session = Depends(get_db)):
    """Get a child profile by handle (public endpoint)"""
    db_profile = crud.get_child_profile_by_handle(db, handle)
    if not db_profile:
        raise HTTPException(status_code=404, detail="Profile not found")

    # Only return public profiles
    if not db_profile.is_public:
        raise HTTPException(status_code=404, detail="Profile not found")

    return db_profile

@app.put("/api/children/{profile_id}", response_model=schemas.ChildProfileResponse)
def update_child_profile(profile_id: UUID, profile: schemas.ChildProfileUpdate, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_parent_user)):
    """Update a child profile"""
    db_profile = crud.get_child_profile(db, profile_id)
    if not db_profile:
        raise HTTPException(status_code=404, detail="Profile not found")

    # Check if the profile belongs to the current user
    if db_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to update this profile")

    # If handle is being updated, check if it's already in use
    if profile.handle and profile.handle != db_profile.handle:
        existing_profile = crud.get_child_profile_by_handle(db, profile.handle)
        if existing_profile:
            raise HTTPException(status_code=400, detail="Handle already in use")

    return crud.update_child_profile(db, profile_id, profile)

@app.delete("/api/children/{profile_id}", response_model=dict)
def delete_child_profile(profile_id: UUID, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_parent_user)):
    """Delete a child profile"""
    db_profile = crud.get_child_profile(db, profile_id)
    if not db_profile:
        raise HTTPException(status_code=404, detail="Profile not found")

    # Check if the profile belongs to the current user
    if db_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this profile")

    success = crud.delete_child_profile(db, profile_id)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete profile")

    return {"success": True, "message": "Profile deleted successfully"}

# Alternative endpoints for frontend compatibility
@app.post("/api/child-profiles", response_model=schemas.ChildProfileResponse)
def create_child_profile_alt(profile: schemas.ChildProfileCreate, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_active_user)):
    """Create a new child profile (alternative endpoint)"""
    print(f"\n==== CREATE CHILD PROFILE (ALT) ====\nUser: {current_user.id}, Profile data: {profile}")

    # Check if handle already exists
    existing_profile = crud.get_child_profile_by_handle(db, profile.handle)
    if existing_profile:
        print(f"Handle already in use: {profile.handle}")
        raise HTTPException(status_code=400, detail="Handle already in use")

    return crud.create_child_profile(db, profile, current_user.id)

@app.get("/api/child-profiles", response_model=List[schemas.ChildProfileResponse])
def get_child_profiles_alt(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_active_user)):
    """Get all child profiles for the current user (alternative endpoint)"""
    return crud.get_child_profiles_by_parent(db, current_user.id, skip, limit)

@app.get("/api/child-profiles/{profile_id}", response_model=schemas.ChildProfileResponse)
def get_child_profile_alt(profile_id: UUID, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_active_user)):
    """Get a specific child profile (alternative endpoint)"""
    db_profile = crud.get_child_profile(db, profile_id)
    if not db_profile:
        raise HTTPException(status_code=404, detail="Profile not found")

    # Check if the profile belongs to the current user
    if db_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this profile")

    return db_profile

@app.put("/api/child-profiles/{profile_id}", response_model=schemas.ChildProfileResponse)
def update_child_profile_alt(profile_id: UUID, profile: schemas.ChildProfileUpdate, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_active_user)):
    """Update a child profile (alternative endpoint)"""
    db_profile = crud.get_child_profile(db, profile_id)
    if not db_profile:
        raise HTTPException(status_code=404, detail="Profile not found")

    # Check if the profile belongs to the current user
    if db_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to update this profile")

    return crud.update_child_profile(db, profile_id, profile)

@app.delete("/api/child-profiles/{profile_id}", response_model=dict)
def delete_child_profile_alt(profile_id: UUID, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_active_user)):
    """Delete a child profile (alternative endpoint)"""
    db_profile = crud.get_child_profile(db, profile_id)
    if not db_profile:
        raise HTTPException(status_code=404, detail="Profile not found")

    # Check if the profile belongs to the current user
    if db_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this profile")

    success = crud.delete_child_profile(db, profile_id)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete profile")

    return {"success": True, "message": "Profile deleted successfully"}


# Investment API Endpoints
@app.post("/api/investments/", response_model=schemas.InvestmentResponse)
def create_investment(investment: schemas.InvestmentCreate, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_parent_user)):
    """Create a new investment"""
    # Check if the child profile belongs to the current user
    child_profile = crud.get_child_profile(db, investment.child_profile_id)
    if not child_profile or child_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to create investments for this profile")

    # If gift_id is provided, check if it belongs to the child profile
    if investment.gift_id:
        gift = crud.get_gift(db, investment.gift_id)
        if not gift or gift.child_profile_id != investment.child_profile_id:
            raise HTTPException(status_code=400, detail="Gift does not belong to this child profile")

    return crud.create_investment(db, investment)

@app.get("/api/investments/", response_model=List[schemas.InvestmentResponse])
def get_investments(child_profile_id: Optional[UUID] = None, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_parent_user)):
    """Get investments for a child profile or all child profiles of the current user"""
    if child_profile_id:
        # Check if the child profile belongs to the current user
        child_profile = crud.get_child_profile(db, child_profile_id)
        if not child_profile or child_profile.parent_id != current_user.id:
            raise HTTPException(status_code=403, detail="Not authorized to access this profile")

        return crud.get_investments_by_child_profile(db, child_profile_id, skip, limit)
    else:
        # Get all child profiles for the current user
        child_profiles = crud.get_child_profiles_by_parent(db, current_user.id)

        # Get investments for all child profiles
        investments = []
        for profile in child_profiles:
            profile_investments = crud.get_investments_by_child_profile(db, profile.id, 0, limit)
            investments.extend(profile_investments)

        # Sort by created_at and apply skip/limit
        investments.sort(key=lambda x: x.created_at, reverse=True)
        return investments[skip:skip+limit]

# Enhanced Portfolio Endpoints
@app.get("/api/child-profiles/{child_profile_id}/portfolio")
def get_child_portfolio(
    child_profile_id: UUID,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Get comprehensive portfolio information for a specific child"""
    # Verify the child profile belongs to the current user
    child_profile = crud.get_child_profile(db, child_profile_id)
    if not child_profile or child_profile.parent_id != current_user.id:
        raise HTTPException(status_code=404, detail="Child profile not found")

    # Get portfolio summary
    portfolio_summary = crud.get_child_portfolio_summary(db, child_profile_id)
    if not portfolio_summary:
        raise HTTPException(status_code=404, detail="Portfolio not found")

    # Get investment summary by symbol
    investments_by_symbol = crud.get_child_investment_summary(db, child_profile_id)

    # Get recent gifts with investment status
    recent_gifts = crud.get_gifts_with_investment_status(db, child_profile_id)

    # Get recent investments
    recent_investments = crud.get_investments_by_child_profile(db, child_profile_id)

    return {
        "profile": child_profile,
        "portfolio_summary": portfolio_summary,
        "recent_gifts": recent_gifts[:5],  # Last 5 gifts
        "investments_by_symbol": investments_by_symbol,
        "recent_investments": recent_investments[:10]  # Last 10 investments
    }

@app.get("/api/child-profiles/{child_profile_id}/gifts-with-investments")
def get_child_gifts_with_investments(
    child_profile_id: UUID,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Get all gifts for a child with their investment status"""
    # Verify the child profile belongs to the current user
    child_profile = crud.get_child_profile(db, child_profile_id)
    if not child_profile or child_profile.parent_id != current_user.id:
        raise HTTPException(status_code=404, detail="Child profile not found")

    gifts_with_investments = crud.get_gifts_with_investment_status(db, child_profile_id)
    return gifts_with_investments

@app.post("/api/gifts/{gift_id}/invest", response_model=schemas.InvestmentResponse)
def create_investment_from_gift(
    gift_id: UUID,
    investment_data: schemas.InvestmentFromGiftCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Create an investment from a specific gift"""
    # Verify the gift belongs to a child of the current user
    gift = crud.get_gift(db, gift_id)
    if not gift:
        raise HTTPException(status_code=404, detail="Gift not found")

    child_profile = crud.get_child_profile(db, gift.child_profile_id)
    if not child_profile or child_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to invest this gift")

    try:
        investment = crud.create_investment_from_gift(
            db,
            gift_id,
            investment_data.symbol,
            investment_data.amount_usd
        )
        return investment
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/dashboard/portfolio-overview")
def get_portfolio_overview(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Get portfolio overview for all children of the current user"""
    # Get all child profiles for the current user
    child_profiles = crud.get_child_profiles_by_parent(db, current_user.id)

    portfolio_overview = []
    total_gifts_received = 0.0
    total_invested = 0.0
    total_available_balance = 0.0

    for child_profile in child_profiles:
        portfolio_summary = crud.get_child_portfolio_summary(db, child_profile.id)
        if portfolio_summary:
            portfolio_overview.append(portfolio_summary)
            total_gifts_received += portfolio_summary['total_gifts_received']
            total_invested += portfolio_summary['total_invested']
            total_available_balance += portfolio_summary['available_balance']

    return {
        "children_portfolios": portfolio_overview,
        "overall_summary": {
            "total_children": len(child_profiles),
            "total_gifts_received": total_gifts_received,
            "total_invested": total_invested,
            "total_available_balance": total_available_balance,
            "total_portfolio_value": total_invested  # TODO: Add real-time values
        }
    }

@app.get("/api/investments/{investment_id}", response_model=schemas.InvestmentResponse)
def get_investment(investment_id: UUID, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_parent_user)):
    """Get a specific investment"""
    db_investment = crud.get_investment(db, investment_id)
    if not db_investment:
        raise HTTPException(status_code=404, detail="Investment not found")

    # Check if the investment belongs to a child profile of the current user
    child_profile = crud.get_child_profile(db, db_investment.child_profile_id)
    if not child_profile or child_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this investment")

    return db_investment

@app.put("/api/investments/{investment_id}", response_model=schemas.InvestmentResponse)
def update_investment(investment_id: UUID, investment: schemas.InvestmentUpdate, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_parent_user)):
    """Update an investment"""
    db_investment = crud.get_investment(db, investment_id)
    if not db_investment:
        raise HTTPException(status_code=404, detail="Investment not found")

    # Check if the investment belongs to a child profile of the current user
    child_profile = crud.get_child_profile(db, db_investment.child_profile_id)
    if not child_profile or child_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to update this investment")

    # Update the investment
    if investment.status:
        db_investment = crud.update_investment_status(db, investment_id, investment.status, investment.transaction_id)

    return db_investment

@app.post("/onboarding/alpaca/")
def onboarding_alpaca(data: schemas.AlpacaOnboardingRequest, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_parent_user)):
    try:
        # Use the authenticated parent user
        parent_user = current_user

        # Split full name and trim spaces
        full_name = data.full_name.strip()
        name_parts = full_name.split(" ", 1)
        given_name = name_parts[0].strip()
        family_name = name_parts[1].strip() if len(name_parts) > 1 else ""

        # Validate name parts
        if not given_name:
            return {"success": False, "message": "First name cannot be empty"}
        if not family_name:
            return {"success": False, "message": "Last name cannot be empty"}

        print(f"Name parts: given_name='{given_name}', family_name='{family_name}'")

        # Format date as YYYY-MM-DD
        formatted_dob = data.dob.strftime("%Y-%m-%d")

        # Prepare payload for Alpaca Broker API
        payload = {
            "contact": {
                "email_address": data.email,  # Use email from request
                "phone_number": data.phone_number,
                "street_address": [data.street_address],
                "city": data.city,
                "state": data.state,
                "postal_code": data.postal_code,
                "country": data.country
            },
            "identity": {
                "given_name": given_name,
                "family_name": family_name,
                "date_of_birth": formatted_dob,
                "tax_id": data.ssn,
                "tax_id_type": "USA_SSN",
                "country_of_citizenship": data.country,
                "country_of_birth": data.country,
                "country_of_tax_residence": data.country,
                "funding_source": [data.funding_source],
                # Add required fields for Broker API
                "annual_income_min": "10000",
                "annual_income_max": "100000",
                "liquid_net_worth_min": "10000",
                "liquid_net_worth_max": "100000",
                "total_net_worth_min": "10000",
                "total_net_worth_max": "100000"
            },
            "disclosures": {
                "is_control_person": False,
                "is_affiliated_exchange_or_finra": False,
                "is_politically_exposed": False,
                "immediate_family_exposed": False
            },
            "agreements": [
                {
                    "agreement": "customer_agreement",
                    "signed_at": "2024-04-15T00:00:00Z",
                    "ip_address": "127.0.0.1"
                },
                {
                    "agreement": "margin_agreement",
                    "signed_at": "2024-04-15T00:00:00Z",
                    "ip_address": "127.0.0.1"
                },
                {
                    "agreement": "account_agreement",
                    "signed_at": "2024-04-15T00:00:00Z",
                    "ip_address": "127.0.0.1"
                }
            ],
            "documents": [],
            "trusted_contact": {
                "given_name": given_name,
                "family_name": family_name,
                "email_address": data.email  # Use email from request
            },
            "enabled_assets": ["us_equity"],
            "account_type": "trading" # Specify account type
        }

        # Remove crypto agreement if not needed
        # Alpaca may have specific requirements for which agreements are needed

        print("\n==== ONBOARDING REQUEST ====")
        print(f"Processing onboarding request for {data.full_name}")

        result, error = onboard_alpaca_user(payload)

        if error:
            print(f"Onboarding error: {error}")
            if isinstance(error, dict):
                # Return a more user-friendly error message
                detail = error.get("error", str(error))
                # Return the error as JSON instead of raising an exception
                return {
                    "success": False,
                    "message": detail,
                    "details": error
                }
            else:
                # Return the error as JSON instead of raising an exception
                return {
                    "success": False,
                    "message": str(error),
                    "details": {}
                }

        # Save new broker account
        new_account = models.BrokerAccount(
            id=uuid4(),
            user_id=parent_user.id,
            broker_type="alpaca",
            external_account_id=result.get("id", "unknown"),
            status=result.get("status", "unknown")
        )

        db.add(new_account)
        db.commit()
        db.refresh(new_account)

        return {
            "message": "Alpaca onboarding submitted",
            "account_id": new_account.external_account_id,
            "status": new_account.status
        }
    except Exception as e:
        import traceback
        print(f"Unexpected error in onboarding: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# Relationship API Endpoints
@app.post("/api/relationships/", response_model=schemas.RelationshipResponse)
def create_relationship_request(
    relationship: schemas.RelationshipCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Create a new relationship request to connect with a child"""
    print(f"\n==== CREATE RELATIONSHIP REQUEST ====\nUser: {current_user.id}, Relationship: {relationship}")

    # Create the relationship request
    db_relationship = crud.create_relationship_request(db, relationship, current_user.id)
    if not db_relationship:
        raise HTTPException(status_code=400, detail="Child not found or relationship already exists")

    # Return relationship with details
    relationship_details = crud.get_relationship_with_details(db, db_relationship.id)
    if not relationship_details:
        raise HTTPException(status_code=500, detail="Failed to retrieve relationship details")

    return relationship_details

@app.get("/api/relationships/", response_model=schemas.RelationshipListResponse)
def get_user_relationships(
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Get all relationships for the current user (both sent and received)"""
    relationships = crud.get_user_relationships(db, current_user.id, status)

    # Get detailed information for each relationship
    detailed_relationships = []
    for rel in relationships:
        details = crud.get_relationship_with_details(db, rel.id)
        if details:
            detailed_relationships.append(details)

    # Count relationships by status
    pending_count = len([r for r in detailed_relationships if r['status'] == 'pending'])
    active_count = len([r for r in detailed_relationships if r['status'] == 'active'])

    return {
        "relationships": detailed_relationships,
        "total_count": len(detailed_relationships),
        "pending_count": pending_count,
        "active_count": active_count
    }

@app.get("/api/relationships/{relationship_id}", response_model=schemas.RelationshipResponse)
def get_relationship(
    relationship_id: UUID,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Get a specific relationship"""
    relationship = crud.get_relationship(db, relationship_id)
    if not relationship:
        raise HTTPException(status_code=404, detail="Relationship not found")

    # Check if user has access to this relationship
    child_profile = crud.get_child_profile(db, relationship.to_child_id)
    if not child_profile:
        raise HTTPException(status_code=404, detail="Child profile not found")

    # Allow access if user is the requester or the child's parent
    if relationship.from_user_id != current_user.id and child_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this relationship")

    relationship_details = crud.get_relationship_with_details(db, relationship_id)
    if not relationship_details:
        raise HTTPException(status_code=500, detail="Failed to retrieve relationship details")

    return relationship_details

@app.patch("/api/relationships/{relationship_id}", response_model=schemas.RelationshipResponse)
def update_relationship_status(
    relationship_id: UUID,
    relationship_update: schemas.RelationshipUpdate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Update relationship status (approve/decline/block)"""
    print(f"\n==== UPDATE RELATIONSHIP STATUS ====\nRelationship ID: {relationship_id}, Status: {relationship_update.status}")

    # Update the relationship status
    updated_relationship = crud.update_relationship_status(
        db, relationship_id, relationship_update.status, current_user.id
    )

    if not updated_relationship:
        raise HTTPException(status_code=404, detail="Relationship not found or not authorized")

    # Return updated relationship with details
    relationship_details = crud.get_relationship_with_details(db, relationship_id)
    if not relationship_details:
        raise HTTPException(status_code=500, detail="Failed to retrieve relationship details")

    return relationship_details

@app.delete("/api/relationships/{relationship_id}", response_model=dict)
def delete_relationship(
    relationship_id: UUID,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Delete a relationship"""
    success = crud.delete_relationship(db, relationship_id, current_user.id)
    if not success:
        raise HTTPException(status_code=404, detail="Relationship not found or not authorized")

    return {"success": True, "message": "Relationship deleted successfully"}

@app.get("/api/child-profiles/{child_profile_id}/relationships", response_model=List[schemas.RelationshipResponse])
def get_child_relationships(
    child_profile_id: UUID,
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Get all relationships for a specific child"""
    # Check if the child profile belongs to the current user
    child_profile = crud.get_child_profile(db, child_profile_id)
    if not child_profile or child_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this child's relationships")

    relationships = crud.get_child_relationships(db, child_profile_id, status)

    # Get detailed information for each relationship
    detailed_relationships = []
    for rel in relationships:
        details = crud.get_relationship_with_details(db, rel.id)
        if details:
            detailed_relationships.append(details)

    return detailed_relationships

@app.post("/api/relationships/search", response_model=dict)
def search_child_for_relationship(
    search_request: schemas.RelationshipSearchRequest,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Search for a child by handle to request a relationship"""
    # Get the child profile by handle
    child_profile = crud.get_child_profile_by_handle(db, search_request.child_handle)
    if not child_profile:
        raise HTTPException(status_code=404, detail="Child not found")

    # Check if relationship already exists
    existing_relationship = db.query(models.Relationship).filter(
        models.Relationship.from_user_id == current_user.id,
        models.Relationship.to_child_id == child_profile.id
    ).first()

    if existing_relationship:
        return {
            "child_found": True,
            "child_name": child_profile.name,
            "child_handle": child_profile.handle,
            "relationship_exists": True,
            "relationship_status": existing_relationship.status,
            "relationship_id": existing_relationship.id
        }

    return {
        "child_found": True,
        "child_name": child_profile.name,
        "child_handle": child_profile.handle,
        "relationship_exists": False,
        "can_request": True
    }
