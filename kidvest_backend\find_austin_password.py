#!/usr/bin/env python3

"""
Find Austin's password hash and test authentication
"""

import sys
import requests
from sqlalchemy import text
from app.database import SessionLocal

BASE_URL = "http://localhost:8000"
AUSTIN_EMAIL = "<EMAIL>"

def get_austin_password_hash():
    """Get Austin's password hash from database"""
    print("🔍 Looking up Austin's password hash...")
    
    db = SessionLocal()
    try:
        result = db.execute(text("""
            SELECT id, name, email, hashed_password
            FROM users 
            WHERE email = :email
        """), {"email": AUSTIN_EMAIL})
        
        user_row = result.fetchone()
        if not user_row:
            print(f"   ❌ User not found: {AUSTIN_EMAIL}")
            return None
        
        print(f"   ✅ Found user: {user_row[1]} ({user_row[2]})")
        print(f"   User ID: {user_row[0]}")
        print(f"   Password hash: {user_row[3][:20]}...")
        
        return {
            "user_id": str(user_row[0]),
            "name": user_row[1],
            "email": user_row[2],
            "password_hash": user_row[3]
        }
        
    except Exception as e:
        print(f"   ❌ Database error: {str(e)}")
        return None
    finally:
        db.close()

def test_common_passwords():
    """Test common passwords for Austin"""
    print("\n🔐 Testing common passwords...")
    
    common_passwords = [
        "password123",
        "password",
        "123456",
        "austin123",
        "glusac123",
        "test123",
        "admin123",
        "kidvest123"
    ]
    
    for password in common_passwords:
        print(f"   Testing password: {password}")
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/token",
                data={
                    "username": AUSTIN_EMAIL,
                    "password": password
                },
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                timeout=5
            )
            
            if response.status_code == 200:
                token_data = response.json()
                print(f"   ✅ SUCCESS! Password is: {password}")
                print(f"   Token: {token_data.get('access_token', '')[:20]}...")
                return password, token_data.get('access_token')
            else:
                print(f"   ❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    print("   ❌ No common passwords worked")
    return None, None

def test_manual_password():
    """Allow manual password testing"""
    print("\n🔐 Manual password testing...")
    
    import getpass
    
    while True:
        try:
            password = getpass.getpass(f"Enter password for {AUSTIN_EMAIL} (or 'quit'): ")
            
            if password.lower() == 'quit':
                break
            
            response = requests.post(
                f"{BASE_URL}/api/token",
                data={
                    "username": AUSTIN_EMAIL,
                    "password": password
                },
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                timeout=5
            )
            
            if response.status_code == 200:
                token_data = response.json()
                print(f"   ✅ SUCCESS! Password is: {password}")
                print(f"   Token: {token_data.get('access_token', '')[:20]}...")
                return password, token_data.get('access_token')
            else:
                print(f"   ❌ Failed: {response.status_code} - {response.text}")
                
        except KeyboardInterrupt:
            print("\n   ⚠️ Cancelled")
            break
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    return None, None

def create_test_user_with_known_password():
    """Create a test user with known password for testing"""
    print("\n👤 Creating test user with known password...")
    
    test_email = "<EMAIL>"
    test_password = "test123"
    
    try:
        # Try to register a test user
        response = requests.post(
            f"{BASE_URL}/api/auth/register",
            json={
                "name": "Test Parent",
                "email": test_email,
                "password": test_password
            },
            timeout=10
        )
        
        if response.status_code in [200, 201]:
            print(f"   ✅ Test user created: {test_email}")
            print(f"   Password: {test_password}")
            
            # Test login
            login_response = requests.post(
                f"{BASE_URL}/api/auth/login",
                data={
                    "username": test_email,
                    "password": test_password
                },
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                timeout=5
            )
            
            if login_response.status_code == 200:
                token_data = login_response.json()
                print(f"   ✅ Test login successful!")
                print(f"   Token: {token_data.get('access_token', '')[:20]}...")
                return test_email, test_password, token_data.get('access_token')
            else:
                print(f"   ❌ Test login failed: {login_response.text}")
        else:
            print(f"   ❌ User creation failed: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Error creating test user: {str(e)}")
    
    return None, None, None

def provide_solutions():
    """Provide solutions for authentication issues"""
    print("\n" + "=" * 60)
    print("🔧 AUTHENTICATION SOLUTIONS")
    print("=" * 60)
    
    print("\n1. 🔐 UPDATE FRONTEND WITH CORRECT PASSWORD:")
    print("   - Edit kidvest-app-new/app/invest/manual-simple.tsx")
    print("   - Update line 55: password: 'CORRECT_PASSWORD'")
    print("   - Replace 'password123' with Austin's actual password")
    
    print("\n2. 👤 CREATE TEST USER:")
    print("   - Use <EMAIL> / test123")
    print("   - Create child profiles for this user")
    print("   - Test investment flow with known credentials")
    
    print("\n3. 🔄 RESET AUSTIN'S PASSWORD:")
    print("   - Update password in database directly")
    print("   - Use bcrypt to hash a known password")
    print("   - Update users table with new hash")
    
    print("\n4. 🧪 BYPASS AUTH FOR TESTING:")
    print("   - Temporarily disable auth in backend")
    print("   - Test investment flow without authentication")
    print("   - Re-enable auth after testing")
    
    print("\n5. 📱 IMPLEMENT PROPER AUTH IN FRONTEND:")
    print("   - Add login screen to React Native app")
    print("   - Store JWT token in AsyncStorage")
    print("   - Use stored token for API calls")

def main():
    """Main function"""
    print("🔍 Austin Password Finder & Auth Tester")
    print("=" * 50)
    
    # Step 1: Get user info
    user_info = get_austin_password_hash()
    if not user_info:
        print("❌ Cannot find Austin's user record")
        return False
    
    # Step 2: Test common passwords
    password, token = test_common_passwords()
    
    if not password:
        # Step 3: Manual password testing
        password, token = test_manual_password()
    
    if not password:
        # Step 4: Create test user
        test_email, test_password, test_token = create_test_user_with_known_password()
        if test_token:
            print(f"\n✅ Use test user for frontend testing:")
            print(f"   Email: {test_email}")
            print(f"   Password: {test_password}")
    
    # Step 5: Provide solutions
    provide_solutions()
    
    if password:
        print(f"\n🎉 SOLUTION FOUND:")
        print(f"   Austin's password: {password}")
        print(f"   Update frontend with this password!")
        return True
    else:
        print(f"\n⚠️ No password found for Austin")
        print(f"   Use test user or reset Austin's password")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
