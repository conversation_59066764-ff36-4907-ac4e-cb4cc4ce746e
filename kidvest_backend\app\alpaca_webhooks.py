"""
Alpaca webhook handlers for account status updates
"""

import json
import hmac
import hashlib
from typing import Optional
from fastapi import HTT<PERSON>Exception
from sqlalchemy.orm import Session
from app import models
from app.alpaca_service import create_account_api_keys, enable_account_trading, encrypt_secret


def verify_alpaca_webhook_signature(payload: bytes, signature: str, secret: str) -> bool:
    """Verify Alpaca webhook signature"""
    if not secret:
        print("⚠️ No webhook secret configured - skipping signature verification")
        return True  # Allow in development
    
    try:
        expected_signature = hmac.new(
            secret.encode(),
            payload,
            hashlib.sha256
        ).hexdigest()
        
        # Alpaca sends signature as 'sha256=<hash>'
        if signature.startswith('sha256='):
            signature = signature[7:]
        
        return hmac.compare_digest(expected_signature, signature)
    except Exception as e:
        print(f"❌ Webhook signature verification failed: {str(e)}")
        return False


def handle_account_status_update(db: Session, webhook_data: dict) -> dict:
    """Handle account status update webhook from Alpaca"""
    print(f"\n==== ALPACA ACCOUNT STATUS WEBHOOK ====")
    print(f"Webhook data: {json.dumps(webhook_data, indent=2)}")
    
    try:
        # Extract account information
        account_id = webhook_data.get("account_id")
        status = webhook_data.get("status")
        event_type = webhook_data.get("event_type")
        
        if not account_id:
            raise ValueError("No account_id in webhook data")
        
        print(f"Account ID: {account_id}")
        print(f"Status: {status}")
        print(f"Event Type: {event_type}")
        
        # Find the broker account in our database
        broker_account = db.query(models.BrokerAccount).filter(
            models.BrokerAccount.external_account_id == account_id
        ).first()
        
        if not broker_account:
            print(f"⚠️ Broker account not found for account_id: {account_id}")
            return {"status": "ignored", "reason": "account_not_found"}
        
        print(f"✅ Found broker account: {broker_account.id}")
        print(f"Current status: {broker_account.status}")
        print(f"Trading enabled: {broker_account.trading_enabled}")
        
        # Update account status
        old_status = broker_account.status
        broker_account.status = status.lower()
        
        # If account is approved/active, enable trading and create API keys
        if status.lower() in ["active", "approved"]:
            print(f"🎉 Account approved! Enabling trading...")
            
            # Enable trading configuration
            trading_result, trading_error = enable_account_trading(account_id)
            if trading_error:
                print(f"⚠️ Failed to enable trading: {trading_error}")
            else:
                print(f"✅ Trading enabled: {trading_result}")
                broker_account.trading_enabled = True
            
            # Create account-specific API keys
            keys_result, keys_error = create_account_api_keys(account_id)
            if keys_error:
                print(f"⚠️ Failed to create API keys: {keys_error}")
                # Continue without API keys - will use master credentials
            else:
                print(f"✅ API keys created: {keys_result['api_key_id']}")
                broker_account.api_key_id = keys_result["api_key_id"]
                broker_account.api_secret_key = encrypt_secret(keys_result["api_secret_key"])
                print(f"🔐 API credentials stored and encrypted")
        
        # Commit changes
        db.commit()
        db.refresh(broker_account)
        
        print(f"✅ Account updated: {old_status} → {broker_account.status}")
        print(f"Trading enabled: {broker_account.trading_enabled}")
        print(f"Has API keys: {bool(broker_account.api_key_id)}")
        
        return {
            "status": "processed",
            "account_id": account_id,
            "old_status": old_status,
            "new_status": broker_account.status,
            "trading_enabled": broker_account.trading_enabled,
            "has_api_keys": bool(broker_account.api_key_id)
        }
        
    except Exception as e:
        print(f"❌ Error processing webhook: {str(e)}")
        db.rollback()
        raise


def handle_trade_execution_webhook(db: Session, webhook_data: dict) -> dict:
    """Handle trade execution webhook from Alpaca"""
    print(f"\n==== ALPACA TRADE EXECUTION WEBHOOK ====")
    print(f"Webhook data: {json.dumps(webhook_data, indent=2)}")
    
    try:
        # Extract trade information
        order_id = webhook_data.get("order_id")
        account_id = webhook_data.get("account_id")
        status = webhook_data.get("status")
        filled_qty = webhook_data.get("filled_qty")
        filled_avg_price = webhook_data.get("filled_avg_price")
        
        print(f"Order ID: {order_id}")
        print(f"Account ID: {account_id}")
        print(f"Status: {status}")
        print(f"Filled Qty: {filled_qty}")
        print(f"Filled Price: {filled_avg_price}")
        
        # Find the investment record
        investment = db.query(models.Investment).filter(
            models.Investment.transaction_id == order_id
        ).first()
        
        if not investment:
            print(f"⚠️ Investment not found for order_id: {order_id}")
            return {"status": "ignored", "reason": "investment_not_found"}
        
        print(f"✅ Found investment: {investment.id}")
        
        # Update investment with execution details
        if status == "filled":
            investment.status = "completed"
            investment.shares = float(filled_qty) if filled_qty else investment.shares
            investment.purchase_price = float(filled_avg_price) if filled_avg_price else investment.purchase_price
            print(f"✅ Investment completed: {filled_qty} shares at ${filled_avg_price}")
        elif status == "canceled" or status == "rejected":
            investment.status = "failed"
            print(f"❌ Investment failed: {status}")
        
        db.commit()
        db.refresh(investment)
        
        return {
            "status": "processed",
            "investment_id": str(investment.id),
            "order_status": status
        }
        
    except Exception as e:
        print(f"❌ Error processing trade webhook: {str(e)}")
        db.rollback()
        raise


def process_alpaca_webhook(db: Session, webhook_type: str, webhook_data: dict) -> dict:
    """Process different types of Alpaca webhooks"""
    
    handlers = {
        "account.status_updated": handle_account_status_update,
        "account.approved": handle_account_status_update,
        "account.rejected": handle_account_status_update,
        "trade.filled": handle_trade_execution_webhook,
        "trade.canceled": handle_trade_execution_webhook,
        "trade.rejected": handle_trade_execution_webhook,
    }
    
    handler = handlers.get(webhook_type)
    if not handler:
        print(f"⚠️ No handler for webhook type: {webhook_type}")
        return {"status": "ignored", "reason": "unsupported_webhook_type"}
    
    return handler(db, webhook_data)
