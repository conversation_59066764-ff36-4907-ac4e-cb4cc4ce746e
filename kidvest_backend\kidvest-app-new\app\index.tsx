import { View, Text, StyleSheet } from 'react-native';
import { Link } from 'expo-router';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';

export default function Index() {
  const colorScheme = useColorScheme();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Welcome to KidVest</Text>
      <Text style={styles.subtitle}>Your child's investment platform</Text>

      <View style={styles.linksContainer}>
        <Link href="/auth/login" style={[styles.link, { backgroundColor: Colors[colorScheme].primary }]}>
          <Text style={styles.linkText}>Login</Text>
        </Link>

        <Link href="/auth/register" style={[styles.link, { backgroundColor: Colors[colorScheme].primary }]}>
          <Text style={styles.linkText}>Register</Text>
        </Link>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 18,
    color: '#666',
    marginBottom: 30,
  },
  linksContainer: {
    width: '100%',
    maxWidth: 300,
    gap: 15,
  },
  link: {
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
  },
  linkText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
});
