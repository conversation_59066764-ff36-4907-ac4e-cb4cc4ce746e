from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, EmailStr
from typing import Optional
import uvicorn

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# User model
class UserCreate(BaseModel):
    name: str
    email: EmailStr
    password: str
    user_type: str
    phone_number: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None

class UserResponse(BaseModel):
    id: str
    name: str
    email: str
    user_type: str
    is_active: bool = True
    created_at: str

# Root route
@app.get("/")
def read_root():
    return {"message": "Welcome to Test Backend!"}

# Register route
@app.post("/api/register", response_model=UserResponse)
async def register_user(user_data: UserCreate):
    print(f"Registration request received: {user_data.name}, {user_data.email}")
    
    # Simulate successful registration
    return {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": user_data.name,
        "email": user_data.email,
        "user_type": user_data.user_type,
        "is_active": True,
        "created_at": "2023-04-19T12:00:00"
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
