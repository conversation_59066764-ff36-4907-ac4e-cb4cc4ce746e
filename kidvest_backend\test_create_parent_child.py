import os
import sys
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API Base URL
API_BASE_URL = "http://127.0.0.1:8000/api"

def create_parent_user():
    """Create a parent user"""
    print("Creating parent user...")

    url = "http://127.0.0.1:8000/users/"
    data = {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "user_type": "parent",
        "phone_number": "************",
        "address": "123 Main St",
        "city": "Anytown",
        "state": "CA",
        "postal_code": "12345",
        "country": "USA"
    }

    try:
        response = requests.post(url, json=data)
        response.raise_for_status()
        user = response.json()
        print(f"Parent user created successfully: {user['name']} ({user['id']})")
        return user
    except requests.exceptions.RequestException as e:
        print(f"Error creating parent user: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"Response: {e.response.text}")
        return None

def create_child_profile(parent_id):
    """Create a child profile"""
    print("Creating child profile...")

    url = f"{API_BASE_URL}/children/"
    data = {
        "name": "Jane Doe",
        "age": 10,
        "handle": "jane_doe",
        "is_public": True,
        "bio": "I love investing and learning about money!"
    }

    try:
        # For this test, we'll use the first parent user we find
        response = requests.post(url, json=data)
        response.raise_for_status()
        profile = response.json()
        print(f"Child profile created successfully: {profile['name']} (@{profile['handle']})")
        return profile
    except requests.exceptions.RequestException as e:
        print(f"Error creating child profile: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"Response: {e.response.text}")
        return None

def main():
    """Main function"""
    # Create parent user
    parent = create_parent_user()
    if not parent:
        print("Failed to create parent user. Exiting.")
        return

    # Create child profile
    child = create_child_profile(parent["id"])
    if not child:
        print("Failed to create child profile. Exiting.")
        return

    print("\nSetup completed successfully!")
    print(f"Parent: {parent['name']} ({parent['id']})")
    print(f"Child: {child['name']} (@{child['handle']})")
    print(f"\nYou can now access the gift wall at: http://localhost:8082/wall/{child['handle']}")

if __name__ == "__main__":
    main()
