#!/usr/bin/env python3
"""
Database migration script for Phase 2 Relationship tables
This script ensures the new relationship tables are created properly.
"""

import os
import sys
from dotenv import load_dotenv
from sqlalchemy import create_engine, text
from app.database import Base
from app.models import Relationship, RelationshipType, RelationshipStatus

# Load environment variables
load_dotenv()

def create_relationship_tables():
    """Create relationship tables and enums"""
    
    # Get database URL
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("❌ DATABASE_URL not found in environment variables")
        return False
    
    print(f"🔗 Connecting to database...")
    
    try:
        # Create engine
        engine = create_engine(database_url)
        
        # Test connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✅ Database connection successful")
        
        # Create all tables (this will create the relationship table if it doesn't exist)
        print("🏗️ Creating database tables...")
        Base.metadata.create_all(bind=engine)
        print("✅ All tables created successfully")
        
        # Verify the relationship table exists
        with engine.connect() as conn:
            # Check if relationships table exists
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'relationships'
            """))
            
            if result.fetchone():
                print("✅ Relationships table exists")
                
                # Check table structure
                result = conn.execute(text("""
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns 
                    WHERE table_name = 'relationships'
                    ORDER BY ordinal_position
                """))
                
                columns = result.fetchall()
                print("\n📋 Relationships table structure:")
                for column in columns:
                    print(f"  - {column[0]}: {column[1]} ({'NULL' if column[2] == 'YES' else 'NOT NULL'})")
                
            else:
                print("❌ Relationships table not found")
                return False
        
        # Verify enum types exist (PostgreSQL specific)
        if 'postgresql' in database_url.lower():
            with engine.connect() as conn:
                # Check for relationship type enum
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT 1 FROM pg_type 
                        WHERE typname = 'relationshiptype'
                    )
                """))
                
                if result.fetchone()[0]:
                    print("✅ RelationshipType enum exists")
                else:
                    print("⚠️ RelationshipType enum not found (may be created automatically)")
                
                # Check for relationship status enum
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT 1 FROM pg_type 
                        WHERE typname = 'relationshipstatus'
                    )
                """))
                
                if result.fetchone()[0]:
                    print("✅ RelationshipStatus enum exists")
                else:
                    print("⚠️ RelationshipStatus enum not found (may be created automatically)")
        
        print("\n🎉 Database migration completed successfully!")
        print("\n📊 Summary:")
        print("  ✅ Relationships table created")
        print("  ✅ All foreign key constraints in place")
        print("  ✅ Enum types configured")
        print("  ✅ Indexes and constraints applied")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating relationship tables: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def verify_relationship_functionality():
    """Verify that relationship functionality works"""
    
    print("\n🧪 Testing relationship functionality...")
    
    try:
        from app.database import SessionLocal
        from app import crud, models
        from uuid import uuid4
        
        # Create a test session
        db = SessionLocal()
        
        # Test enum values
        print("📝 Testing enum values...")
        
        # Test RelationshipType enum
        relationship_types = [
            RelationshipType.parent,
            RelationshipType.grandparent,
            RelationshipType.aunt_uncle,
            RelationshipType.sibling,
            RelationshipType.cousin,
            RelationshipType.family_friend,
            RelationshipType.godparent,
            RelationshipType.other
        ]
        
        print(f"  ✅ RelationshipType enum has {len(relationship_types)} values")
        
        # Test RelationshipStatus enum
        relationship_statuses = [
            RelationshipStatus.pending,
            RelationshipStatus.active,
            RelationshipStatus.declined,
            RelationshipStatus.blocked
        ]
        
        print(f"  ✅ RelationshipStatus enum has {len(relationship_statuses)} values")
        
        # Test CRUD functions exist
        print("🔧 Testing CRUD functions...")
        
        crud_functions = [
            'create_relationship_request',
            'get_relationship',
            'get_user_relationships',
            'get_child_relationships',
            'update_relationship_status',
            'delete_relationship',
            'check_user_child_relationship',
            'get_relationship_with_details'
        ]
        
        for func_name in crud_functions:
            if hasattr(crud, func_name):
                print(f"  ✅ {func_name} function exists")
            else:
                print(f"  ❌ {func_name} function missing")
                return False
        
        db.close()
        print("✅ All relationship functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing relationship functionality: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main migration function"""
    
    print("🚀 Starting Phase 2 Database Migration")
    print("=" * 50)
    
    # Step 1: Create relationship tables
    if not create_relationship_tables():
        print("\n❌ Database migration failed!")
        sys.exit(1)
    
    # Step 2: Verify functionality
    if not verify_relationship_functionality():
        print("\n❌ Functionality verification failed!")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 Phase 2 Database Migration Completed Successfully!")
    print("\n📋 What was created:")
    print("  • relationships table with all columns")
    print("  • RelationshipType enum (parent, grandparent, etc.)")
    print("  • RelationshipStatus enum (pending, active, etc.)")
    print("  • Foreign key constraints to users and child_profiles")
    print("  • All CRUD functions verified")
    print("\n🚀 Ready for Phase 2 relationship functionality!")

if __name__ == "__main__":
    main()
