import http.server
import socketserver
import os
import sys
from pathlib import Path

PORT = 8080
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
UNIFIED_DIR = os.path.join(BASE_DIR, "unified-ui")

class FixedHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # Set the directory to the unified-ui directory
        print(f"Initializing handler with directory: {UNIFIED_DIR}")
        super().__init__(*args, directory=UNIFIED_DIR, **kwargs)

    def log_message(self, format, *args):
        # Enhanced logging
        print(f"[{self.log_date_time_string()}] {format % args}")

    def do_GET(self):
        # Log the request
        print(f"Request path: {self.path}")

        # Serve the index.html file for the root path
        if self.path == "/" or self.path == "":
            self.path = "/index.html"
            print(f"Serving root path, redirecting to: {self.path}")

        # Map section paths to their directories
        elif self.path.startswith("/auth"):
            if self.path == "/auth" or self.path == "/auth/":
                self.path = "/auth/index.html"
            print(f"Serving auth path: {self.path}")

        elif self.path.startswith("/dashboard"):
            if self.path == "/dashboard" or self.path == "/dashboard/":
                self.path = "/dashboard/index.html"
            print(f"Serving dashboard path: {self.path}")

        elif self.path.startswith("/onboarding"):
            if self.path == "/onboarding" or self.path == "/onboarding/":
                self.path = "/onboarding/index.html"
            print(f"Serving onboarding path: {self.path}")

        elif self.path.startswith("/child-profile"):
            if self.path == "/child-profile" or self.path == "/child-profile/":
                self.path = "/child-profile/index.html"
            print(f"Serving child-profile path: {self.path}")

        elif self.path.startswith("/gift-wall"):
            if self.path == "/gift-wall" or self.path == "/gift-wall/":
                self.path = "/gift-wall/index.html"
            print(f"Serving gift-wall path: {self.path}")

        # Try to serve the file
        try:
            super().do_GET()
        except Exception as e:
            print(f"Error serving {self.path}: {str(e)}")
            self.send_error(404, f"File not found: {self.path}")

def run_server():
    # Make sure the unified-ui directory exists
    if not os.path.exists(UNIFIED_DIR):
        print(f"Error: The unified-ui directory does not exist at {UNIFIED_DIR}")
        print("Please run the prepare_unified_ui.py script first.")
        sys.exit(1)

    # Make sure the index.html file exists
    if not os.path.exists(os.path.join(UNIFIED_DIR, "index.html")):
        print(f"Error: The index.html file does not exist at {os.path.join(UNIFIED_DIR, 'index.html')}")
        print("Please run the prepare_unified_ui.py script first.")
        sys.exit(1)

    # Start the server
    with socketserver.TCPServer(("", PORT), FixedHandler) as httpd:
        print(f"Serving at http://localhost:{PORT}")
        print(f"Base directory: {UNIFIED_DIR}")
        print(f"Available UIs:")
        print(f"  - Main: http://localhost:{PORT}/")
        print(f"  - Auth: http://localhost:{PORT}/auth/")
        print(f"  - Dashboard: http://localhost:{PORT}/dashboard/")
        print(f"  - Onboarding: http://localhost:{PORT}/onboarding/")
        print(f"  - Child Profiles: http://localhost:{PORT}/child-profile/")
        print(f"  - Gift Walls: http://localhost:{PORT}/gift-wall/")
        httpd.serve_forever()

if __name__ == "__main__":
    run_server()
