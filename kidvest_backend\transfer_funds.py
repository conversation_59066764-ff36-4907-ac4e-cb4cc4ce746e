#!/usr/bin/env python3

"""
Transfer funds to Austin's account
"""

import requests
import json
import os
import base64
from datetime import datetime

AUSTIN_ACCOUNT_ID = "25aa2245-9d53-42f9-b3b3-43cb899c1798"
ACH_RELATIONSHIP_ID = "e7635944-b71d-46fc-923c-a9d5159336b4"

def initiate_ach_transfer():
    """Initiate ACH transfer to fund account"""
    print("💸 Initiating ACH transfer...")
    
    api_key = os.getenv("ALPACA_API_KEY")
    api_secret = os.getenv("ALPACA_SECRET_KEY")
    
    auth_string = f"{api_key}:{api_secret}"
    encoded_auth = base64.b64encode(auth_string.encode()).decode()
    
    headers = {
        "Authorization": f"Basic {encoded_auth}",
        "Content-Type": "application/json"
    }
    
    # Transfer payload
    transfer_payload = {
        "transfer_type": "ach",
        "relationship_id": ACH_RELATIONSHIP_ID,
        "amount": "1000.00",
        "direction": "INCOMING"
    }
    
    url = f"https://broker-api.sandbox.alpaca.markets/v1/accounts/{AUSTIN_ACCOUNT_ID}/transfers"
    
    print(f"   URL: {url}")
    print(f"   Payload: {json.dumps(transfer_payload, indent=2)}")
    
    try:
        response = requests.post(url, headers=headers, json=transfer_payload, timeout=30)
        
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code in [200, 201]:
            result = response.json()
            print(f"   ✅ Transfer initiated: {result.get('id')}")
            print(f"   Amount: ${result.get('amount')}")
            print(f"   Status: {result.get('status')}")
            return result
        else:
            print(f"   ❌ Transfer failed")
            return None
            
    except Exception as e:
        print(f"   ❌ Transfer error: {str(e)}")
        return None

def check_transfers():
    """Check transfer status"""
    print("\n📋 Checking transfers...")
    
    api_key = os.getenv("ALPACA_API_KEY")
    api_secret = os.getenv("ALPACA_SECRET_KEY")
    
    auth_string = f"{api_key}:{api_secret}"
    encoded_auth = base64.b64encode(auth_string.encode()).decode()
    
    headers = {
        "Authorization": f"Basic {encoded_auth}",
        "Content-Type": "application/json"
    }
    
    url = f"https://broker-api.sandbox.alpaca.markets/v1/accounts/{AUSTIN_ACCOUNT_ID}/transfers"
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            transfers = response.json()
            print(f"   Found {len(transfers)} transfers:")
            
            for transfer in transfers:
                print(f"   - ID: {transfer.get('id')}")
                print(f"     Amount: ${transfer.get('amount')}")
                print(f"     Status: {transfer.get('status')}")
                print(f"     Direction: {transfer.get('direction')}")
                print(f"     Created: {transfer.get('created_at')}")
                print()
            
            return transfers
        else:
            print(f"   Error: {response.text}")
            return []
            
    except Exception as e:
        print(f"   Error: {str(e)}")
        return []

def check_balance_after_transfer():
    """Check account balance after transfer"""
    print("\n💰 Checking balance after transfer...")
    
    api_key = os.getenv("ALPACA_API_KEY")
    api_secret = os.getenv("ALPACA_SECRET_KEY")
    
    auth_string = f"{api_key}:{api_secret}"
    encoded_auth = base64.b64encode(auth_string.encode()).decode()
    
    headers = {
        "Authorization": f"Basic {encoded_auth}",
        "Content-Type": "application/json"
    }
    
    url = f"https://broker-api.sandbox.alpaca.markets/v1/trading/accounts/{AUSTIN_ACCOUNT_ID}/account"
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            buying_power = result.get('buying_power', '0')
            cash = result.get('cash', '0')
            equity = result.get('equity', '0')
            
            print(f"   💰 Cash: ${cash}")
            print(f"   💰 Buying Power: ${buying_power}")
            print(f"   💰 Equity: ${equity}")
            
            return float(buying_power)
        else:
            print(f"   Error: {response.text}")
            return 0
            
    except Exception as e:
        print(f"   Error: {str(e)}")
        return 0

def try_instant_funding():
    """Try instant funding for sandbox"""
    print("\n⚡ Trying instant funding...")
    
    api_key = os.getenv("ALPACA_API_KEY")
    api_secret = os.getenv("ALPACA_SECRET_KEY")
    
    auth_string = f"{api_key}:{api_secret}"
    encoded_auth = base64.b64encode(auth_string.encode()).decode()
    
    headers = {
        "Authorization": f"Basic {encoded_auth}",
        "Content-Type": "application/json"
    }
    
    # Try different instant funding methods
    funding_methods = [
        {
            "name": "Sandbox Deposit",
            "url": f"https://broker-api.sandbox.alpaca.markets/v1/accounts/{AUSTIN_ACCOUNT_ID}/transfers",
            "payload": {
                "transfer_type": "deposit",
                "amount": "1000.00",
                "method": "sandbox"
            }
        },
        {
            "name": "Journal Entry",
            "url": f"https://broker-api.sandbox.alpaca.markets/v1/journals",
            "payload": {
                "entry_type": "JNLC",
                "to_account": AUSTIN_ACCOUNT_ID,
                "amount": "1000.00",
                "description": "Sandbox funding"
            }
        }
    ]
    
    for method in funding_methods:
        print(f"\n   Trying {method['name']}...")
        print(f"   URL: {method['url']}")
        print(f"   Payload: {json.dumps(method['payload'], indent=2)}")
        
        try:
            response = requests.post(
                method['url'], 
                headers=headers, 
                json=method['payload'], 
                timeout=30
            )
            
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.text}")
            
            if response.status_code in [200, 201]:
                print(f"   ✅ {method['name']} successful!")
                return True
            else:
                print(f"   ❌ {method['name']} failed")
                
        except Exception as e:
            print(f"   ❌ {method['name']} error: {str(e)}")
    
    return False

def main():
    """Main function"""
    print("💸 Fund Transfer Test")
    print("=" * 50)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Try ACH transfer
    transfer_result = initiate_ach_transfer()
    
    # Step 2: Check transfers
    transfers = check_transfers()
    
    # Step 3: Check balance
    balance = check_balance_after_transfer()
    
    # Step 4: If still no balance, try instant funding
    if balance == 0:
        print(f"\n❌ Balance still $0 after ACH transfer")
        instant_success = try_instant_funding()
        
        if instant_success:
            balance = check_balance_after_transfer()
    
    # Summary
    print(f"\n" + "=" * 60)
    print(f"📊 FUNDING SUMMARY")
    print(f"=" * 60)
    
    if balance > 0:
        print(f"✅ SUCCESS: Account funded with ${balance}")
        print(f"🎉 Austin can now execute trades!")
        
        print(f"\n📋 NEXT STEPS:")
        print(f"1. Run investment test: python test_austin_auth.py")
        print(f"2. Test frontend investment flow")
        print(f"3. Verify trades execute successfully")
        
    else:
        print(f"❌ FUNDING FAILED: Account still has $0")
        print(f"💡 Sandbox accounts may require manual funding")
        
        print(f"\n📋 ALTERNATIVES:")
        print(f"1. 🎭 Modify backend to simulate trades")
        print(f"2. 💰 Contact Alpaca for sandbox funding")
        print(f"3. 🔄 Use paper trading instead")
        print(f"4. 📱 Test frontend with mock responses")
    
    return balance > 0

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted")
        exit(1)
    except Exception as e:
        print(f"\n💥 Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        exit(1)
