import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, TextInput, ScrollView, KeyboardAvoidingView, Platform, ActivityIndicator, Image, Alert } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';

import { Text, View } from '@/components/Themed';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';
import { childProfilesAPI } from '../../services/api';

export default function ChildProfileScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Child profile information
  const [name, setName] = useState('');
  const [dob, setDob] = useState('');
  const [relationship, setRelationship] = useState('');
  const [ssn, setSsn] = useState('');
  const [avatar, setAvatar] = useState<string | null>(null);
  
  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant permission to access your photo library.');
      return;
    }
    
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.5,
    });
    
    if (!result.canceled) {
      setAvatar(result.assets[0].uri);
    }
  };

  const validateForm = () => {
    if (!name.trim()) {
      setError('Child name is required');
      return false;
    }
    
    if (!dob.trim()) {
      setError('Date of birth is required');
      return false;
    }
    
    if (!relationship.trim()) {
      setError('Relationship is required');
      return false;
    }
    
    if (!ssn.trim()) {
      setError('SSN is required');
      return false;
    }
    
    setError(null);
    return true;
  };

  const handleCreateProfile = async () => {
    if (!validateForm()) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // In a real app, this would upload the avatar image to a server
      // and get back a URL to store in the profile
      
      const profileData = {
        name,
        date_of_birth: dob,
        relationship,
        ssn,
        avatar_url: avatar, // In a real app, this would be the URL from the server
      };
      
      // Create the profile
      const response = await childProfilesAPI.createProfile(profileData);
      
      // Show success message
      Alert.alert(
        'Profile Created',
        `${name}'s profile has been created successfully!`,
        [
          {
            text: 'OK',
            onPress: () => router.replace('/(tabs)/profiles'),
          },
        ]
      );
    } catch (error) {
      console.error('Error creating child profile:', error);
      setError('Failed to create child profile. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <FontAwesome5 name="arrow-left" size={16} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Add Child Profile</Text>
          <View style={{ width: 40 }} />
        </View>
        
        {error && (
          <View style={styles.errorContainer}>
            <FontAwesome5 name="exclamation-circle" size={16} color="#F44336" />
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}
        
        <View style={styles.formContainer}>
          <View style={styles.avatarContainer}>
            <TouchableOpacity onPress={pickImage}>
              {avatar ? (
                <Image source={{ uri: avatar }} style={styles.avatar} />
              ) : (
                <View style={[styles.avatarPlaceholder, { backgroundColor: Colors[colorScheme].primary + '20' }]}>
                  <FontAwesome5 name="camera" size={24} color={Colors[colorScheme].primary} />
                </View>
              )}
            </TouchableOpacity>
            <Text style={styles.avatarLabel}>Add Photo</Text>
          </View>
          
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Child's Name</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter child's name"
              value={name}
              onChangeText={setName}
            />
          </View>
          
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Date of Birth</Text>
            <TextInput
              style={styles.input}
              placeholder="MM/DD/YYYY"
              value={dob}
              onChangeText={setDob}
            />
          </View>
          
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Relationship</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g., Son, Daughter, Niece, Nephew"
              value={relationship}
              onChangeText={setRelationship}
            />
          </View>
          
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Social Security Number</Text>
            <TextInput
              style={styles.input}
              placeholder="XXX-XX-XXXX"
              value={ssn}
              onChangeText={setSsn}
            />
          </View>
          
          <View style={styles.infoContainer}>
            <FontAwesome5 name="info-circle" size={16} color={Colors[colorScheme].primary} style={styles.infoIcon} />
            <Text style={styles.infoText}>
              The child's SSN is required to create a brokerage account. This information is securely stored and encrypted.
            </Text>
          </View>
          
          <TouchableOpacity 
            style={[styles.createButton, { backgroundColor: Colors[colorScheme].primary }]}
            onPress={handleCreateProfile}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text style={styles.createButtonText}>Create Profile</Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFEBEE',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: '#F44336',
    marginLeft: 8,
    flex: 1,
  },
  formContainer: {
    flex: 1,
  },
  avatarContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarLabel: {
    marginTop: 8,
    fontSize: 14,
    color: '#666',
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: '#666666',
  },
  input: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 48,
    fontSize: 16,
  },
  infoContainer: {
    flexDirection: 'row',
    backgroundColor: '#E3F2FD',
    padding: 12,
    borderRadius: 8,
    marginBottom: 24,
  },
  infoIcon: {
    marginRight: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  createButton: {
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  createButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
});
