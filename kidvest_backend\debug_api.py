import requests
import json
import time

def debug_api():
    """Debug the API endpoints"""
    print("=== API Debugging Tool ===")
    
    # Base URL
    base_url = "http://127.0.0.1:8000"
    
    # Test 1: Check if the server is running
    print("\n=== Test 1: Server Health Check ===")
    try:
        response = requests.get(f"{base_url}/")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {str(e)}")
    
    # Test 2: Get the gift wall
    print("\n=== Test 2: Get Gift Wall ===")
    try:
        response = requests.get(f"{base_url}/api/wall/jane_doe")
        print(f"Status: {response.status_code}")
        try:
            data = response.json()
            print(f"Profile: {data.get('profile', {}).get('name')}")
            print(f"Gifts: {len(data.get('gifts', []))}")
        except:
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {str(e)}")
    
    # Test 3: Create a gift
    print("\n=== Test 3: Create Gift ===")
    try:
        gift_data = {
            "child_profile_handle": "jane_doe",
            "from_name": "Test Gifter",
            "from_email": "<EMAIL>",
            "amount_usd": 50,
            "message": "Test gift message",
            "is_anonymous": False
        }
        
        print(f"Request URL: {base_url}/api/wall/jane_doe/gift")
        print(f"Request data: {json.dumps(gift_data, indent=2)}")
        
        response = requests.post(
            f"{base_url}/api/wall/jane_doe/gift", 
            json=gift_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        try:
            data = response.json()
            print(f"Response data: {json.dumps(data, indent=2)}")
            
            if "checkout_url" in data:
                print(f"Checkout URL: {data['checkout_url']}")
            else:
                print("No checkout URL in response")
        except:
            print(f"Response is not JSON: {response.text}")
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n=== Debugging Complete ===")

if __name__ == "__main__":
    debug_api()
