from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
import os
from typing import List, Dict, Any, Optional

# Create FastAPI app
app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# Mock data
users = {
    "<EMAIL>": {
        "id": "1",
        "name": "Test User",
        "email": "<EMAIL>",
        "password": "password123"  # In a real app, this would be hashed
    }
}

# Mock authentication token
tokens = {}

# Models
class UserCreate(BaseModel):
    name: str
    email: str
    password: str

class UserLogin(BaseModel):
    email: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class UserResponse(BaseModel):
    id: str
    name: str
    email: str

# Routes
@app.get("/")
def read_root():
    return {"message": "Welcome to KidVest Backend!"}

@app.post("/api/register")
def register_user(user_data: UserCreate):
    if user_data.email in users:
        raise HTTPException(status_code=400, detail="Email already registered")
    
    user_id = str(len(users) + 1)
    users[user_data.email] = {
        "id": user_id,
        "name": user_data.name,
        "email": user_data.email,
        "password": user_data.password  # In a real app, this would be hashed
    }
    
    return {
        "id": user_id,
        "name": user_data.name,
        "email": user_data.email
    }

@app.post("/api/token")
def login_for_access_token(form_data: UserLogin):
    user = users.get(form_data.email)
    if not user or user["password"] != form_data.password:
        raise HTTPException(
            status_code=401,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # In a real app, this would be a JWT token
    access_token = f"mock_token_{user['id']}"
    tokens[access_token] = user["email"]
    
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/api/users/me")
def read_users_me(authorization: str = None):
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Not authenticated")
    
    token = authorization.replace("Bearer ", "")
    email = tokens.get(token)
    if not email or email not in users:
        raise HTTPException(status_code=401, detail="Invalid token")
    
    user = users[email]
    return {
        "id": user["id"],
        "name": user["name"],
        "email": user["email"]
    }

@app.get("/api/health")
def health_check():
    return {"status": "ok"}

# Onboarding endpoints
class OnboardingStep1(BaseModel):
    full_name: str
    email: str

class OnboardingResponse(BaseModel):
    success: bool
    message: str
    session_token: Optional[str] = None
    current_step: Optional[int] = None
    next_step: Optional[int] = None

@app.post("/api/onboarding/step1")
def onboarding_step1(data: OnboardingStep1):
    # Mock successful response
    return {
        "success": True,
        "message": "Step 1 completed successfully",
        "session_token": "mock_session_token",
        "current_step": 1,
        "next_step": 2
    }

# Run the server
if __name__ == "__main__":
    uvicorn.run("simple_backend:app", host="0.0.0.0", port=8000, reload=True)
