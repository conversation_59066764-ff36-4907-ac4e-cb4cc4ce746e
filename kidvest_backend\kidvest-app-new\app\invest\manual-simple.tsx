import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  TextInput,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';

interface ChildProfile {
  id: string;
  name: string;
  handle: string;
  available_balance: number;
}

interface ParentDashboard {
  parent_info: {
    id: string;
    name: string;
    email: string;
  };
  summary: {
    total_children: number;
    total_available_balance: number;
    total_investments: number;
    total_invested: number;
  };
  children: Array<{
    profile: ChildProfile;
    available_balance: number;
  }>;
}

// Helper function to get auth token
const getAuthToken = async (): Promise<string> => {
  try {
    // For development/testing, we'll use a hardcoded login
    // In production, this should get the token from AsyncStorage, Context, etc.

    console.log('🔐 Getting auth token for Austin...');

    // Login as Austin to get a real JWT token
    const loginResponse = await fetch('http://localhost:8000/api/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        username: '<EMAIL>',
        password: 'austinglusac' // Austin's actual password
      }),
    });

    if (loginResponse.ok) {
      const tokenData = await loginResponse.json();
      const accessToken = tokenData.access_token;
      console.log('✅ Got real JWT token:', accessToken.substring(0, 20) + '...');
      return accessToken;
    } else {
      console.error('❌ Login failed:', await loginResponse.text());
      // Fallback to mock for development
      console.log('⚠️ Using mock auth token as fallback');
      return 'mock-parent-token';
    }
  } catch (error) {
    console.error('Error getting auth token:', error);
    // Fallback to mock for development
    console.log('⚠️ Using mock auth token as fallback');
    return 'mock-parent-token';
  }
};

export default function ManualInvestScreen() {
  const router = useRouter();
  const { childId } = useLocalSearchParams();

  const [loading, setLoading] = useState(true);
  const [investing, setInvesting] = useState(false);
  const [dashboard, setDashboard] = useState<ParentDashboard | null>(null);
  const [selectedChild, setSelectedChild] = useState<string>('');
  const [selectedStock, setSelectedStock] = useState<'TSLA' | 'SPY' | ''>('');
  const [amount, setAmount] = useState<string>('');
  const [error, setError] = useState<string>('');

  useEffect(() => {
    loadDashboard();
    if (childId) {
      setSelectedChild(childId as string);
    }
  }, [childId]);

  const loadDashboard = async () => {
    try {
      setLoading(true);

      // Get auth token (you'll need to implement this based on your auth system)
      const authToken = await getAuthToken();

      const response = await fetch('http://localhost:8000/api/parent/dashboard', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setDashboard(data);
        console.log('✅ Dashboard loaded:', data);
      } else {
        const errorData = await response.json();
        console.error('❌ Dashboard error:', errorData);
        throw new Error(errorData.detail || 'Failed to load dashboard');
      }
    } catch (error) {
      console.error('Error loading dashboard:', error);
      Alert.alert('Error', 'Failed to load dashboard data. Make sure backend is running.');

      // Fallback to mock data for development
      console.log('🔄 Using mock data as fallback...');
      const mockDashboard: ParentDashboard = {
        parent_info: {
          id: "parent-1",
          name: "John Doe",
          email: "<EMAIL>"
        },
        summary: {
          total_children: 2,
          total_available_balance: 150.00,
          total_investments: 3,
          total_invested: 75.25
        },
        children: [
          {
            profile: {
              id: "child-1",
              name: "Alice",
              handle: "alice-doe",
              available_balance: 75.00
            },
            available_balance: 75.00
          },
          {
            profile: {
              id: "child-2",
              name: "Bob",
              handle: "bob-doe",
              available_balance: 75.00
            },
            available_balance: 75.00
          }
        ]
      };
      setDashboard(mockDashboard);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    if (!selectedChild) {
      setError('Please select a child');
      return false;
    }
    if (!selectedStock) {
      setError('Please select a stock (TSLA or SPY)');
      return false;
    }
    if (!amount || parseFloat(amount) <= 0) {
      setError('Please enter a valid amount');
      return false;
    }

    const selectedChildData = dashboard?.children.find(
      child => child.profile.id === selectedChild
    );
    
    if (selectedChildData && parseFloat(amount) > selectedChildData.available_balance) {
      setError(`Amount exceeds available balance of $${selectedChildData.available_balance.toFixed(2)}`);
      return false;
    }

    setError('');
    return true;
  };

  const handleInvest = async () => {
    console.log('🔴 DEBUG: handleInvest called');
    console.log('🔴 DEBUG: Form state:', {
      selectedChild,
      selectedStock,
      amount,
      investing
    });

    if (!validateForm()) {
      console.log('🔴 DEBUG: Form validation failed');
      return;
    }

    console.log('🔴 DEBUG: Form validation passed, starting investment...');
    setInvesting(true);
    try {
      console.log('🚀 Starting investment...', {
        child: selectedChild,
        stock: selectedStock,
        amount: amount
      });

      // Get auth token
      const authToken = await getAuthToken();

      const investmentRequest = {
        child_profile_id: selectedChild,
        stock_symbol: selectedStock,
        amount_usd: parseFloat(amount)
      };

      console.log('📡 Sending investment request:', investmentRequest);

      const response = await fetch('http://localhost:8000/api/invest/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(investmentRequest),
      });

      console.log('📊 Investment response status:', response.status);
      console.log('📊 Investment response headers:', Object.fromEntries(response.headers.entries()));

      const result = await response.json();
      console.log('📊 Investment response body:', result);

      if (response.ok) {
        // Success - show success message
        Alert.alert(
          'Investment Successful! 🎉',
          `Successfully invested $${amount} in ${selectedStock}.\n\n` +
          `Investment ID: ${result.investment_id || 'N/A'}\n` +
          `Transaction ID: ${result.transaction_id || 'N/A'}\n` +
          `Status: ${result.status || 'Pending'}`,
          [
            {
              text: 'View Portfolio',
              onPress: () => router.push(`/portfolio/${selectedChild}`)
            },
            {
              text: 'Make Another Investment',
              onPress: () => {
                setAmount('');
                setSelectedStock('');
                loadDashboard(); // Refresh balances
              }
            }
          ]
        );
      } else {
        // Error - show detailed error message
        let errorMessage = result.detail || result.message || 'Investment failed';

        if (response.status === 401) {
          errorMessage = 'Authentication failed. Please check your login credentials.';
        } else if (response.status === 403) {
          errorMessage = 'Access denied. You may not have permission to invest for this child.';
        } else if (response.status === 400) {
          errorMessage = result.detail || 'Invalid investment request. Please check your inputs.';
        } else if (response.status === 500) {
          errorMessage = 'Server error. The investment could not be processed. Please try again later.';
        }

        throw new Error(errorMessage);
      }
    } catch (error: any) {
      console.error('❌ Investment error:', error);

      let errorMessage = 'Failed to complete investment. Please try again.';

      if (error.message.includes('401')) {
        errorMessage = 'Authentication failed. Please login again.';
      } else if (error.message.includes('400')) {
        errorMessage = error.message || 'Invalid investment request.';
      } else if (error.message.includes('500')) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert(
        'Investment Failed ❌',
        errorMessage
      );
    } finally {
      setInvesting(false);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color="#4CAF50" />
        <Text style={styles.loadingText}>
          Loading dashboard...
        </Text>
      </View>
    );
  }

  if (!dashboard) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.errorText}>
          Failed to load dashboard data
        </Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={loadDashboard}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const selectedChildData = dashboard.children.find(
    child => child.profile.id === selectedChild
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Manual Investment</Text>
        <Text style={styles.subtitle}>Invest gift money for your children</Text>
      </View>

      {/* Summary Card */}
      <View style={styles.summaryCard}>
        <Text style={styles.summaryTitle}>Investment Summary</Text>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Total Available Balance:</Text>
          <Text style={styles.summaryValue}>
            ${dashboard.summary.total_available_balance.toFixed(2)}
          </Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Total Invested:</Text>
          <Text style={styles.summaryValue}>
            ${dashboard.summary.total_invested.toFixed(2)}
          </Text>
        </View>
      </View>

      {/* Child Selection */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Select Child</Text>
        {dashboard.children.map((child) => (
          <TouchableOpacity
            key={child.profile.id}
            style={[
              styles.childOption,
              selectedChild === child.profile.id && styles.selectedOption
            ]}
            onPress={() => setSelectedChild(child.profile.id)}
          >
            <View style={styles.childInfo}>
              <Text style={styles.childName}>{child.profile.name}</Text>
              <Text style={styles.childHandle}>@{child.profile.handle}</Text>
            </View>
            <Text style={styles.childBalance}>
              ${child.available_balance.toFixed(2)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Stock Selection */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Select Stock</Text>
        <View style={styles.stockOptions}>
          <TouchableOpacity
            style={[
              styles.stockOption,
              selectedStock === 'TSLA' && styles.selectedOption
            ]}
            onPress={() => setSelectedStock('TSLA')}
          >
            <Text style={styles.stockSymbol}>TSLA</Text>
            <Text style={styles.stockName}>Tesla, Inc.</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.stockOption,
              selectedStock === 'SPY' && styles.selectedOption
            ]}
            onPress={() => setSelectedStock('SPY')}
          >
            <Text style={styles.stockSymbol}>SPY</Text>
            <Text style={styles.stockName}>S&P 500 ETF</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Amount Input */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Investment Amount</Text>
        <View style={styles.amountInputContainer}>
          <Text style={styles.dollarSign}>$</Text>
          <TextInput
            style={styles.amountInput}
            placeholder="0.00"
            value={amount}
            onChangeText={(text) => {
              const cleanText = text.replace(/[^0-9.]/g, '');
              const parts = cleanText.split('.');
              if (parts.length > 2) return;
              setAmount(cleanText);
              setError('');
            }}
            keyboardType="decimal-pad"
          />
        </View>
        
        {selectedChildData && (
          <View style={styles.balanceInfo}>
            <Text style={styles.balanceLabel}>
              Available Balance: ${selectedChildData.available_balance.toFixed(2)}
            </Text>
            <TouchableOpacity
              style={styles.maxButton}
              onPress={() => setAmount(selectedChildData.available_balance.toFixed(2))}
            >
              <Text style={styles.maxButtonText}>Max</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Error Message */}
      {error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorMessage}>{error}</Text>
        </View>
      ) : null}

      {/* Invest Button */}
      <TouchableOpacity
        style={[
          styles.investButton,
          {
            opacity: investing || !selectedChild || !selectedStock || !amount ? 0.5 : 1,
          }
        ]}
        onPress={() => {
          console.log('🔴 BUTTON CLICKED!');
          Alert.alert('Debug', 'Button was clicked!');
          handleInvest();
        }}
        disabled={investing || !selectedChild || !selectedStock || !amount}
      >
        {investing ? (
          <ActivityIndicator color="white" />
        ) : (
          <Text style={styles.investButtonText}>
            Invest ${amount || '0.00'} in {selectedStock || 'Stock'}
          </Text>
        )}
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#FFFFFF',
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
  },
  summaryCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    backgroundColor: '#F5F5F5',
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333333',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666666',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4CAF50',
  },
  section: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    backgroundColor: '#F5F5F5',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333333',
  },
  childOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    marginBottom: 8,
    backgroundColor: '#FFFFFF',
  },
  selectedOption: {
    borderColor: '#4CAF50',
    backgroundColor: '#E8F5E8',
  },
  childInfo: {
    flex: 1,
  },
  childName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  childHandle: {
    fontSize: 14,
    marginTop: 2,
    color: '#666666',
  },
  childBalance: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4CAF50',
  },
  stockOptions: {
    flexDirection: 'row',
    gap: 12,
  },
  stockOption: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  stockSymbol: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  stockName: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
    color: '#666666',
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  dollarSign: {
    fontSize: 24,
    fontWeight: 'bold',
    marginRight: 8,
    color: '#333333',
  },
  amountInput: {
    flex: 1,
    fontSize: 24,
    fontWeight: 'bold',
    borderBottomWidth: 2,
    borderBottomColor: '#E0E0E0',
    paddingVertical: 8,
    paddingHorizontal: 4,
    color: '#333333',
  },
  balanceInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  balanceLabel: {
    fontSize: 14,
    color: '#666666',
  },
  maxButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  maxButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4CAF50',
  },
  errorContainer: {
    marginBottom: 16,
  },
  errorMessage: {
    color: '#F44336',
    fontSize: 14,
    textAlign: 'center',
  },
  investButton: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 32,
    backgroundColor: '#4CAF50',
  },
  investButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#333333',
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
    color: '#333333',
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#4CAF50',
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
