import requests
import json

def test_new_backend():
    url = "http://localhost:8000/api/register"
    data = {
        "name": "Test Client User",
        "email": "<EMAIL>",
        "password": "password123",
        "user_type": "parent"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"Sending request to {url}")
        print(f"Request data: {json.dumps(data)}")
        
        response = requests.post(url, json=data, headers=headers)
        
        print(f"Response status code: {response.status_code}")
        print(f"Response headers: {response.headers}")
        
        try:
            print(f"Response body: {response.json()}")
        except:
            print(f"Response body (raw): {response.text}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("Testing new backend...")
    success = test_new_backend()
    print(f"Test {'succeeded' if success else 'failed'}")
