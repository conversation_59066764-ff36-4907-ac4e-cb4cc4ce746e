import os
import sys
import psycopg2
from dotenv import load_dotenv
from datetime import datetime, timezone

# Load environment variables
load_dotenv()

# Get database connection details from DATABASE_URL
DATABASE_URL = os.getenv("DATABASE_URL")

print("=== Updating Database Schema ===")

# Parse the DATABASE_URL
if DATABASE_URL.startswith("postgresql://"):
    # Format: postgresql://username:password@host:port/database
    url_without_protocol = DATABASE_URL.replace("postgresql://", "")
    user_pass, host_port_db = url_without_protocol.split("@")
    
    if ":" in user_pass:
        username, password = user_pass.split(":")
    else:
        username = user_pass
        password = ""
    
    host_port, database = host_port_db.split("/")
    
    if ":" in host_port:
        host, port = host_port.split(":")
    else:
        host = host_port
        port = "5432"
else:
    print("Invalid DATABASE_URL format")
    sys.exit(1)

def update_users_table():
    """Update users table schema"""
    try:
        # Connect to the database
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=password
        )
        
        # Create a cursor
        cur = conn.cursor()
        
        # Check if phone_number column exists
        cur.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name = 'phone_number'
        """)
        
        phone_number_exists = cur.fetchone() is not None
        
        if not phone_number_exists:
            print("Adding phone_number column to users table...")
            
            # Add the column
            cur.execute("""
                ALTER TABLE users 
                ADD COLUMN phone_number VARCHAR
            """)
            
            print("phone_number column added successfully!")
        else:
            print("phone_number column already exists in users table.")
        
        # Check if address column exists
        cur.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name = 'address'
        """)
        
        address_exists = cur.fetchone() is not None
        
        if not address_exists:
            print("Adding address column to users table...")
            
            # Add the column
            cur.execute("""
                ALTER TABLE users 
                ADD COLUMN address VARCHAR
            """)
            
            print("address column added successfully!")
        else:
            print("address column already exists in users table.")
        
        # Check if city column exists
        cur.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name = 'city'
        """)
        
        city_exists = cur.fetchone() is not None
        
        if not city_exists:
            print("Adding city column to users table...")
            
            # Add the column
            cur.execute("""
                ALTER TABLE users 
                ADD COLUMN city VARCHAR
            """)
            
            print("city column added successfully!")
        else:
            print("city column already exists in users table.")
        
        # Check if state column exists
        cur.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name = 'state'
        """)
        
        state_exists = cur.fetchone() is not None
        
        if not state_exists:
            print("Adding state column to users table...")
            
            # Add the column
            cur.execute("""
                ALTER TABLE users 
                ADD COLUMN state VARCHAR
            """)
            
            print("state column added successfully!")
        else:
            print("state column already exists in users table.")
        
        # Check if postal_code column exists
        cur.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name = 'postal_code'
        """)
        
        postal_code_exists = cur.fetchone() is not None
        
        if not postal_code_exists:
            print("Adding postal_code column to users table...")
            
            # Add the column
            cur.execute("""
                ALTER TABLE users 
                ADD COLUMN postal_code VARCHAR
            """)
            
            print("postal_code column added successfully!")
        else:
            print("postal_code column already exists in users table.")
        
        # Check if country column exists
        cur.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name = 'country'
        """)
        
        country_exists = cur.fetchone() is not None
        
        if not country_exists:
            print("Adding country column to users table...")
            
            # Add the column
            cur.execute("""
                ALTER TABLE users 
                ADD COLUMN country VARCHAR
            """)
            
            print("country column added successfully!")
        else:
            print("country column already exists in users table.")
        
        # Commit the transaction
        conn.commit()
        
    except Exception as e:
        print(f"Error updating users table: {e}")
        conn.rollback()
    finally:
        # Close the cursor and connection
        cur.close()
        conn.close()

def create_investments_table():
    """Create investments table if it doesn't exist"""
    try:
        # Connect to the database
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=password
        )
        
        # Create a cursor
        cur = conn.cursor()
        
        # Check if investments table exists
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'investments'
            )
        """)
        
        table_exists = cur.fetchone()[0]
        
        if not table_exists:
            print("Creating investments table...")
            
            # Create the table
            cur.execute("""
                CREATE TABLE investments (
                    id UUID PRIMARY KEY,
                    child_profile_id UUID REFERENCES kid_profiles(id),
                    gift_id UUID REFERENCES gifts(id),
                    amount_usd FLOAT NOT NULL,
                    symbol VARCHAR NOT NULL,
                    shares FLOAT NOT NULL,
                    purchase_price FLOAT NOT NULL,
                    status VARCHAR DEFAULT 'pending',
                    transaction_id VARCHAR,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            """)
            
            print("Investments table created successfully!")
        else:
            print("Investments table already exists.")
        
        # Commit the transaction
        conn.commit()
        
    except Exception as e:
        print(f"Error creating investments table: {e}")
        conn.rollback()
    finally:
        # Close the cursor and connection
        cur.close()
        conn.close()

def update_broker_accounts_table():
    """Update broker_accounts table schema"""
    try:
        # Connect to the database
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=password
        )
        
        # Create a cursor
        cur = conn.cursor()
        
        # Check if updated_at column exists
        cur.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'broker_accounts' AND column_name = 'updated_at'
        """)
        
        updated_at_exists = cur.fetchone() is not None
        
        if not updated_at_exists:
            print("Adding updated_at column to broker_accounts table...")
            
            # Add the column
            cur.execute("""
                ALTER TABLE broker_accounts 
                ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            """)
            
            print("updated_at column added successfully!")
        else:
            print("updated_at column already exists in broker_accounts table.")
        
        # Commit the transaction
        conn.commit()
        
    except Exception as e:
        print(f"Error updating broker_accounts table: {e}")
        conn.rollback()
    finally:
        # Close the cursor and connection
        cur.close()
        conn.close()

def main():
    """Main function to update the database schema"""
    update_users_table()
    create_investments_table()
    update_broker_accounts_table()
    print("Database schema updated successfully!")

if __name__ == "__main__":
    main()
    print("\nDone.")
