# KidVest Gift Wall UI

This is a UI for the KidVest Gift Wall feature. It allows friends and family to send monetary gifts to kids through a public-facing page.

## Features

- View a kid's profile and gift wall
- Send monetary gifts with personal messages
- Integration with <PERSON><PERSON> for payment processing
- Anonymous gifting option
- Success and cancel pages for payment flow

## Setup

1. Make sure your FastAPI backend is running on `http://localhost:8000`
2. Start the UI server:

```bash
python server.py
```

3. Open your browser and navigate to `http://localhost:8082/wall/{handle}`
   - Replace `{handle}` with a kid's profile handle (e.g., `zohaib_ali`)

## Using the Gift Wall

### Viewing a Gift Wall

1. Navigate to `http://localhost:8082/wall/{handle}`
2. View the kid's profile information and existing gifts

### Sending a Gift

1. Click the "Send a Gift" button
2. Select or enter a gift amount
3. Enter your name and email
4. Add an optional message
5. Choose whether to make the gift anonymous
6. Click "Continue to Payment"
7. Complete the payment using <PERSON><PERSON>'s checkout page
8. You'll be redirected back to the gift wall after payment

## API Integration

This UI integrates with the following API endpoints:

- `GET /api/wall/{handle}`: Get a kid's gift wall
- `POST /api/wall/{handle}/gift`: Create a new gift

## Stripe Integration

The gift wall uses Stripe Checkout for payment processing. When a gift is created:

1. A gift record is created in the database with status "pending"
2. A Stripe Checkout session is created
3. The user is redirected to the Stripe Checkout page
4. After payment, the user is redirected back to the success or cancel page
5. Stripe sends a webhook to update the gift status to "completed" or "failed"

## Troubleshooting

- If you see "Failed to load gift wall", check that your FastAPI backend is running
- If you encounter CORS issues, make sure your FastAPI backend has CORS enabled
- If payments aren't being processed, check that your Stripe webhook is configured correctly
