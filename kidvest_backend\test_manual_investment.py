#!/usr/bin/env python3

"""
Test script for Phase 3: Manual Investment Flow
Tests the complete flow from parent dashboard to investment execution
"""

import requests
import json
import time
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_PARENT_EMAIL = "<EMAIL>"
TEST_CHILD_HANDLE = "test-child-165125"

def test_manual_investment_flow():
    """Test the complete manual investment flow"""
    print("🧪 Testing Phase 3: Manual Investment Flow")
    print("=" * 60)
    
    # Step 1: Login as parent (simulate authentication)
    print("\n1️⃣ Step 1: Parent Authentication")
    print("-" * 30)
    
    # For testing, we'll use a mock auth token
    # In real implementation, this would come from login
    auth_headers = {
        "Authorization": "Bearer mock-parent-token",
        "Content-Type": "application/json"
    }
    
    print(f"✅ Using mock authentication for parent: {TEST_PARENT_EMAIL}")
    
    # Step 2: Get parent dashboard
    print("\n2️⃣ Step 2: Load Parent Dashboard")
    print("-" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/api/parent/dashboard", headers=auth_headers)
        
        print(f"📡 Dashboard Request: GET /api/parent/dashboard")
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            dashboard = response.json()
            print(f"✅ Dashboard loaded successfully")
            print(f"📋 Parent: {dashboard['parent_info']['name']}")
            print(f"👶 Children: {dashboard['summary']['total_children']}")
            print(f"💰 Total Available Balance: ${dashboard['summary']['total_available_balance']:.2f}")
            print(f"📈 Total Invested: ${dashboard['summary']['total_invested']:.2f}")
            
            # Show children details
            print(f"\n📋 Children Details:")
            for child_data in dashboard['children']:
                child = child_data['profile']
                balance = child_data['available_balance']
                print(f"   • {child['name']} (@{child['handle']}): ${balance:.2f}")
                
        else:
            print(f"❌ Dashboard request failed: {response.status_code}")
            try:
                error = response.json()
                print(f"❌ Error: {error}")
            except:
                print(f"❌ Error text: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Dashboard request error: {str(e)}")
        return False
    
    # Step 3: Find a child with available balance
    print("\n3️⃣ Step 3: Select Child for Investment")
    print("-" * 30)
    
    target_child = None
    for child_data in dashboard['children']:
        if child_data['available_balance'] > 0:
            target_child = child_data
            break
    
    if not target_child:
        print(f"❌ No children with available gift balance found")
        print(f"💡 Create a gift first to test investment flow")
        return False
    
    child_profile = target_child['profile']
    available_balance = target_child['available_balance']
    
    print(f"✅ Selected child: {child_profile['name']} (@{child_profile['handle']})")
    print(f"💰 Available balance: ${available_balance:.2f}")
    
    # Step 4: Test investment request
    print("\n4️⃣ Step 4: Execute Manual Investment")
    print("-" * 30)
    
    # Test with a small amount (10% of available balance or $5, whichever is smaller)
    investment_amount = min(available_balance * 0.1, 5.0)
    investment_stock = "SPY"  # Use SPY for testing
    
    investment_request = {
        "child_profile_id": child_profile['id'],
        "stock_symbol": investment_stock,
        "amount_usd": investment_amount
    }
    
    print(f"📡 Investment Request:")
    print(f"   Child: {child_profile['name']}")
    print(f"   Stock: {investment_stock}")
    print(f"   Amount: ${investment_amount:.2f}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/invest/",
            headers=auth_headers,
            json=investment_request
        )
        
        print(f"\n📊 Investment Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Investment successful!")
            print(f"📋 Response: {json.dumps(result, indent=2)}")
            
            if result.get('success'):
                print(f"\n🎉 INVESTMENT DETAILS:")
                print(f"   Investment ID: {result.get('investment_id')}")
                print(f"   Transaction ID: {result.get('transaction_id')}")
                print(f"   Shares Purchased: {result.get('shares_purchased', 0):.4f}")
                print(f"   Purchase Price: ${result.get('purchase_price', 0):.2f}")
                print(f"   Executed At: {result.get('executed_at')}")
                
                return True
            else:
                print(f"❌ Investment marked as failed: {result.get('message')}")
                return False
                
        else:
            print(f"❌ Investment request failed: {response.status_code}")
            try:
                error = response.json()
                print(f"❌ Error: {json.dumps(error, indent=2)}")
            except:
                print(f"❌ Error text: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Investment request error: {str(e)}")
        return False

def test_investment_validations():
    """Test investment validation scenarios"""
    print("\n🔍 Testing Investment Validations")
    print("=" * 40)
    
    auth_headers = {
        "Authorization": "Bearer mock-parent-token",
        "Content-Type": "application/json"
    }
    
    # Test cases for validation
    test_cases = [
        {
            "name": "Invalid Stock Symbol",
            "request": {
                "child_profile_id": "550e8400-e29b-41d4-a716-446655440000",
                "stock_symbol": "INVALID",
                "amount_usd": 10.0
            },
            "expected_status": 422
        },
        {
            "name": "Zero Amount",
            "request": {
                "child_profile_id": "550e8400-e29b-41d4-a716-446655440000",
                "stock_symbol": "TSLA",
                "amount_usd": 0.0
            },
            "expected_status": 422
        },
        {
            "name": "Negative Amount",
            "request": {
                "child_profile_id": "550e8400-e29b-41d4-a716-446655440000",
                "stock_symbol": "SPY",
                "amount_usd": -5.0
            },
            "expected_status": 422
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: {test_case['name']}")
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/invest/",
                headers=auth_headers,
                json=test_case['request']
            )
            
            if response.status_code == test_case['expected_status']:
                print(f"   ✅ Validation working: {response.status_code}")
            else:
                print(f"   ❌ Unexpected status: {response.status_code} (expected {test_case['expected_status']})")
                
        except Exception as e:
            print(f"   ❌ Request error: {str(e)}")

def test_alpaca_integration():
    """Test Alpaca API integration components"""
    print("\n🔌 Testing Alpaca Integration")
    print("=" * 40)
    
    try:
        from app.alpaca_service import get_alpaca_market_data, place_alpaca_order
        
        # Test market data retrieval
        print("1. Testing market data retrieval...")
        
        market_data, error = get_alpaca_market_data("SPY")
        if error:
            print(f"   ❌ Market data error: {error}")
        else:
            print(f"   ✅ Market data retrieved: {market_data}")
        
        # Test order placement (with mock account)
        print("\n2. Testing order placement...")
        print("   ⚠️ Using mock account ID for testing")
        
        # This will likely fail without a real account, but tests the integration
        order_result, order_error = place_alpaca_order(
            account_id="mock-account-id",
            symbol="SPY",
            amount_usd=5.0
        )
        
        if order_error:
            print(f"   ⚠️ Order error (expected): {order_error}")
        else:
            print(f"   ✅ Order result: {order_result}")
            
    except Exception as e:
        print(f"❌ Alpaca integration test error: {str(e)}")

def main():
    """Main test function"""
    print("🚀 Phase 3: Manual Investment Flow - Test Suite")
    print("=" * 60)
    
    # Check if backend is running
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ Backend server is running")
        else:
            print(f"⚠️ Backend server responded with status {response.status_code}")
    except:
        print("❌ Backend server is not running")
        print("💡 Start the backend with: uvicorn app.main:app --reload")
        return
    
    # Run tests
    print(f"\n🧪 Starting Manual Investment Flow Tests")
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test 1: Complete investment flow
    success = test_manual_investment_flow()
    
    # Test 2: Validation scenarios
    test_investment_validations()
    
    # Test 3: Alpaca integration
    test_alpaca_integration()
    
    # Summary
    print(f"\n" + "=" * 60)
    print(f"🏁 TEST SUMMARY")
    print(f"=" * 60)
    
    if success:
        print(f"✅ MANUAL INVESTMENT FLOW: WORKING")
        print(f"✅ Parent can view dashboard with children and balances")
        print(f"✅ Parent can execute investments for children")
        print(f"✅ Investments are properly recorded and tracked")
        print(f"\n🎉 Phase 3 implementation is ready for use!")
    else:
        print(f"❌ MANUAL INVESTMENT FLOW: NEEDS ATTENTION")
        print(f"\n🔧 Check the following:")
        print(f"   • Database migration completed")
        print(f"   • Alpaca API credentials configured")
        print(f"   • Parent authentication working")
        print(f"   • Child profiles with gift balances exist")
    
    print(f"\n🧪 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
