from app.database import SessionLocal
from app.models import User, UserType
from app.auth import get_password_hash
import uuid

def create_test_user():
    """Create a test user for login"""
    db = SessionLocal()
    try:
        # Check if test user already exists
        test_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if test_user:
            print(f"Test user already exists: {test_user.id} ({test_user.email})")
            print(f"Password: 'password'")
            return
        
        # Create test user
        test_user = User(
            id=uuid.uuid4(),
            name="Test User",
            email="<EMAIL>",
            hashed_password=get_password_hash("password"),
            user_type=UserType.parent,
            is_active=True
        )
        db.add(test_user)
        db.commit()
        print(f"Created test user: {test_user.id} ({test_user.email})")
        print(f"Password: 'password'")
    finally:
        db.close()

if __name__ == "__main__":
    create_test_user()
