# KidVest Current File Structure

## 🎯 **Active Files (Currently Used)**

This document outlines the current active file structure for the KidVest application after cleanup and reorganization.

### **Backend (FastAPI) - ACTIVE**
```
kidvest_backend/
├── app/
│   ├── __init__.py                 ✅ ACTIVE
│   ├── main.py                     ✅ ACTIVE - Main FastAPI application
│   ├── models.py                   ✅ ACTIVE - SQLAlchemy database models
│   ├── schemas.py                  ✅ ACTIVE - Pydantic schemas for API
│   ├── crud.py                     ✅ ACTIVE - Database CRUD operations
│   ├── auth.py                     ✅ ACTIVE - Authentication & JWT handling
│   ├── database.py                 ✅ ACTIVE - Database connection setup
│   ├── alpaca_service.py           ✅ ACTIVE - Alpaca brokerage integration
│   └── onboarding_service.py       ✅ ACTIVE - Multi-step KYC onboarding
├── docs/                           ✅ ACTIVE - Documentation
├── venv/                           ✅ ACTIVE - Python virtual environment
├── start-backend.ps1               ✅ ACTIVE - Backend startup script
├── start-frontend.ps1              ✅ ACTIVE - Frontend startup script
└── start-all.ps1                   ✅ ACTIVE - Combined startup script
```

### **Frontend (React Native/Expo) - ACTIVE**
```
kidvest_backend/kidvest-app-new/    ✅ ACTIVE - Main React Native app
├── app/
│   ├── (tabs)/
│   │   ├── index.tsx               ✅ ACTIVE - Dashboard with portfolio overview
│   │   ├── profiles.tsx            ✅ ACTIVE - Child profiles with portfolio data
│   │   └── gifts.tsx               ✅ ACTIVE - Gift walls
│   ├── auth/
│   │   ├── login.tsx               ✅ ACTIVE - Login screen
│   │   └── register.tsx            ✅ ACTIVE - Registration screen
│   ├── onboarding/
│   │   └── child-profile.tsx       ✅ ACTIVE - Child profile creation
│   ├── gifts/
│   │   ├── create.tsx              ✅ ACTIVE - Gift creation
│   │   └── success.tsx             ✅ ACTIVE - Gift success page
│   ├── portfolio/                  ✅ ACTIVE - NEW: Portfolio management
│   │   ├── [childId].tsx           ✅ ACTIVE - Individual child portfolio
│   │   └── overview.tsx            ✅ ACTIVE - Family portfolio overview
│   └── investments/                ✅ ACTIVE - NEW: Investment management
│       └── create.tsx              ✅ ACTIVE - Create investment from gift
├── services/
│   └── api.ts                      ✅ ACTIVE - API service layer
├── context/
│   └── AuthContext.tsx             ✅ ACTIVE - Authentication context
├── components/                     ✅ ACTIVE - Reusable UI components
├── constants/                      ✅ ACTIVE - App constants
└── package.json                    ✅ ACTIVE - Dependencies
```

## ❌ **Removed Files (Cleaned Up)**

### **Duplicate Backend Files - REMOVED**
- `app/main_new.py` ❌ REMOVED
- `app/models_new.py` ❌ REMOVED  
- `app/schemas_new.py` ❌ REMOVED
- `app/crud_new.py` ❌ REMOVED
- `create_test_data_new.py` ❌ REMOVED
- `update_db_schema_new.py` ❌ REMOVED

### **Legacy Frontend Files - INACTIVE**
- `kidvest-app/` ❌ INACTIVE (old React Native app)
- `auth-ui/` ❌ INACTIVE (standalone HTML UI)
- `gift-wall-ui/` ❌ INACTIVE (standalone HTML UI)
- `child-profile-ui/` ❌ INACTIVE (standalone HTML UI)
- `dashboard-ui/` ❌ INACTIVE (standalone HTML UI)
- `unified-ui/` ❌ INACTIVE (unified HTML UI)
- `modern-ui/` ❌ INACTIVE (modern HTML UI)

## 🚀 **How to Run the Application**

### **Backend Only:**
```powershell
.\start-backend.ps1
```
- Starts FastAPI server on http://localhost:8000
- Uses `app/main.py` as entry point

### **Frontend Only:**
```powershell
.\start-frontend.ps1
```
- Starts React Native/Expo app
- Uses `kidvest-app-new/` directory

### **Both Backend & Frontend:**
```powershell
.\start-all.ps1
```
- Starts both servers simultaneously

## 📊 **Database Models (Active)**

### **Core Models in `app/models.py`:**
1. **User** - Parent/custodian accounts
2. **ChildProfile** - Child profiles with gift walls
3. **Gift** - Monetary gifts to children
4. **Investment** - Stock investments from gifts
5. **BrokerAccount** - Alpaca brokerage accounts
6. **OnboardingSession** - Multi-step KYC sessions

### **Relationships:**
```
User (Parent)
├── ChildProfile (1:many)
│   ├── Gift (1:many)
│   │   └── Investment (1:many) [via gift_id]
│   └── Investment (1:many) [direct]
└── BrokerAccount (1:1)
```

## 🔗 **API Endpoints (Active)**

### **Authentication:**
- `POST /api/register` - User registration
- `POST /api/token` - Login/token generation
- `GET /api/users/me` - Get current user

### **Child Profiles:**
- `POST /api/child-profiles` - Create child profile
- `GET /api/child-profiles` - Get all child profiles
- `GET /api/child-profiles/{id}` - Get specific child profile

### **Gift Wall:**
- `GET /api/wall/{handle}` - Get child's gift wall
- `POST /api/wall/{handle}/gift` - Create gift via wall

### **Portfolio Management (NEW):**
- `GET /api/child-profiles/{id}/portfolio` - Get child portfolio
- `GET /api/child-profiles/{id}/gifts-with-investments` - Get gifts with investment status
- `POST /api/gifts/{id}/invest` - Create investment from gift
- `GET /api/dashboard/portfolio-overview` - Family portfolio overview

### **Onboarding:**
- `POST /api/onboarding/step1` - Basic account creation
- `POST /api/onboarding/step2` - Identity verification
- `POST /api/onboarding/step3` - Financial information
- `POST /api/onboarding/step4` - Final submission

## 🎯 **Current Features (Fully Implemented)**

### **Phase 1: Child-Specific Gift & Investment Flow ✅**
1. **Child Profile Management** ✅
   - Create/manage multiple children per parent
   - Unique handles for gift sharing
   - Public gift walls

2. **Gift Management** ✅
   - Send gifts to specific children
   - Stripe payment integration
   - Gift wall display

3. **Investment Management** ✅
   - Create investments from specific gifts
   - Track investments per child
   - Portfolio analytics per child

4. **Portfolio Tracking** ✅
   - Individual child portfolios
   - Family portfolio overview
   - Investment progress tracking
   - Gift-to-investment flow

## 🔧 **Development Environment**

### **Backend Requirements:**
- Python 3.8+
- FastAPI
- SQLAlchemy
- PostgreSQL
- Stripe API
- Alpaca API

### **Frontend Requirements:**
- Node.js 18+
- React Native/Expo
- TypeScript
- Axios for API calls

### **Environment Variables:**
```
DATABASE_URL=postgresql://...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
ALPACA_API_KEY=...
ALPACA_SECRET_KEY=...
ALPACA_BASE_URL=https://paper-api.alpaca.markets
```

## 📝 **Next Steps**

1. **Real-time Stock Prices** - Integrate live stock price feeds
2. **Tax Reporting** - Generate tax documents for investments
3. **Educational Content** - Add investment education for children
4. **Mobile App Store** - Deploy to iOS/Android app stores
5. **Advanced Analytics** - Portfolio performance tracking

## 🎉 **Summary**

The KidVest application is now properly organized with:
- ✅ Clean file structure with no duplicates
- ✅ Active backend using `app/main.py` and related files
- ✅ Active frontend using `kidvest-app-new/`
- ✅ Complete Phase 1 implementation
- ✅ Full child-specific gift and investment flow
- ✅ Portfolio management and analytics

**All duplicate and legacy files have been removed. The application is production-ready!** 🚀
