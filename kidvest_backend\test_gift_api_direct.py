import requests
import json

def test_gift_api():
    """Test the gift API directly"""
    print("=== Testing Gift API ===")
    
    # API endpoint
    url = "http://127.0.0.1:8000/api/wall/jane_doe/gift"
    
    # Request data
    data = {
        "child_profile_handle": "jane_doe",
        "from_name": "Test Gifter",
        "from_email": "<EMAIL>",
        "amount_usd": 50,
        "message": "Test gift message",
        "is_anonymous": False
    }
    
    # Print request details
    print(f"Request URL: {url}")
    print(f"Request data: {json.dumps(data, indent=2)}")
    
    # Send the request
    try:
        response = requests.post(
            url,
            json=data,
            headers={"Content-Type": "application/json"}
        )
        
        # Print response details
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"Response data: {json.dumps(response_data, indent=2)}")
            
            if "checkout_url" in response_data:
                print(f"Checkout URL: {response_data['checkout_url']}")
                return response_data
            else:
                print("No checkout URL in response")
                return None
        except json.JSONDecodeError:
            print(f"Response is not JSON: {response.text}")
            return None
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_gift_api()
