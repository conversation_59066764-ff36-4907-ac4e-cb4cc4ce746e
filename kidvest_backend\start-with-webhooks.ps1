#!/usr/bin/env powershell

# KidVest Backend with Stripe Webhook Support
Write-Host "🚀 Starting KidVest Backend with Stripe Webhook Support..." -ForegroundColor Green

# Set execution policy for this session
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process -Force

# Function to check if ngrok is installed
function Test-NgrokInstalled {
    try {
        $null = Get-Command ngrok -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Function to check if a port is in use
function Test-PortInUse {
    param ([int]$Port)
    $connections = Get-NetTCPConnection -State Listen -ErrorAction SilentlyContinue | Where-Object { $_.LocalPort -eq $Port }
    return ($null -ne $connections)
}

# Function to start ngrok tunnel
function Start-NgrokTunnel {
    Write-Host "🌐 Starting ngrok tunnel for Stripe webhooks..." -ForegroundColor Cyan
    
    if (-not (Test-NgrokInstalled)) {
        Write-Host "❌ ngrok not found. Please install ngrok first:" -ForegroundColor Red
        Write-Host "   1. Download from https://ngrok.com/download" -ForegroundColor Yellow
        Write-Host "   2. Extract to a folder in your PATH" -ForegroundColor Yellow
        Write-Host "   3. Run 'ngrok authtoken YOUR_TOKEN' with your ngrok token" -ForegroundColor Yellow
        return $false
    }
    
    # Kill any existing ngrok processes
    Get-Process -Name "ngrok" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 2
    
    # Start ngrok in background
    Write-Host "   Starting ngrok tunnel..." -ForegroundColor Gray
    Start-Process -FilePath "ngrok" -ArgumentList @("http", "8000", "--log=stdout") -WindowStyle Minimized
    
    # Wait for ngrok to start
    Write-Host "   Waiting for ngrok to initialize..." -ForegroundColor Gray
    Start-Sleep -Seconds 8
    
    # Get ngrok URL
    try {
        $ngrokApi = Invoke-RestMethod -Uri "http://localhost:4040/api/tunnels" -Method Get -TimeoutSec 10
        $publicUrl = $ngrokApi.tunnels[0].public_url
        
        if ($publicUrl) {
            Write-Host "✅ ngrok tunnel started successfully!" -ForegroundColor Green
            Write-Host "🔗 Public URL: $publicUrl" -ForegroundColor Cyan
            
            # Save webhook URL to file for reference
            $webhookUrl = "$publicUrl/webhook/"
            $webhookUrl | Out-File -FilePath "webhook_url.txt" -Encoding UTF8
            
            return $publicUrl
        } else {
            Write-Host "❌ Failed to get ngrok URL from API" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Failed to connect to ngrok API: $_" -ForegroundColor Red
        Write-Host "   Make sure ngrok is running and accessible on port 4040" -ForegroundColor Yellow
        return $false
    }
}

# Function to display webhook setup instructions
function Show-WebhookInstructions {
    param ([string]$NgrokUrl)
    
    $webhookUrl = "$NgrokUrl/webhook/"
    
    Write-Host ""
    Write-Host "🔧 STRIPE WEBHOOK SETUP REQUIRED:" -ForegroundColor Yellow
    Write-Host "=" * 60 -ForegroundColor Gray
    Write-Host "1. Go to Stripe Dashboard: https://dashboard.stripe.com/webhooks" -ForegroundColor White
    Write-Host "2. Click 'Add endpoint'" -ForegroundColor White
    Write-Host "3. Enter webhook URL: $webhookUrl" -ForegroundColor Cyan
    Write-Host "4. Select these events:" -ForegroundColor White
    Write-Host "   • checkout.session.completed" -ForegroundColor Gray
    Write-Host "   • checkout.session.expired" -ForegroundColor Gray
    Write-Host "5. Click 'Add endpoint'" -ForegroundColor White
    Write-Host "6. Copy the 'Signing secret' (starts with whsec_)" -ForegroundColor White
    Write-Host "7. Update your .env file:" -ForegroundColor White
    Write-Host "   STRIPE_WEBHOOK_SECRET=whsec_your_secret_here" -ForegroundColor Gray
    Write-Host "8. Restart this script after updating .env" -ForegroundColor White
    Write-Host ""
    Write-Host "💡 The webhook URL is saved to 'webhook_url.txt' for reference" -ForegroundColor Yellow
    Write-Host ""
}

# Function to test webhook configuration
function Test-WebhookConfig {
    # Check if webhook secret is configured
    $webhookSecret = $env:STRIPE_WEBHOOK_SECRET
    if (-not $webhookSecret -or $webhookSecret -eq "whsec_your_webhook_secret_here") {
        Write-Host "⚠️ Webhook secret not configured in .env file" -ForegroundColor Yellow
        return $false
    }
    
    Write-Host "✅ Webhook secret configured" -ForegroundColor Green
    return $true
}

# Main execution
Write-Host ""
Write-Host "🎉 KidVest Backend with Webhook Support" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Gray

# Check if virtual environment exists
if (-not (Test-Path ".\venv\Scripts\activate.ps1")) {
    Write-Host "❌ Virtual environment not found. Please run setup first." -ForegroundColor Red
    exit 1
}

# Activate virtual environment
Write-Host "📦 Activating virtual environment..." -ForegroundColor Yellow
& ".\venv\Scripts\activate.ps1"

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "⚠️ .env file not found. Creating template..." -ForegroundColor Yellow
    @"
DATABASE_URL=postgresql://username:password@localhost/kidvest
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here
ALPACA_BASE_URL=https://paper-api.alpaca.markets
"@ | Out-File -FilePath ".env" -Encoding UTF8
    Write-Host "📝 Template .env file created. Please update with your actual values." -ForegroundColor Yellow
}

# Load environment variables
Get-Content ".env" | ForEach-Object {
    if ($_ -match "^([^=]+)=(.*)$") {
        [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
    }
}

# Check if backend is already running
if (Test-PortInUse -Port 8000) {
    Write-Host "⚠️ Port 8000 is already in use. Backend may already be running." -ForegroundColor Yellow
    $continue = Read-Host "Continue anyway? (y/n)"
    if ($continue -ne "y") {
        exit 1
    }
}

# Step 1: Start ngrok tunnel
Write-Host ""
Write-Host "🌐 Setting up ngrok tunnel..." -ForegroundColor Cyan
$ngrokUrl = Start-NgrokTunnel

if ($ngrokUrl) {
    # Show webhook setup instructions
    Show-WebhookInstructions -NgrokUrl $ngrokUrl
    
    # Test webhook configuration
    $webhookConfigured = Test-WebhookConfig
    
    Write-Host "📋 Server Information:" -ForegroundColor Yellow
    Write-Host "   📊 API Documentation: http://localhost:8000/docs" -ForegroundColor White
    Write-Host "   🎁 Gift Wall UI: http://localhost:8000/wall/test-child-165125" -ForegroundColor White
    Write-Host "   📱 React Native App: http://localhost:8081" -ForegroundColor White
    Write-Host "   🔗 Public Webhook URL: $ngrokUrl/webhook/" -ForegroundColor White
    Write-Host "   🌐 ngrok Web Interface: http://localhost:4040" -ForegroundColor White
    Write-Host ""
    
    if ($webhookConfigured) {
        Write-Host "✅ Webhook configuration: READY" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Webhook configuration: NEEDS SETUP" -ForegroundColor Yellow
    }
    
} else {
    Write-Host ""
    Write-Host "⚠️ ngrok tunnel failed to start. Webhook functionality will be limited." -ForegroundColor Yellow
    Write-Host "   You can still test locally, but Stripe webhooks won't work." -ForegroundColor Gray
    Write-Host "   Gifts will remain in 'pending' status until webhook is configured." -ForegroundColor Gray
    Write-Host ""
}

# Step 2: Start backend server
Write-Host "🚀 Starting backend server..." -ForegroundColor Green
Write-Host ""

# Set PYTHONPATH
$env:PYTHONPATH = "$PSScriptRoot"

try {
    # Start the backend server
    uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
} catch {
    Write-Host "❌ Failed to start backend server: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 Troubleshooting:" -ForegroundColor Yellow
    Write-Host "   1. Make sure Python and uvicorn are installed" -ForegroundColor Gray
    Write-Host "   2. Check if port 8000 is available" -ForegroundColor Gray
    Write-Host "   3. Verify virtual environment is activated" -ForegroundColor Gray
    Write-Host "   4. Check .env file configuration" -ForegroundColor Gray
}
