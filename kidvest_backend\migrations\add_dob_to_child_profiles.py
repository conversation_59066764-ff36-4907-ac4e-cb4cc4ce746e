from sqlalchemy import create_engine, Column, String, MetaData, Table, text
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database URL from environment
DATABASE_URL = os.getenv("DATABASE_URL")
if not DATABASE_URL:
    raise ValueError("DATABASE_URL environment variable not set")

print(f"Connecting to database: {DATABASE_URL[:20]}...")

# Create engine
engine = create_engine(DATABASE_URL)

# Create metadata
metadata = MetaData()
metadata.bind = engine

# Define the child_profiles table
child_profiles = Table('child_profiles', metadata, autoload_with=engine)

def run_migration():
    """Add dob column to child_profiles table"""
    print("Starting migration: Add dob column to child_profiles table")

    # Check if the column already exists
    if 'dob' not in child_profiles.columns:
        print("Adding dob column to child_profiles table...")

        # Create a connection
        with engine.connect() as conn:
            # Begin a transaction
            with conn.begin():
                # Add the dob column
                conn.execute(text("ALTER TABLE child_profiles ADD COLUMN dob VARCHAR"))
                print("Column added successfully")
    else:
        print("Column dob already exists in child_profiles table")

    print("Migration completed successfully")

if __name__ == "__main__":
    run_migration()
