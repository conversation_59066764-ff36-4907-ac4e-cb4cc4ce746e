<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KidVest - Child Profiles</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
        }
        .container {
            max-width: 1000px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .card-header {
            background-color: #4a6cf7;
            color: white;
            font-weight: bold;
        }
        .btn-primary {
            background-color: #4a6cf7;
            border-color: #4a6cf7;
        }
        .profile-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            transition: transform 0.3s;
        }
        .profile-card:hover {
            transform: translateY(-5px);
        }
        .profile-header {
            background-color: #4a6cf7;
            color: white;
            padding: 15px;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }
        .profile-body {
            padding: 20px;
        }
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: #4a6cf7;
            margin: 0 auto 15px;
        }
        .profile-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        #api-response {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f8f9fa;
            white-space: pre-wrap;
            display: none;
        }
        .navbar {
            background-color: #4a6cf7;
            margin-bottom: 30px;
        }
        .navbar-brand {
            color: white;
            font-weight: bold;
        }
        .nav-link {
            color: rgba(255, 255, 255, 0.8);
        }
        .nav-link:hover {
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">KidVest</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard-ui/">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/child-profile-ui/">Child Profiles</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/gift-wall-ui/">Gift Walls</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <span class="navbar-text me-3" id="user-name">Welcome, User</span>
                    <button class="btn btn-outline-light btn-sm" id="logout-btn">Logout</button>
                </div>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Child Profiles</h5>
                        <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#addProfileModal">
                            <i class="bi bi-plus-circle"></i> Add Child Profile
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row" id="profiles-container">
                            <!-- Profiles will be loaded here -->
                            <div class="col-12 text-center py-5" id="loading-profiles">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading profiles...</p>
                            </div>
                            <div class="col-12 text-center py-5 d-none" id="no-profiles">
                                <i class="bi bi-person-plus" style="font-size: 3rem; color: #ccc;"></i>
                                <p class="mt-2">No child profiles yet. Click "Add Child Profile" to get started.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- API Response Display -->
        <div id="api-response"></div>
    </div>
    
    <!-- Add Profile Modal -->
    <div class="modal fade" id="addProfileModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Child Profile</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="add-profile-form">
                        <div class="mb-3">
                            <label for="name" class="form-label">Child's Name</label>
                            <input type="text" class="form-control" id="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="handle" class="form-label">Profile Handle (for gift wall URL)</label>
                            <div class="input-group">
                                <span class="input-group-text">kidvest.com/wall/</span>
                                <input type="text" class="form-control" id="handle" required>
                            </div>
                            <div class="form-text">Only use letters, numbers, and underscores. No spaces.</div>
                        </div>
                        <div class="mb-3">
                            <label for="dob" class="form-label">Date of Birth</label>
                            <input type="date" class="form-control" id="dob" required>
                        </div>
                        <div class="mb-3">
                            <label for="bio" class="form-label">Bio (optional)</label>
                            <textarea class="form-control" id="bio" rows="3"></textarea>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="is_public" checked>
                            <label class="form-check-label" for="is_public">
                                Make profile public (visible on gift wall)
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="save-profile-btn">Save Profile</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Edit Profile Modal -->
    <div class="modal fade" id="editProfileModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Child Profile</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="edit-profile-form">
                        <input type="hidden" id="edit-profile-id">
                        <div class="mb-3">
                            <label for="edit-name" class="form-label">Child's Name</label>
                            <input type="text" class="form-control" id="edit-name" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit-handle" class="form-label">Profile Handle</label>
                            <div class="input-group">
                                <span class="input-group-text">kidvest.com/wall/</span>
                                <input type="text" class="form-control" id="edit-handle" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="edit-dob" class="form-label">Date of Birth</label>
                            <input type="date" class="form-control" id="edit-dob" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit-bio" class="form-label">Bio (optional)</label>
                            <textarea class="form-control" id="edit-bio" rows="3"></textarea>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="edit-is_public">
                            <label class="form-check-label" for="edit-is_public">
                                Make profile public (visible on gift wall)
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="update-profile-btn">Update Profile</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this child profile? This action cannot be undone.</p>
                    <p><strong>Profile: </strong><span id="delete-profile-name"></span></p>
                    <input type="hidden" id="delete-profile-id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirm-delete-btn">Delete Profile</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
