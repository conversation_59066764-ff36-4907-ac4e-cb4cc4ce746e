from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, EmailStr
from typing import Optional
import uvicorn
import uuid
from datetime import datetime

# Create FastAPI app
app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# User models
class UserCreate(BaseModel):
    name: str
    email: EmailStr
    password: str
    user_type: str
    phone_number: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None

class UserResponse(BaseModel):
    id: str
    name: str
    email: str
    user_type: str
    is_active: bool = True
    created_at: str

# In-memory user storage for testing
users = {}

# Root route
@app.get("/")
def read_root():
    print("Root endpoint called")
    return {"message": "Welcome to Minimal KidVest Backend!"}

# Register route
@app.post("/api/register", response_model=UserResponse)
def register_user(user_data: UserCreate):
    print(f"\n=== Registration Request ===\nData: {user_data}")
    
    try:
        # Check if user already exists
        if any(u.email == user_data.email for u in users.values()):
            print(f"Email already registered: {user_data.email}")
            raise HTTPException(status_code=400, detail="Email already registered")
        
        # Create new user
        user_id = str(uuid.uuid4())
        created_at = datetime.now().isoformat()
        
        # Store user in memory
        users[user_id] = {
            "id": user_id,
            "name": user_data.name,
            "email": user_data.email,
            "password": user_data.password,  # In a real app, this would be hashed
            "user_type": user_data.user_type,
            "phone_number": user_data.phone_number,
            "address": user_data.address,
            "city": user_data.city,
            "state": user_data.state,
            "postal_code": user_data.postal_code,
            "country": user_data.country,
            "is_active": True,
            "created_at": created_at
        }
        
        print(f"User registered successfully: {user_id} ({user_data.email})")
        
        # Return user response
        return {
            "id": user_id,
            "name": user_data.name,
            "email": user_data.email,
            "user_type": user_data.user_type,
            "is_active": True,
            "created_at": created_at
        }
    except Exception as e:
        print(f"Error registering user: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error registering user: {str(e)}")

# Login route (simplified)
@app.post("/api/token")
def login(email: str, password: str):
    # Find user by email
    user = next((u for u in users.values() if u["email"] == email), None)
    
    if not user or user["password"] != password:
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    # In a real app, this would generate a JWT token
    return {"access_token": f"fake_token_{user['id']}", "token_type": "bearer"}

# User profile route
@app.get("/api/users/me/{user_id}")
def get_user(user_id: str):
    if user_id not in users:
        raise HTTPException(status_code=404, detail="User not found")
    
    user = users[user_id]
    return {
        "id": user["id"],
        "name": user["name"],
        "email": user["email"],
        "user_type": user["user_type"],
        "is_active": user["is_active"],
        "created_at": user["created_at"]
    }

if __name__ == "__main__":
    print("Starting minimal backend on port 8003...")
    uvicorn.run(app, host="0.0.0.0", port=8003)
