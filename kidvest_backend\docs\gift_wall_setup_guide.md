# Gift Wall Setup & Testing Guide

## 🎯 **The Issue & Solution**

### **Problem:**
You were trying to access the gift wall at `http://localhost:8000/wall/...` but the FastAPI backend only provides the API endpoints, not the HTML interface.

### **Solution:**
<PERSON><PERSON><PERSON> uses a **two-server architecture**:
1. **Backend (FastAPI)**: Port 8000 - API endpoints only
2. **Frontend (Gift Wall UI)**: Port 8082 - HTML/CSS/JS interface

## 🚀 **Quick Start**

### **Option 1: Use the PowerShell Script (Recommended)**
```powershell
.\start-gift-wall.ps1
```

### **Option 2: Manual Startup**
```powershell
# Terminal 1: Start Backend
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process
.\venv\Scripts\activate
uvicorn app.main:app --reload

# Terminal 2: Start Gift Wall UI
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process
.\venv\Scripts\activate
cd gift-wall-ui
python server.py
```

## 🧪 **Test the Stripe Checkout**

### **1. Access the Gift Wall**
```
http://localhost:8082/wall/test-child-163809
```

### **2. Create a Test Gift**
- **Name**: "Test User"
- **Email**: "<EMAIL>"
- **Amount**: "$25"
- **Message**: "Test gift for Stripe checkout"

### **3. Click "Post & Gift"**
You should now see:
1. ✅ "Redirecting to payment..." message
2. ✅ Redirect to Stripe checkout page
3. ✅ Stripe payment form

### **4. Complete Test Payment**
- **Test Card**: `4242 4242 4242 4242`
- **Expiry**: Any future date (e.g., 12/25)
- **CVC**: Any 3 digits (e.g., 123)
- **Name**: Any name
- **Complete the payment**

### **5. Verify Gift Appears**
- Return to the gift wall
- Your gift should now be visible
- Check that the amount and message are correct

## 🔧 **Server Architecture**

### **Backend Server (Port 8000)**
- **Purpose**: API endpoints for gift creation, Stripe integration
- **URL**: `http://localhost:8000`
- **Endpoints**:
  - `GET /api/wall/{handle}` - Get gift wall data
  - `POST /api/wall/{handle}/gift` - Create new gift
  - `POST /webhook/` - Stripe webhook handler

### **Frontend Server (Port 8082)**
- **Purpose**: Serves HTML/CSS/JS files
- **URL**: `http://localhost:8082`
- **Routes**:
  - `/wall/{handle}` - Gift wall interface
  - `/wall/{handle}/success` - Payment success page
  - `/wall/{handle}/cancel` - Payment cancelled page

## 🔍 **Debugging Information**

### **Check if Servers are Running**
```powershell
# Test Backend
Invoke-WebRequest -Uri "http://localhost:8000/" -Method GET

# Test Frontend
Invoke-WebRequest -Uri "http://localhost:8082/" -Method GET
```

### **Browser Console Logs**
When you click "Post & Gift", you should see:
```javascript
🎁 createGift function called
Form elements: {fromName: "Test User", fromEmail: "<EMAIL>", ...}
🚀 Creating gift with data: {...}
🌐 API URL: http://127.0.0.1:8000/api/wall/test-child-163809/gift
📡 Making API request...
📨 Response received: {status: 200, statusText: "OK", ok: true}
✅ API response data: {success: true, gift_id: "...", checkout_url: "https://checkout.stripe.com/..."}
🔗 Checkout URL found: https://checkout.stripe.com/c/pay/cs_test_...
🎯 About to redirect to Stripe...
🚀 Redirecting now to: https://checkout.stripe.com/c/pay/cs_test_...
```

### **If You See Errors**
- **"Failed to fetch"** → Backend not running or wrong URL
- **"No checkout URL"** → Stripe API key issue
- **"Profile not found"** → Child profile doesn't exist
- **No console logs** → JavaScript error, check browser console

## 📊 **Expected Flow**

### **Complete User Journey:**
1. **User visits**: `http://localhost:8082/wall/test-child-163809`
2. **Frontend loads**: Gift wall HTML/CSS/JS from port 8082
3. **User fills form**: Name, email, amount, message
4. **User clicks "Post & Gift"**: JavaScript sends API request
5. **API request goes to**: `http://127.0.0.1:8000/api/wall/test-child-163809/gift`
6. **Backend creates gift**: Saves to database with "pending" status
7. **Backend creates Stripe session**: Returns checkout URL
8. **Frontend redirects**: User goes to Stripe checkout
9. **User completes payment**: Stripe processes transaction
10. **Stripe webhook fires**: Updates gift status to "completed"
11. **Gift becomes visible**: Appears on the gift wall

## 🎯 **Troubleshooting Checklist**

### **✅ Pre-flight Checks**
- [ ] Backend server running on port 8000
- [ ] Frontend server running on port 8082
- [ ] Stripe API key configured in .env file
- [ ] Child profile exists in database
- [ ] Browser can access both servers

### **✅ Gift Creation Checks**
- [ ] Form fields filled correctly
- [ ] JavaScript console shows API request
- [ ] API returns 200 status code
- [ ] Response contains checkout_url
- [ ] Browser redirects to Stripe

### **✅ Payment Checks**
- [ ] Stripe checkout page loads
- [ ] Test card accepted
- [ ] Payment completes successfully
- [ ] Webhook updates gift status
- [ ] Gift appears on wall

## 🎉 **Success Indicators**

### **✅ Everything Working:**
- Gift wall loads at `http://localhost:8082/wall/test-child-163809`
- Form submission shows "Redirecting to payment..."
- Browser redirects to `https://checkout.stripe.com/c/pay/cs_test_...`
- Test payment completes successfully
- Gift appears on the wall after payment

### **🎯 Current Status:**
- ✅ Backend API working (confirmed by automated tests)
- ✅ Frontend server running on port 8082
- ✅ JavaScript fixes applied to remove demo mode
- ✅ Stripe integration functional
- ✅ Two-server architecture properly configured

## 📝 **Quick Commands**

### **Start Both Servers:**
```powershell
.\start-gift-wall.ps1
```

### **Test Gift Wall:**
```
http://localhost:8082/wall/test-child-163809
```

### **Test API Directly:**
```powershell
$body = @{
    child_profile_handle = "test-child-163809"
    from_name = "Test User"
    from_email = "<EMAIL>"
    amount_usd = 25.0
    message = "Test gift"
    is_anonymous = $false
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:8000/api/wall/test-child-163809/gift" -Method POST -Body $body -ContentType "application/json"
```

**Your KidVest gift wall with Stripe checkout is now fully functional!** 🎉

**Access it at: http://localhost:8082/wall/test-child-163809**
