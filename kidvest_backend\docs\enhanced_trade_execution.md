# Enhanced Trade Execution Logic

## Overview

This document describes the enhanced trade execution logic that uses account-specific credentials for each parent's broker account, ensuring proper isolation and security.

## 🔁 Step-by-Step Trade Execution Flow

### 1. Frontend Request
Parent initiates investment via frontend:
```typescript
{
  child_profile_id: "uuid",
  stock_symbol: "TSLA" | "SPY",
  amount_usd: 50.0
}
```

### 2. Backend Authentication
- Extract parent ID from JWT token
- Validate parent owns the child profile

### 3. Database Lookup
```sql
SELECT * FROM broker_accounts 
WHERE user_id = :parent_id 
AND status = 'active' 
AND trading_enabled = TRUE
```

### 4. Account-Specific Trading
- Use stored `api_key_id` and `api_secret_key` for the parent's account
- Fall back to master credentials if account-specific not available
- Place order using Alpaca Broker API with account isolation

### 5. Investment Recording
- Create investment record linked to child, parent, and gift
- Store transaction details and execution timestamp

## 🧩 Enhanced BrokerAccount Model

```python
class BrokerAccount(Base):
    __tablename__ = "broker_accounts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    broker_type = Column(String, nullable=False)  # 'alpaca'
    external_account_id = Column(String, nullable=True)  # Alpaca account ID
    bearer_token = Column(String, nullable=True)  # Account-specific bearer token (encrypted)
    api_key_id = Column(String, nullable=True)  # Account-specific API key
    api_secret_key = Column(String, nullable=True)  # Account-specific secret (encrypted)
    status = Column(String, nullable=False, default="pending")  # pending, active, rejected
    trading_enabled = Column(Boolean, default=False)  # Whether trading is enabled
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)
```

## 🔐 Security Implementation

### Credential Storage
- **Bearer tokens** and **API secrets** are encrypted before database storage
- **API key IDs** can be stored in plain text (they're not sensitive alone)
- Implement proper encryption/decryption functions

### Authentication Hierarchy
1. **Account-specific credentials** (preferred)
   - Use parent's individual API key/secret
   - Provides account isolation
   
2. **Master credentials** (fallback)
   - Use your master Alpaca API credentials
   - For broker API operations

### Example Encryption (TODO: Implement)
```python
def encrypt_secret(secret: str) -> str:
    """Encrypt sensitive data before storing in database"""
    # TODO: Implement using cryptography library
    # Example: Fernet encryption with environment key
    return encrypted_secret

def decrypt_secret(encrypted_secret: str) -> str:
    """Decrypt sensitive data from database"""
    # TODO: Implement corresponding decryption
    return decrypted_secret
```

## 🚀 API Endpoints

### Manual Investment
```
POST /api/invest/
Authorization: Bearer <parent_jwt_token>

{
  "child_profile_id": "uuid",
  "stock_symbol": "TSLA",
  "amount_usd": 50.0
}
```

### Parent Dashboard
```
GET /api/parent/dashboard
Authorization: Bearer <parent_jwt_token>

Response:
{
  "parent_info": {...},
  "summary": {...},
  "children": [
    {
      "profile": {...},
      "available_balance": 75.0
    }
  ]
}
```

## 🔧 Implementation Status

### ✅ Completed
- Enhanced BrokerAccount model with credential fields
- Account-specific trading function (`place_alpaca_order_with_account_credentials`)
- Updated manual investment endpoint
- Database migration script
- Frontend integration with real API calls

### 🚧 TODO
- [ ] Implement proper encryption for sensitive fields
- [ ] Update onboarding to capture and store account credentials
- [ ] Add account credential management endpoints
- [ ] Implement credential rotation functionality
- [ ] Add audit logging for trade executions

## 🧪 Testing

### 1. Run Database Migration
```bash
python migrate_broker_account_credentials.py
```

### 2. Test Integration
```bash
python test_frontend_backend_integration.py
```

### 3. Manual Testing
1. Start backend: `uvicorn app.main:app --reload`
2. Start frontend: `cd kidvest-app-new && npm start`
3. Navigate to manual investment screen
4. Select child, stock, amount and invest
5. Check backend logs for account-specific trading

## 🎯 Expected Behavior

### With Account-Specific Credentials
```
🔐 Using broker account: uuid
   - External ID: alpaca_account_123
   - Trading Enabled: True
   - Has API Key: True
   - Has Bearer Token: False
✅ Using account-specific API credentials
```

### With Fallback Credentials
```
🔐 Using broker account: uuid
   - External ID: alpaca_account_123
   - Trading Enabled: True
   - Has API Key: False
   - Has Bearer Token: False
⚠️ Using master API credentials (fallback)
```

## 🔗 Integration Points

### Alpaca Broker API
- **Account Creation**: Uses master credentials
- **Trade Execution**: Uses account-specific credentials
- **Market Data**: Uses master credentials (shared)

### Database
- **BrokerAccount**: Stores account and credential info
- **Investment**: Records trade execution details
- **ChildProfile**: Links investments to children

### Frontend
- **Authentication**: JWT tokens for parent identification
- **Real-time Updates**: Dashboard refresh after trades
- **Error Handling**: Proper error messages for failed trades

## 📊 Monitoring

### Trade Execution Logs
```
🚀 Starting investment...
✅ Child profile validated: Alice
✅ Sufficient balance: $75.00
✅ Broker account found: alpaca_account_123
🔐 Using account-specific credentials
📡 Placing order: $50 TSLA
✅ Order successful: order_123
✅ Investment created: investment_456
```

### Error Scenarios
- Insufficient balance
- No active broker account
- Trading not enabled
- Alpaca API errors
- Network timeouts
