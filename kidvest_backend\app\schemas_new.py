from pydantic import BaseModel, EmailStr, Field
from uuid import UUID
from enum import Enum
from datetime import datetime
from typing import Optional, List

class UserType(str, Enum):
    parent = "parent"
    child = "child"

# User schemas
class UserBase(BaseModel):
    name: str
    email: EmailStr
    user_type: UserType

class UserCreate(UserBase):
    phone_number: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None

class UserUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone_number: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None
    is_active: Optional[bool] = None

class UserResponse(UserBase):
    id: UUID
    created_at: datetime
    is_active: bool
    phone_number: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None

    class Config:
        from_attributes = True

# Child Profile schemas
class ChildProfileBase(BaseModel):
    name: str
    age: Optional[int] = None
    handle: str
    is_public: bool = True
    bio: Optional[str] = None

class ChildProfileCreate(ChildProfileBase):
    pass

class ChildProfileUpdate(BaseModel):
    name: Optional[str] = None
    age: Optional[int] = None
    handle: Optional[str] = None
    is_public: Optional[bool] = None
    bio: Optional[str] = None
    avatar: Optional[str] = None

class ChildProfileResponse(ChildProfileBase):
    id: UUID
    parent_id: UUID
    avatar: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Gift schemas
class GiftBase(BaseModel):
    from_name: Optional[str] = None
    from_email: Optional[EmailStr] = None
    amount_usd: float = Field(..., gt=0)
    message: Optional[str] = None
    is_anonymous: bool = False

class GiftCreate(GiftBase):
    child_profile_id: UUID

class GiftWallCreate(GiftBase):
    child_profile_handle: str

class GiftUpdate(BaseModel):
    payment_status: Optional[str] = None
    payment_intent_id: Optional[str] = None
    checkout_session_id: Optional[str] = None

class GiftResponse(GiftBase):
    id: UUID
    child_profile_id: UUID
    payment_status: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class GiftWallResponse(BaseModel):
    id: UUID
    from_name: Optional[str] = None
    amount_usd: float
    message: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True

# Investment schemas
class InvestmentBase(BaseModel):
    amount_usd: float = Field(..., gt=0)
    symbol: str
    shares: float = Field(..., gt=0)
    purchase_price: float = Field(..., gt=0)

class InvestmentCreate(InvestmentBase):
    child_profile_id: UUID
    gift_id: Optional[UUID] = None

class InvestmentUpdate(BaseModel):
    status: Optional[str] = None
    transaction_id: Optional[str] = None

class InvestmentResponse(InvestmentBase):
    id: UUID
    child_profile_id: UUID
    gift_id: Optional[UUID] = None
    status: str
    transaction_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Gift Wall response
class GiftWallProfileResponse(BaseModel):
    profile: ChildProfileResponse
    gifts: List[GiftWallResponse]
    total_gifts: int
    total_amount: float

# Checkout session response
class CheckoutSessionResponse(BaseModel):
    success: bool
    gift_id: str
    checkout_url: str
