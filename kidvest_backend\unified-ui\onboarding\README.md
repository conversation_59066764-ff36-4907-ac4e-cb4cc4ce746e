# KidVest Onboarding UI

This is a simple UI for testing the multi-step onboarding process for KidVest.

## Features

- Clean, modern UI with progress tracking
- Step-by-step form with validation
- Real-time API response display
- Complete onboarding flow from start to finish

## Setup

1. Make sure your FastAPI backend is running on `http://localhost:8000`
2. Start the UI server:

```bash
python server.py
```

3. Open your browser and navigate to `http://localhost:8080`

## Testing the Onboarding Process

1. **Step 1: Basic Account Information**
   - Fill in your name and email
   - Click "Next"

2. **Step 2: Identity Verification**
   - Fill in your personal information
   - Use a valid SSN format (e.g., ***********)
   - Click "Next"

3. **Step 3: Financial Profile**
   - Select your financial information
   - Click "Next"

4. **Step 4: Disclosures and Agreements**
   - Answer the disclosure questions
   - Check all agreement boxes
   - Click "Submit"

5. **Completion**
   - View your account details
   - Click "Start Over" to begin again

## API Response

The API response panel at the bottom of the page shows the raw JSON response from the API for each step. This is useful for debugging and understanding the API behavior.

## CORS Configuration

If you encounter CORS issues, make sure your FastAPI backend has CORS enabled. Add the following to your FastAPI app:

```python
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development only
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## Troubleshooting

- If you see "API Error" messages, check that your FastAPI backend is running
- If form submissions fail, check the API response panel for detailed error messages
- Make sure you're using valid data formats (especially for SSN and date fields)
