import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { Picker } from '@react-native-picker/picker';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { relationshipsAPI } from '@/services/api';
import { useRouter } from 'expo-router';

const relationshipTypes = [
  { value: 'parent', label: 'Parent' },
  { value: 'grandparent', label: 'Grandparent' },
  { value: 'aunt_uncle', label: 'Aunt/Uncle' },
  { value: 'sibling', label: 'Sibling' },
  { value: 'cousin', label: 'Cousin' },
  { value: 'family_friend', label: 'Family Friend' },
  { value: 'godparent', label: 'Godparent' },
  { value: 'other', label: 'Other' },
];

interface SearchResult {
  child_found: boolean;
  child_name?: string;
  child_handle?: string;
  relationship_exists?: boolean;
  relationship_status?: string;
  relationship_id?: string;
  can_request?: boolean;
}

export default function RequestRelationshipScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const [childHandle, setChildHandle] = useState('');
  const [relationshipType, setRelationshipType] = useState('family_friend');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchResult, setSearchResult] = useState<SearchResult | null>(null);
  const [step, setStep] = useState<'search' | 'confirm' | 'success'>('search');

  const handleSearch = async () => {
    if (!childHandle.trim()) {
      Alert.alert('Error', 'Please enter a child handle');
      return;
    }

    setLoading(true);
    try {
      const result = await relationshipsAPI.searchChild({
        child_handle: childHandle.trim(),
        relationship_type: relationshipType as any,
        description: description.trim() || undefined,
      });
      
      setSearchResult(result);
      setStep('confirm');
    } catch (error: any) {
      console.error('Error searching for child:', error);
      if (error.response?.status === 404) {
        Alert.alert('Not Found', 'No child found with that handle');
      } else {
        Alert.alert('Error', 'Failed to search for child');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleRequestRelationship = async () => {
    if (!searchResult?.can_request) {
      Alert.alert('Error', 'Cannot request relationship with this child');
      return;
    }

    setLoading(true);
    try {
      await relationshipsAPI.createRelationshipRequest({
        to_child_handle: childHandle.trim(),
        relationship_type: relationshipType as any,
        description: description.trim() || undefined,
      });
      
      setStep('success');
    } catch (error: any) {
      console.error('Error creating relationship request:', error);
      Alert.alert('Error', 'Failed to send relationship request');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setChildHandle('');
    setRelationshipType('family_friend');
    setDescription('');
    setSearchResult(null);
    setStep('search');
  };

  const renderSearchStep = () => (
    <ScrollView style={styles.scrollView}>
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: Colors[colorScheme].text }]}>
          Find Child
        </Text>
        <Text style={[styles.sectionDescription, { color: Colors[colorScheme].tabIconDefault }]}>
          Enter the child's handle to request a relationship
        </Text>

        <View style={styles.inputContainer}>
          <Text style={[styles.inputLabel, { color: Colors[colorScheme].text }]}>
            Child Handle *
          </Text>
          <TextInput
            style={[styles.input, { 
              backgroundColor: Colors[colorScheme].background,
              borderColor: Colors[colorScheme].tabIconDefault,
              color: Colors[colorScheme].text 
            }]}
            placeholder="e.g., emma-johnson-123"
            placeholderTextColor={Colors[colorScheme].tabIconDefault}
            value={childHandle}
            onChangeText={setChildHandle}
            autoCapitalize="none"
            autoCorrect={false}
          />
          <Text style={[styles.helperText, { color: Colors[colorScheme].tabIconDefault }]}>
            You can find this on the child's gift wall page
          </Text>
        </View>

        <View style={styles.inputContainer}>
          <Text style={[styles.inputLabel, { color: Colors[colorScheme].text }]}>
            Relationship Type *
          </Text>
          <View style={[styles.pickerContainer, { 
            backgroundColor: Colors[colorScheme].background,
            borderColor: Colors[colorScheme].tabIconDefault 
          }]}>
            <Picker
              selectedValue={relationshipType}
              onValueChange={setRelationshipType}
              style={[styles.picker, { color: Colors[colorScheme].text }]}
            >
              {relationshipTypes.map((type) => (
                <Picker.Item key={type.value} label={type.label} value={type.value} />
              ))}
            </Picker>
          </View>
        </View>

        <View style={styles.inputContainer}>
          <Text style={[styles.inputLabel, { color: Colors[colorScheme].text }]}>
            Description (Optional)
          </Text>
          <TextInput
            style={[styles.input, styles.textArea, { 
              backgroundColor: Colors[colorScheme].background,
              borderColor: Colors[colorScheme].tabIconDefault,
              color: Colors[colorScheme].text 
            }]}
            placeholder="Add a personal message..."
            placeholderTextColor={Colors[colorScheme].tabIconDefault}
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={3}
          />
        </View>

        <TouchableOpacity
          style={[styles.searchButton, { backgroundColor: Colors[colorScheme].primary }]}
          onPress={handleSearch}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#FFFFFF" />
          ) : (
            <>
              <FontAwesome5 name="search" size={16} color="#FFFFFF" />
              <Text style={styles.searchButtonText}>Search Child</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  const renderConfirmStep = () => (
    <ScrollView style={styles.scrollView}>
      <View style={styles.section}>
        <View style={styles.resultCard}>
          <FontAwesome5 
            name="child" 
            size={48} 
            color={Colors[colorScheme].primary} 
            style={styles.resultIcon}
          />
          <Text style={[styles.resultTitle, { color: Colors[colorScheme].text }]}>
            Child Found!
          </Text>
          <Text style={[styles.resultName, { color: Colors[colorScheme].text }]}>
            {searchResult?.child_name}
          </Text>
          <Text style={[styles.resultHandle, { color: Colors[colorScheme].tabIconDefault }]}>
            @{searchResult?.child_handle}
          </Text>
        </View>

        {searchResult?.relationship_exists ? (
          <View style={styles.existingRelationship}>
            <FontAwesome5 name="info-circle" size={20} color="#FF9800" />
            <Text style={[styles.existingRelationshipText, { color: Colors[colorScheme].text }]}>
              You already have a {searchResult.relationship_status} relationship with this child
            </Text>
          </View>
        ) : (
          <>
            <View style={styles.requestDetails}>
              <Text style={[styles.requestTitle, { color: Colors[colorScheme].text }]}>
                Request Details
              </Text>
              <View style={styles.requestItem}>
                <Text style={[styles.requestLabel, { color: Colors[colorScheme].tabIconDefault }]}>
                  Relationship Type:
                </Text>
                <Text style={[styles.requestValue, { color: Colors[colorScheme].text }]}>
                  {relationshipTypes.find(t => t.value === relationshipType)?.label}
                </Text>
              </View>
              {description && (
                <View style={styles.requestItem}>
                  <Text style={[styles.requestLabel, { color: Colors[colorScheme].tabIconDefault }]}>
                    Description:
                  </Text>
                  <Text style={[styles.requestValue, { color: Colors[colorScheme].text }]}>
                    {description}
                  </Text>
                </View>
              )}
            </View>

            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[styles.actionButton, styles.backButton]}
                onPress={() => setStep('search')}
              >
                <Text style={[styles.actionButtonText, { color: Colors[colorScheme].text }]}>
                  Back
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionButton, styles.confirmButton, { backgroundColor: Colors[colorScheme].primary }]}
                onPress={handleRequestRelationship}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color="#FFFFFF" />
                ) : (
                  <Text style={styles.confirmButtonText}>Send Request</Text>
                )}
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>
    </ScrollView>
  );

  const renderSuccessStep = () => (
    <View style={[styles.successContainer, { backgroundColor: Colors[colorScheme].background }]}>
      <FontAwesome5 name="check-circle" size={64} color="#4CAF50" />
      <Text style={[styles.successTitle, { color: Colors[colorScheme].text }]}>
        Request Sent!
      </Text>
      <Text style={[styles.successMessage, { color: Colors[colorScheme].tabIconDefault }]}>
        Your relationship request has been sent to {searchResult?.child_name}'s parent. 
        You'll be notified when they respond.
      </Text>
      
      <View style={styles.successActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.backButton]}
          onPress={resetForm}
        >
          <Text style={[styles.actionButtonText, { color: Colors[colorScheme].text }]}>
            Send Another Request
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.confirmButton, { backgroundColor: Colors[colorScheme].primary }]}
          onPress={() => router.back()}
        >
          <Text style={styles.confirmButtonText}>Done</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: Colors[colorScheme].background }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <FontAwesome5 name="arrow-left" size={20} color={Colors[colorScheme].text} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: Colors[colorScheme].text }]}>
          Request Relationship
        </Text>
      </View>

      {step === 'search' && renderSearchStep()}
      {step === 'confirm' && renderConfirmStep()}
      {step === 'success' && renderSuccessStep()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 10,
  },
  backButton: {
    marginRight: 16,
    padding: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    marginBottom: 24,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  helperText: {
    fontSize: 12,
    marginTop: 4,
    fontStyle: 'italic',
  },
  pickerContainer: {
    borderWidth: 1,
    borderRadius: 8,
  },
  picker: {
    height: 50,
  },
  searchButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    gap: 8,
    marginTop: 20,
  },
  searchButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  resultCard: {
    alignItems: 'center',
    padding: 24,
    marginBottom: 24,
  },
  resultIcon: {
    marginBottom: 16,
  },
  resultTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },
  resultName: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 4,
  },
  resultHandle: {
    fontSize: 14,
  },
  existingRelationship: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
    gap: 12,
  },
  existingRelationshipText: {
    flex: 1,
    fontSize: 14,
  },
  requestDetails: {
    marginBottom: 24,
  },
  requestTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  requestItem: {
    marginBottom: 12,
  },
  requestLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  requestValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  backButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  confirmButton: {
    // backgroundColor set dynamically
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  successContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 24,
    marginBottom: 16,
  },
  successMessage: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  successActions: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
  },
});
