import requests
import json

# Test data with a valid SSN format (not starting with 000 or 666)
test_data = {
    "full_name": "<PERSON>",
    "dob": "1990-04-15",
    "phone_number": "************",
    "street_address": "123 Main St",
    "city": "New York",
    "state": "NY",
    "postal_code": "10001",
    "country": "USA",
    # "ssn": "***********",  # Valid SSN format but sequential
    # Try with a more realistic SSN that doesn't follow a pattern
    "ssn": "***********",
    "employment_status": "EMPLOYED",
    "funding_source": "employment_income"
}

# Send the request to your local API
url = "http://localhost:8000/onboarding/alpaca/"
headers = {"Content-Type": "application/json"}

print("Sending onboarding request...")
response = requests.post(url, json=test_data, headers=headers)

print(f"Status Code: {response.status_code}")
try:
    print(f"Response: {json.dumps(response.json(), indent=2)}")
except:
    print(f"Response: {response.text}")
