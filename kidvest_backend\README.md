# KidVest Two-Server Architecture

This is a two-server architecture for the KidVest application, which provides a seamless user experience for the entire application flow:

1. **Backend Server**: Handles all API requests and database operations (port 8000)
2. **Unified UI Server**: Serves all UI components from a single server (port 8080)

## Getting Started

1. Install the required dependencies:
   ```
   pip install flask python-jose passlib bcrypt python-multipart
   ```

2. Start both servers with a single command:
   ```
   python start_servers.py
   ```

   This will start both the backend server and the unified UI server, and open the unified UI in your browser.

3. Alternatively, you can start the servers separately:
   ```
   # Start the backend server
   python -m uvicorn app.main:app --reload --log-level debug

   # Start the unified UI server
   python simple_unified_server.py
   ```

4. Open your browser and navigate to:
   ```
   http://localhost:8080
   ```

## User Journey

The two-server architecture provides a seamless user journey through the following steps:

1. **Authentication**: Login or register to access the KidVest platform
   - URL: http://localhost:8080/auth/

2. **Dashboard**: View your account dashboard and access all features
   - URL: http://localhost:8080/dashboard/

3. **Onboarding**: Complete the KYC onboarding process
   - URL: http://localhost:8080/onboarding/

4. **Child Profiles**: Manage your child profiles
   - URL: http://localhost:8080/child-profile/

5. **Gift Walls**: View and share gift walls
   - URL: http://localhost:8080/gift-wall/

## How It Works

The two-server architecture consists of:

1. **Backend Server (port 8000)**:
   - Handles all API requests
   - Manages database operations
   - Provides authentication and authorization
   - Implements business logic

2. **Unified UI Server (port 8080)**:
   - Uses Flask to serve all UI components from their respective directories
   - Provides a consistent URL structure
   - Handles all the routing between different parts of the application
   - Makes all UI components appear as if they are part of a single application

This architecture allows for a seamless user experience while maintaining a clean separation between the frontend and backend concerns.

## Troubleshooting

If you encounter any issues with the unified server, try the following:

1. Make sure the backend server is running on port 8000
2. Make sure the unified server is running on port 8080
3. Check the browser console for any JavaScript errors
4. Check the server logs for any errors

If you still have issues, try running each UI component separately using their individual server.py files.
