# KidVest - Child Investment Platform

A comprehensive platform enabling parents to create investment accounts for their children and manage monetary gifts through a social wall interface with complete portfolio tracking.

## 🎯 **Current Status: Phase 1 Complete**

✅ **Child-Specific Gift & Investment Flow Fully Implemented**
- Complete backend API with portfolio analytics
- Full React Native frontend with portfolio management
- Child-specific gift and investment tracking
- Family portfolio overview and individual child portfolios

## 🚀 **Quick Start**

### **Start Everything (Recommended)**
```powershell
.\start-all.ps1
```

### **Start Backend Only**
```powershell
.\start-backend.ps1
```
- FastAPI server: http://localhost:8000
- API docs: http://localhost:8000/docs

### **Start Frontend Only**
```powershell
.\start-frontend.ps1
```
- React Native/Expo app with portfolio management

## 📱 **Features**

### **Core Features ✅**
- **User Management**: Parent registration and authentication
- **Child Profiles**: Create multiple children with unique gift wall handles
- **Gift Wall**: Public interface for receiving monetary gifts
- **Stripe Integration**: Secure payment processing
- **Investment Management**: Create investments from specific gifts
- **Portfolio Tracking**: Individual and family portfolio analytics

### **Phase 1 Features ✅**
- **Child-Specific Portfolios**: Each child has their own portfolio view
- **Gift-to-Investment Flow**: Direct investment creation from gifts
- **Portfolio Analytics**: Real-time portfolio summaries and progress tracking
- **Investment History**: Track all investments per child
- **Balance Management**: Available balance tracking per child

## 🏗️ **Architecture**

### **Backend (FastAPI)**
```
app/
├── main.py              ✅ Main FastAPI application
├── models.py            ✅ SQLAlchemy database models
├── schemas.py           ✅ Pydantic schemas with portfolio support
├── crud.py              ✅ Database operations with portfolio analytics
├── auth.py              ✅ JWT authentication
├── database.py          ✅ PostgreSQL connection
├── alpaca_service.py    ✅ Brokerage integration
└── onboarding_service.py ✅ Multi-step KYC
```

### **Frontend (React Native/Expo)**
```
kidvest-app-new/
├── app/
│   ├── (tabs)/          ✅ Main navigation (Dashboard, Profiles, Gifts)
│   ├── portfolio/       ✅ Portfolio management screens
│   ├── investments/     ✅ Investment creation screens
│   ├── auth/            ✅ Authentication screens
│   ├── onboarding/      ✅ Child profile creation
│   └── gifts/           ✅ Gift creation and success
├── services/api.ts      ✅ API integration with portfolio endpoints
└── context/             ✅ Authentication and state management
```

## 🗄️ **Database Schema**

### **Core Models**
```sql
User (Parent)
├── ChildProfile (1:many)
│   ├── Gift (1:many)
│   │   └── Investment (1:many) [via gift_id]
│   └── Investment (1:many) [direct]
└── BrokerAccount (1:1)
```

## 🔗 **API Endpoints**

### **Authentication**
- `POST /api/register` - User registration
- `POST /api/token` - Login
- `GET /api/users/me` - Current user

### **Child Management**
- `POST /api/child-profiles` - Create child profile
- `GET /api/child-profiles` - Get all child profiles

### **Gift Wall**
- `GET /api/wall/{handle}` - Public gift wall
- `POST /api/wall/{handle}/gift` - Create gift

### **Portfolio Management (NEW)**
- `GET /api/child-profiles/{id}/portfolio` - Complete child portfolio
- `POST /api/gifts/{id}/invest` - Create investment from gift
- `GET /api/dashboard/portfolio-overview` - Family portfolio overview

## ⚙️ **Environment Setup**

### **Required Environment Variables**
```env
DATABASE_URL=postgresql://username:password@localhost/kidvest
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
ALPACA_API_KEY=your_alpaca_key
ALPACA_SECRET_KEY=your_alpaca_secret
ALPACA_BASE_URL=https://paper-api.alpaca.markets
```

## 📚 **Documentation**

- **[Current File Structure](docs/current_file_structure.md)** - Active files and organization
- **[Phase 1 Enhancements](docs/phase1_child_specific_enhancements.md)** - Portfolio features
- **[Frontend Integration](docs/frontend_integration_complete.md)** - UI implementation

## 🎉 **Production Ready**

The KidVest platform is now production-ready with:
- ✅ Complete child-specific gift and investment flow
- ✅ Full portfolio management and analytics
- ✅ Secure payment processing with Stripe
- ✅ Mobile-ready React Native frontend
- ✅ Clean, organized codebase

**Ready for deployment and user testing!** 🎯

This architecture allows for a seamless user experience while maintaining a clean separation between the frontend and backend concerns.

## Troubleshooting

If you encounter any issues with the unified server, try the following:

1. Make sure the backend server is running on port 8000
2. Make sure the unified server is running on port 8080
3. Check the browser console for any JavaScript errors
4. Check the server logs for any errors

If you still have issues, try running each UI component separately using their individual server.py files.
