from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, Request, status
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
import os
import stripe
import json
from datetime import datetime, timezone

from . import models_new as models
from . import schemas_new as schemas
from . import crud_new as crud
from .database import SessionLocal, engine

# Create tables
models.Base.metadata.create_all(bind=engine)

# Initialize Stripe
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

app = FastAPI(title="KidVest API")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development, replace with specific origins in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Dependency to get the database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Dependency to get the current user (placeholder for authentication)
def get_current_user(db: Session = Depends(get_db)):
    # For development, return the first parent user
    user = db.query(models.User).filter(models.User.user_type == "parent").first()
    if not user:
        raise HTTPException(status_code=404, detail="No parent user found")
    return user

# Root endpoint
@app.get("/")
def read_root():
    return {"message": "Welcome to KidVest API"}

# User endpoints
@app.post("/api/users/", response_model=schemas.UserResponse)
def create_user(user: schemas.UserCreate, db: Session = Depends(get_db)):
    """Create a new user"""
    db_user = crud.get_user_by_email(db, user.email)
    if db_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    return crud.create_user(db, user)

@app.get("/api/users/me", response_model=schemas.UserResponse)
def read_user_me(current_user: models.User = Depends(get_current_user)):
    """Get current user"""
    return current_user

@app.put("/api/users/me", response_model=schemas.UserResponse)
def update_user_me(user: schemas.UserUpdate, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    """Update current user"""
    return crud.update_user(db, current_user.id, user)

# Child Profile endpoints
@app.post("/api/children/", response_model=schemas.ChildProfileResponse)
def create_child_profile(profile: schemas.ChildProfileCreate, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    """Create a new child profile"""
    # Check if handle already exists
    existing_profile = crud.get_child_profile_by_handle(db, profile.handle)
    if existing_profile:
        raise HTTPException(status_code=400, detail="Handle already in use")
    
    return crud.create_child_profile(db, profile, current_user.id)

@app.get("/api/children/", response_model=List[schemas.ChildProfileResponse])
def read_child_profiles(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    """Get all child profiles for the current user"""
    return crud.get_child_profiles_by_parent(db, current_user.id, skip, limit)

@app.get("/api/children/{profile_id}", response_model=schemas.ChildProfileResponse)
def read_child_profile(profile_id: UUID, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    """Get a specific child profile"""
    db_profile = crud.get_child_profile(db, profile_id)
    if not db_profile:
        raise HTTPException(status_code=404, detail="Profile not found")
    
    # Check if the profile belongs to the current user
    if db_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this profile")
    
    return db_profile

@app.put("/api/children/{profile_id}", response_model=schemas.ChildProfileResponse)
def update_child_profile(profile_id: UUID, profile: schemas.ChildProfileUpdate, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    """Update a child profile"""
    db_profile = crud.get_child_profile(db, profile_id)
    if not db_profile:
        raise HTTPException(status_code=404, detail="Profile not found")
    
    # Check if the profile belongs to the current user
    if db_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to update this profile")
    
    # If handle is being updated, check if it's already in use
    if profile.handle and profile.handle != db_profile.handle:
        existing_profile = crud.get_child_profile_by_handle(db, profile.handle)
        if existing_profile:
            raise HTTPException(status_code=400, detail="Handle already in use")
    
    return crud.update_child_profile(db, profile_id, profile)

@app.delete("/api/children/{profile_id}", response_model=dict)
def delete_child_profile(profile_id: UUID, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    """Delete a child profile"""
    db_profile = crud.get_child_profile(db, profile_id)
    if not db_profile:
        raise HTTPException(status_code=404, detail="Profile not found")
    
    # Check if the profile belongs to the current user
    if db_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this profile")
    
    success = crud.delete_child_profile(db, profile_id)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete profile")
    
    return {"success": True, "message": "Profile deleted successfully"}

# Gift Wall endpoints
@app.get("/api/wall/{handle}", response_model=schemas.GiftWallProfileResponse)
def read_gift_wall(handle: str, db: Session = Depends(get_db)):
    """Get a gift wall by handle"""
    # Get the child profile
    child_profile = crud.get_child_profile_by_handle(db, handle)
    if not child_profile:
        raise HTTPException(status_code=404, detail="Profile not found")
    
    # Only return public profiles
    if not child_profile.is_public:
        raise HTTPException(status_code=404, detail="Profile not found")
    
    # Get completed gifts for the profile
    gifts = crud.get_gifts_by_child_profile(db, child_profile.id)
    
    # Format gifts for the gift wall
    gift_wall_items = []
    for gift in gifts:
        # If gift is anonymous, don't include from_name
        from_name = None if gift.is_anonymous else gift.from_name
        
        gift_wall_items.append({
            "id": gift.id,
            "from_name": from_name,
            "amount_usd": gift.amount_usd,
            "message": gift.message,
            "created_at": gift.created_at
        })
    
    # Return the gift wall data
    return {
        "profile": child_profile,
        "gifts": gift_wall_items,
        "total_gifts": len(gift_wall_items),
        "total_amount": sum(gift.amount_usd for gift in gifts)
    }

@app.post("/api/wall/{handle}/gift", response_model=schemas.CheckoutSessionResponse)
def create_wall_gift(handle: str, gift: schemas.GiftWallCreate, db: Session = Depends(get_db)):
    """Create a new gift from the gift wall"""
    try:
        print(f"Creating gift for handle: {handle}")
        
        # Print gift data safely
        try:
            gift_data = gift.model_dump()
            print(f"Gift data: {gift_data}")
        except Exception as e:
            print(f"Error printing gift data: {str(e)}")
        
        # Override the handle from the URL
        gift.child_profile_handle = handle
        
        # Create the gift
        db_gift = crud.create_gift_from_wall(db, gift)
        if not db_gift:
            raise HTTPException(status_code=404, detail="Profile not found")
        
        print(f"Gift created with ID: {db_gift.id}")
        
        # Get the child profile for the gift
        child_profile = crud.get_child_profile(db, db_gift.child_profile_id)
        
        # Create a Stripe checkout session
        stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
        print(f"Using Stripe API key: {stripe.api_key[:5] if stripe.api_key else 'None'}...")
        
        try:
            checkout_session = stripe.checkout.Session.create(
                payment_method_types=["card"],
                line_items=[{
                    "price_data": {
                        "currency": "usd",
                        "unit_amount": int(gift.amount_usd * 100),
                        "product_data": {
                            "name": f"Gift to {child_profile.name}",
                            "description": gift.message or "No message"
                        },
                    },
                    "quantity": 1,
                }],
                mode="payment",
                success_url=f"{os.getenv('FRONTEND_URL', 'http://localhost:8082')}/wall/{handle}/success?gift_id={db_gift.id}",
                cancel_url=f"{os.getenv('FRONTEND_URL', 'http://localhost:8082')}/wall/{handle}/cancel",
                metadata={"gift_id": str(db_gift.id)},
            )
            
            print(f"Stripe checkout session created: {checkout_session.id}")
            
            # Update the gift with the checkout session ID
            crud.update_gift_payment_status(
                db,
                db_gift.id,
                "pending",
                checkout_session_id=checkout_session.id
            )
            
            return {
                "success": True,
                "gift_id": str(db_gift.id),
                "checkout_url": checkout_session.url
            }
        except Exception as stripe_error:
            print(f"Stripe error: {str(stripe_error)}")
            # If Stripe fails, still return a success response for testing
            return {
                "success": True,
                "gift_id": str(db_gift.id),
                "checkout_url": f"/wall/{handle}/success?gift_id={db_gift.id}&test=true"
            }
    
    except Exception as e:
        # Delete the gift if checkout creation fails
        if 'db_gift' in locals():
            db.delete(db_gift)
            db.commit()
        raise HTTPException(status_code=500, detail=str(e))

# Gift endpoints for parents
@app.get("/api/gifts/", response_model=List[schemas.GiftResponse])
def read_gifts(child_profile_id: Optional[UUID] = None, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    """Get gifts for a child profile or all child profiles of the current user"""
    if child_profile_id:
        # Check if the child profile belongs to the current user
        child_profile = crud.get_child_profile(db, child_profile_id)
        if not child_profile or child_profile.parent_id != current_user.id:
            raise HTTPException(status_code=403, detail="Not authorized to access this profile")
        
        return crud.get_gifts_by_child_profile(db, child_profile_id, skip, limit)
    else:
        # Get all child profiles for the current user
        child_profiles = crud.get_child_profiles_by_parent(db, current_user.id)
        
        # Get gifts for all child profiles
        gifts = []
        for profile in child_profiles:
            profile_gifts = crud.get_gifts_by_child_profile(db, profile.id, 0, limit)
            gifts.extend(profile_gifts)
        
        # Sort by created_at and apply skip/limit
        gifts.sort(key=lambda x: x.created_at, reverse=True)
        return gifts[skip:skip+limit]

@app.get("/api/gifts/{gift_id}", response_model=schemas.GiftResponse)
def read_gift(gift_id: UUID, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    """Get a specific gift"""
    db_gift = crud.get_gift(db, gift_id)
    if not db_gift:
        raise HTTPException(status_code=404, detail="Gift not found")
    
    # Check if the gift belongs to a child profile of the current user
    child_profile = crud.get_child_profile(db, db_gift.child_profile_id)
    if not child_profile or child_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this gift")
    
    return db_gift

# Investment endpoints
@app.post("/api/investments/", response_model=schemas.InvestmentResponse)
def create_investment(investment: schemas.InvestmentCreate, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    """Create a new investment"""
    # Check if the child profile belongs to the current user
    child_profile = crud.get_child_profile(db, investment.child_profile_id)
    if not child_profile or child_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to create investments for this profile")
    
    # If gift_id is provided, check if it belongs to the child profile
    if investment.gift_id:
        gift = crud.get_gift(db, investment.gift_id)
        if not gift or gift.child_profile_id != investment.child_profile_id:
            raise HTTPException(status_code=400, detail="Gift does not belong to this child profile")
    
    return crud.create_investment(db, investment)

@app.get("/api/investments/", response_model=List[schemas.InvestmentResponse])
def read_investments(child_profile_id: Optional[UUID] = None, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    """Get investments for a child profile or all child profiles of the current user"""
    if child_profile_id:
        # Check if the child profile belongs to the current user
        child_profile = crud.get_child_profile(db, child_profile_id)
        if not child_profile or child_profile.parent_id != current_user.id:
            raise HTTPException(status_code=403, detail="Not authorized to access this profile")
        
        return crud.get_investments_by_child_profile(db, child_profile_id, skip, limit)
    else:
        # Get all child profiles for the current user
        child_profiles = crud.get_child_profiles_by_parent(db, current_user.id)
        
        # Get investments for all child profiles
        investments = []
        for profile in child_profiles:
            profile_investments = crud.get_investments_by_child_profile(db, profile.id, 0, limit)
            investments.extend(profile_investments)
        
        # Sort by created_at and apply skip/limit
        investments.sort(key=lambda x: x.created_at, reverse=True)
        return investments[skip:skip+limit]

@app.get("/api/investments/{investment_id}", response_model=schemas.InvestmentResponse)
def read_investment(investment_id: UUID, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    """Get a specific investment"""
    db_investment = crud.get_investment(db, investment_id)
    if not db_investment:
        raise HTTPException(status_code=404, detail="Investment not found")
    
    # Check if the investment belongs to a child profile of the current user
    child_profile = crud.get_child_profile(db, db_investment.child_profile_id)
    if not child_profile or child_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this investment")
    
    return db_investment

@app.put("/api/investments/{investment_id}", response_model=schemas.InvestmentResponse)
def update_investment(investment_id: UUID, investment: schemas.InvestmentUpdate, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    """Update an investment"""
    db_investment = crud.get_investment(db, investment_id)
    if not db_investment:
        raise HTTPException(status_code=404, detail="Investment not found")
    
    # Check if the investment belongs to a child profile of the current user
    child_profile = crud.get_child_profile(db, db_investment.child_profile_id)
    if not child_profile or child_profile.parent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to update this investment")
    
    # Update the investment
    if investment.status:
        db_investment = crud.update_investment_status(db, investment_id, investment.status, investment.transaction_id)
    
    return db_investment

# Webhook handler for Stripe events
@app.post("/webhook/")
async def stripe_webhook(request: Request, db: Session = Depends(get_db)):
    """Handle Stripe webhook events"""
    payload = await request.body()
    sig_header = request.headers.get("stripe-signature")
    endpoint_secret = os.getenv("STRIPE_WEBHOOK_SECRET")
    
    try:
        event = stripe.Webhook.construct_event(payload, sig_header, endpoint_secret)
    except stripe.error.SignatureVerificationError:
        raise HTTPException(status_code=400, detail="Invalid signature")
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
    
    print(f"Received Stripe webhook event: {event['type']}")
    
    if event["type"] == "checkout.session.completed":
        print("🎯 Checkout session completed!")
        session = event["data"]["object"]
        
        # Get the gift ID from the metadata
        gift_id = session.get("metadata", {}).get("gift_id")
        if not gift_id:
            print("No gift ID found in metadata")
            return {"status": "error", "message": "No gift ID found in metadata"}
        
        # Get the payment intent ID
        payment_intent_id = session.get("payment_intent")
        
        # Update the gift status
        gift = crud.get_gift(db, UUID(gift_id))
        if gift:
            print(f"Updating gift {gift_id} to completed")
            crud.update_gift_payment_status(
                db,
                UUID(gift_id),
                "completed",
                payment_intent_id=payment_intent_id
            )
        else:
            print(f"Gift {gift_id} not found")
    
    elif event["type"] == "checkout.session.expired":
        print("🕒 Checkout session expired!")
        session = event["data"]["object"]
        
        # Get the gift ID from the metadata
        gift_id = session.get("metadata", {}).get("gift_id")
        if not gift_id:
            return {"status": "error", "message": "No gift ID found in metadata"}
        
        # Update the gift status
        gift = crud.get_gift(db, UUID(gift_id))
        if gift:
            crud.update_gift_payment_status(db, UUID(gift_id), "failed")
    
    return {"status": "success"}
