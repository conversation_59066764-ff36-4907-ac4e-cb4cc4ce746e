import requests
import json
import uuid
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def simulate_webhook_event():
    """Simulate a webhook event from Stripe with detailed logging"""
    print("=== Simulating Webhook Event (Detailed) ===")
    
    # Get the gift ID from the user
    gift_id = input("Enter the gift ID to update (or press Enter to use a test ID): ")
    if not gift_id:
        gift_id = str(uuid.uuid4())
        print(f"Using test gift ID: {gift_id}")
    
    # Create a simulated checkout.session.completed event
    event_data = {
        "type": "checkout.session.completed",
        "data": {
            "object": {
                "id": f"cs_{uuid.uuid4().hex[:24]}",
                "object": "checkout.session",
                "payment_status": "paid",
                "payment_intent": f"pi_{uuid.uuid4().hex[:24]}",
                "metadata": {
                    "gift_id": gift_id
                }
            }
        }
    }
    
    # Print the event data
    print(f"Event data: {json.dumps(event_data, indent=2)}")
    
    # Get the webhook URL
    webhook_url = "http://localhost:8000/webhook/"
    print(f"Sending to webhook URL: {webhook_url}")
    
    # Send the webhook event
    try:
        print("\nSending request...")
        response = requests.post(
            webhook_url,
            json=event_data,
            headers={
                "Content-Type": "application/json",
                "Stripe-Signature": "test_signature"  # This will be ignored in test mode
            }
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"Response data: {json.dumps(response_data, indent=2)}")
        except:
            print(f"Response text: {response.text}")
        
        # Check if the gift was updated
        print("\nChecking if gift was updated...")
        from app.database import SessionLocal
        from app import models
        from uuid import UUID
        
        db = SessionLocal()
        try:
            gift = db.query(models.Gift).filter(models.Gift.id == UUID(gift_id)).first()
            if gift:
                print(f"Gift found: {gift.id}")
                print(f"Payment status: {gift.payment_status}")
                print(f"Payment intent ID: {gift.payment_intent_id}")
                
                if gift.payment_status == "completed":
                    print("\n✅ SUCCESS: Gift payment status updated to completed!")
                else:
                    print("\n❌ ERROR: Gift payment status not updated!")
            else:
                print(f"Gift not found with ID: {gift_id}")
        except Exception as e:
            print(f"Error checking gift: {str(e)}")
        finally:
            db.close()
        
        print("\n=== Webhook Simulation Complete ===")
    except Exception as e:
        print(f"Error sending webhook event: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simulate_webhook_event()
