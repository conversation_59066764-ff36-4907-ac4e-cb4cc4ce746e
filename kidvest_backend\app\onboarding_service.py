# app/onboarding_service.py
import json
import uuid
from datetime import datetime, timedelta, timezone
from sqlalchemy.orm import Session
from . import models, schemas
from .alpaca_service import onboard_alpaca_user

# Constants for mapping simplified inputs to Alpaca API format
INCOME_RANGES = {
    "under_50k": {"min": "0", "max": "50000"},
    "50k_100k": {"min": "50000", "max": "100000"},
    "100k_250k": {"min": "100000", "max": "250000"},
    "over_250k": {"min": "250000", "max": "1000000"}
}

NET_WORTH_RANGES = {
    "under_50k": {"min": "0", "max": "50000"},
    "50k_100k": {"min": "50000", "max": "100000"},
    "100k_250k": {"min": "100000", "max": "250000"},
    "over_250k": {"min": "250000", "max": "1000000"}
}

EXPERIENCE_LEVELS = {
    "none": "limited_experience",
    "some": "good_experience",
    "experienced": "extensive_experience"
}

RISK_TOLERANCE = {
    "conservative": "conservative",
    "moderate": "moderate",
    "aggressive": "aggressive"
}

def generate_session_token():
    """Generate a unique session token for onboarding"""
    return str(uuid.uuid4())

def create_onboarding_session(db: Session):
    """Create a new onboarding session"""
    print("\n==== CREATING NEW SESSION ====")

    session_token = generate_session_token()
    print(f"Generated token: {session_token}")

    # Set expiration to 24 hours from now
    expires_at = datetime.now(timezone.utc) + timedelta(hours=24)

    session = models.OnboardingSession(
        session_token=session_token,
        current_step=1,
        expires_at=expires_at
    )

    db.add(session)
    db.commit()
    db.refresh(session)

    print(f"Created session: {session.id}, token: {session.session_token}")

    return session

def get_session_by_token(db: Session, token: str):
    """Get an onboarding session by token"""
    print(f"\n==== LOOKING UP SESSION ====\nToken: {token}")

    if not token:
        print("Error: Token is None or empty")
        return None

    # Query the database
    session = db.query(models.OnboardingSession).filter(
        models.OnboardingSession.session_token == token
    ).first()

    if session:
        print(f"Found session: {session.id}, current_step: {session.current_step}")
    else:
        print(f"No session found for token: {token}")

        # Debug: List all sessions
        all_sessions = db.query(models.OnboardingSession).all()
        print(f"Total sessions in database: {len(all_sessions)}")
        for s in all_sessions:
            print(f"  - Session {s.id}: token={s.session_token}, step={s.current_step}")

    return session

def update_session_step(db: Session, session: models.OnboardingSession, step_number: int, step_data: dict):
    """Update a session with step data and mark as completed"""
    # Convert date objects to strings for JSON serialization
    serializable_data = {}
    for key, value in step_data.items():
        # Check if value is a date object
        if hasattr(value, 'isoformat') and callable(getattr(value, 'isoformat')):
            serializable_data[key] = value.isoformat()
        else:
            serializable_data[key] = value

    # Store step data
    setattr(session, f"step{step_number}_data", json.dumps(serializable_data))

    # Update completion timestamp
    setattr(session, f"step{step_number}_completed_at", datetime.now(timezone.utc))

    # Update current step
    session.current_step = step_number + 1

    db.commit()
    db.refresh(session)

    return session

def process_step1(db: Session, data: schemas.OnboardingStep1, session_token: str = None):
    """Process step 1: Basic account creation"""
    # Create or get session
    if session_token:
        session = get_session_by_token(db, session_token)
        if not session:
            return None, "Invalid session token"
    else:
        session = create_onboarding_session(db)

    # Create user if it doesn't exist
    user = db.query(models.User).filter(models.User.email == data.email).first()

    if not user:
        # Generate a temporary password hash
        from app.auth import get_password_hash
        temp_password = "temporary_password"
        hashed_password = get_password_hash(temp_password)

        user = models.User(
            name=data.full_name,
            email=data.email,
            hashed_password=hashed_password,  # Add hashed password
            user_type=models.UserType.parent,  # Use the Enum value (changed from custodian to parent)
            is_active=True
        )
        db.add(user)
        db.commit()
        db.refresh(user)

    # Link user to session
    session.user_id = user.id

    # Update session with step data
    update_session_step(db, session, 1, data.model_dump())

    return session, None

def process_step2(db: Session, data: schemas.OnboardingStep2, session_token: str):
    """Process step 2: Identity verification"""
    print(f"\n==== PROCESSING STEP 2 ====\nSession token: {session_token}")

    # Check if session token is in the data object
    if hasattr(data, 'session_token') and data.session_token:
        print(f"Found session token in data object: {data.session_token}")
        if not session_token:
            session_token = data.session_token
            print(f"Using session token from data object: {session_token}")

    session = get_session_by_token(db, session_token)

    if not session:
        print("Error: Session not found")
        return None, "Invalid session token"

    if not session.step1_completed_at:
        print("Error: Step 1 not completed")
        return None, "Step 1 must be completed first"

    # Validate SSN format
    import re
    ssn = data.ssn
    ssn_regex = re.compile(r'^\d{3}-\d{2}-\d{4}$')

    if not ssn_regex.match(ssn):
        # Try to format it if it's just digits
        if re.match(r'^\d{9}$', ssn):
            ssn = f"{ssn[0:3]}-{ssn[3:5]}-{ssn[5:9]}"
            data.ssn = ssn
        else:
            return None, "SSN must be in the format XXX-XX-XXXX"

    # Update session with step data
    update_session_step(db, session, 2, data.model_dump())

    return session, None

def process_step3(db: Session, data: schemas.OnboardingStep3, session_token: str):
    """Process step 3: Financial profile"""
    session = get_session_by_token(db, session_token)

    if not session:
        return None, "Invalid session token"

    if not session.step2_completed_at:
        return None, "Step 2 must be completed first"

    # Update session with step data
    update_session_step(db, session, 3, data.model_dump())

    return session, None

def process_step4(db: Session, data: schemas.OnboardingStep4, session_token: str):
    """Process step 4: Disclosures and agreements"""
    session = get_session_by_token(db, session_token)

    if not session:
        return None, "Invalid session token"

    if not session.step3_completed_at:
        return None, "Step 3 must be completed first"

    # Validate agreements
    if not (data.customer_agreement_accepted and
            data.margin_agreement_accepted and
            data.account_agreement_accepted):
        return None, "All agreements must be accepted"

    # Update session with step data
    update_session_step(db, session, 4, data.model_dump())

    return session, None

def submit_to_alpaca(db: Session, session_token: str):
    """Submit completed onboarding data to Alpaca"""
    print(f"\n==== SUBMIT TO ALPACA ====\nSession Token: {session_token}")

    session = get_session_by_token(db, session_token)

    if not session:
        print(f"Error: Invalid session token: {session_token}")
        return None, {"error": "Invalid session token"}

    # Check if all steps are completed
    print(f"Checking step completion:\n" +
          f"Step 1: {session.step1_completed_at is not None}\n" +
          f"Step 2: {session.step2_completed_at is not None}\n" +
          f"Step 3: {session.step3_completed_at is not None}\n" +
          f"Step 4: {session.step4_completed_at is not None}")

    if not (session.step1_completed_at and
            session.step2_completed_at and
            session.step3_completed_at and
            session.step4_completed_at):
        print("Error: Not all steps are completed")
        return None, {"error": "All steps must be completed before submission"}

    # Get user
    user = db.query(models.User).filter(models.User.id == session.user_id).first()
    if not user:
        print(f"Error: User not found for user_id: {session.user_id}")
        return None, {"error": "User not found"}

    print(f"Found user: {user.name} ({user.email})")

    # Parse step data
    try:
        step1_data = json.loads(session.step1_data)
        step2_data = json.loads(session.step2_data)
        step3_data = json.loads(session.step3_data)
        step4_data = json.loads(session.step4_data)

        print("Successfully parsed all step data")
    except Exception as e:
        print(f"Error parsing step data: {str(e)}")
        return None, {"error": f"Error parsing step data: {str(e)}"}

    # Extract name parts and trim spaces
    full_name = step1_data["full_name"].strip()
    name_parts = full_name.split(" ", 1)
    given_name = name_parts[0].strip()
    family_name = name_parts[1].strip() if len(name_parts) > 1 else ""

    # Validate name parts
    if not given_name:
        return None, {"error": "First name cannot be empty"}
    if not family_name:
        return None, {"error": "Last name cannot be empty"}

    print(f"Name parts: given_name='{given_name}', family_name='{family_name}'")

    # Format date as YYYY-MM-DD
    try:
        if isinstance(step2_data["dob"], str):
            dob = datetime.strptime(step2_data["dob"], "%Y-%m-%d").strftime("%Y-%m-%d")
        else:
            dob = step2_data["dob"]
    except Exception as e:
        print(f"Error formatting date: {str(e)}")
        return None, {"error": f"Invalid date format: {step2_data['dob']}. Please use YYYY-MM-DD format."}

    # Map income and net worth ranges
    income_range = INCOME_RANGES.get(step3_data["income_range"], INCOME_RANGES["50k_100k"])
    net_worth_range = NET_WORTH_RANGES.get(step3_data["net_worth_range"], NET_WORTH_RANGES["50k_100k"])

    # Prepare payload for Alpaca Broker API
    payload = {
        "contact": {
            "email_address": step1_data["email"],
            "phone_number": step2_data["phone_number"],
            "street_address": [step2_data["street_address"]],
            "city": step2_data["city"],
            "state": step2_data["state"],
            "postal_code": step2_data["postal_code"],
            "country": step2_data["country"]
        },
        "identity": {
            "given_name": given_name,
            "family_name": family_name,
            "date_of_birth": dob,
            "tax_id": step2_data["ssn"],
            "tax_id_type": "USA_SSN",
            "country_of_citizenship": step2_data["country"],
            "country_of_birth": step2_data["country"],
            "country_of_tax_residence": step2_data["country"],
            "funding_source": [step3_data["funding_source"]],
            # Financial information
            "annual_income_min": income_range["min"],
            "annual_income_max": income_range["max"],
            "liquid_net_worth_min": net_worth_range["min"],
            "liquid_net_worth_max": net_worth_range["max"],
            "total_net_worth_min": net_worth_range["min"],
            "total_net_worth_max": net_worth_range["max"]
        },
        "disclosures": {
            "is_control_person": step4_data["is_control_person"],
            "is_affiliated_exchange_or_finra": step4_data["is_affiliated_exchange_or_finra"],
            "is_politically_exposed": step4_data["is_politically_exposed"],
            "immediate_family_exposed": step4_data["immediate_family_exposed"]
        },
        "agreements": [
            {
                "agreement": "customer_agreement",
                "signed_at": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
                "ip_address": "127.0.0.1"
            },
            {
                "agreement": "margin_agreement",
                "signed_at": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
                "ip_address": "127.0.0.1"
            },
            {
                "agreement": "account_agreement",
                "signed_at": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
                "ip_address": "127.0.0.1"
            }
        ],
        "documents": [],
        "trusted_contact": {
            "given_name": given_name,
            "family_name": family_name,
            "email_address": step1_data["email"]
        },
        "enabled_assets": ["us_equity"],
        "account_type": "trading"
    }

    # Print the payload for debugging
    print("\n==== ALPACA PAYLOAD ====")
    print(json.dumps(payload, indent=2))

    # Submit to Alpaca
    print("\nCalling Alpaca Broker API...")
    result, error = onboard_alpaca_user(payload)

    if error:
        print(f"\nAlpaca API Error: {json.dumps(error, indent=2)}")
        return None, error

    print(f"\nAlpaca API Success: {json.dumps(result, indent=2)}")

    # Save broker account
    new_account = models.BrokerAccount(
        user_id=user.id,
        broker_type="alpaca",
        external_account_id=result.get("id", "unknown"),
        status=result.get("status", "unknown")
    )

    db.add(new_account)
    db.commit()
    db.refresh(new_account)

    return {
        "message": "Alpaca onboarding submitted",
        "account_id": new_account.external_account_id,
        "status": new_account.status
    }, None
