#!/usr/bin/env python3
"""
Test script for Phase 2 Relationship functionality
"""

import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
TEST_EMAIL_1 = "<EMAIL>"
TEST_EMAIL_2 = "<EMAIL>"
TEST_PASSWORD = "testpassword123"

class RelationshipTester:
    def __init__(self):
        self.session = requests.Session()
        self.parent1_token = None
        self.parent2_token = None
        self.child_profile_id = None
        self.child_handle = None
        self.relationship_id = None

    def log(self, message):
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")

    def register_user(self, email, name):
        """Register a new user"""
        self.log(f"Registering user: {email}")
        
        data = {
            "name": name,
            "email": email,
            "password": TEST_PASSWORD
        }
        
        response = self.session.post(f"{BASE_URL}/api/register", json=data)
        if response.status_code == 200:
            self.log(f"✅ User registered successfully: {email}")
            return True
        else:
            self.log(f"❌ Failed to register user: {response.status_code} - {response.text}")
            return False

    def login_user(self, email):
        """Login user and get token"""
        self.log(f"Logging in user: {email}")
        
        data = {
            "username": email,
            "password": TEST_PASSWORD
        }
        
        response = self.session.post(f"{BASE_URL}/api/token", data=data)
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            self.log(f"✅ User logged in successfully: {email}")
            return token
        else:
            self.log(f"❌ Failed to login user: {response.status_code} - {response.text}")
            return None

    def create_child_profile(self, token, name, handle):
        """Create a child profile"""
        self.log(f"Creating child profile: {name}")
        
        headers = {"Authorization": f"Bearer {token}"}
        data = {
            "name": name,
            "handle": handle,
            "age": 8,
            "bio": "Test child profile for relationship testing",
            "is_public": True
        }
        
        response = self.session.post(f"{BASE_URL}/api/child-profiles", json=data, headers=headers)
        if response.status_code == 200:
            profile_data = response.json()
            self.log(f"✅ Child profile created: {profile_data.get('id')}")
            return profile_data.get('id'), profile_data.get('handle')
        else:
            self.log(f"❌ Failed to create child profile: {response.status_code} - {response.text}")
            return None, None

    def search_child(self, token, child_handle):
        """Search for a child by handle"""
        self.log(f"Searching for child: {child_handle}")
        
        headers = {"Authorization": f"Bearer {token}"}
        data = {
            "child_handle": child_handle,
            "relationship_type": "family_friend",
            "description": "Test relationship request"
        }
        
        response = self.session.post(f"{BASE_URL}/api/relationships/search", json=data, headers=headers)
        if response.status_code == 200:
            search_data = response.json()
            self.log(f"✅ Child found: {search_data.get('child_name')}")
            return search_data
        else:
            self.log(f"❌ Failed to search for child: {response.status_code} - {response.text}")
            return None

    def create_relationship_request(self, token, child_handle):
        """Create a relationship request"""
        self.log(f"Creating relationship request for: {child_handle}")
        
        headers = {"Authorization": f"Bearer {token}"}
        data = {
            "to_child_handle": child_handle,
            "relationship_type": "family_friend",
            "description": "Test relationship from automated testing"
        }
        
        response = self.session.post(f"{BASE_URL}/api/relationships/", json=data, headers=headers)
        if response.status_code == 200:
            relationship_data = response.json()
            self.log(f"✅ Relationship request created: {relationship_data.get('id')}")
            return relationship_data.get('id')
        else:
            self.log(f"❌ Failed to create relationship request: {response.status_code} - {response.text}")
            return None

    def get_relationships(self, token, status=None):
        """Get user's relationships"""
        self.log(f"Getting relationships (status: {status or 'all'})")
        
        headers = {"Authorization": f"Bearer {token}"}
        params = {"status": status} if status else {}
        
        response = self.session.get(f"{BASE_URL}/api/relationships/", headers=headers, params=params)
        if response.status_code == 200:
            relationships_data = response.json()
            self.log(f"✅ Retrieved {relationships_data.get('total_count', 0)} relationships")
            return relationships_data
        else:
            self.log(f"❌ Failed to get relationships: {response.status_code} - {response.text}")
            return None

    def update_relationship_status(self, token, relationship_id, status):
        """Update relationship status"""
        self.log(f"Updating relationship {relationship_id} to {status}")
        
        headers = {"Authorization": f"Bearer {token}"}
        data = {"status": status}
        
        response = self.session.patch(f"{BASE_URL}/api/relationships/{relationship_id}", json=data, headers=headers)
        if response.status_code == 200:
            relationship_data = response.json()
            self.log(f"✅ Relationship status updated to: {relationship_data.get('status')}")
            return True
        else:
            self.log(f"❌ Failed to update relationship status: {response.status_code} - {response.text}")
            return False

    def run_full_test(self):
        """Run complete relationship testing flow"""
        self.log("🚀 Starting Phase 2 Relationship Testing")
        
        # Step 1: Register two users
        self.log("\n📝 Step 1: User Registration")
        if not self.register_user(TEST_EMAIL_1, "Parent One"):
            return False
        if not self.register_user(TEST_EMAIL_2, "Parent Two"):
            return False
        
        # Step 2: Login both users
        self.log("\n🔐 Step 2: User Authentication")
        self.parent1_token = self.login_user(TEST_EMAIL_1)
        if not self.parent1_token:
            return False
        
        self.parent2_token = self.login_user(TEST_EMAIL_2)
        if not self.parent2_token:
            return False
        
        # Step 3: Create child profile (Parent 1)
        self.log("\n👶 Step 3: Child Profile Creation")
        self.child_profile_id, self.child_handle = self.create_child_profile(
            self.parent1_token, 
            "Emma Test", 
            f"emma-test-{datetime.now().strftime('%H%M%S')}"
        )
        if not self.child_profile_id:
            return False
        
        # Step 4: Search for child (Parent 2)
        self.log("\n🔍 Step 4: Child Search")
        search_result = self.search_child(self.parent2_token, self.child_handle)
        if not search_result:
            return False
        
        # Step 5: Create relationship request (Parent 2)
        self.log("\n🤝 Step 5: Relationship Request")
        self.relationship_id = self.create_relationship_request(self.parent2_token, self.child_handle)
        if not self.relationship_id:
            return False
        
        # Step 6: Check pending relationships (Parent 1)
        self.log("\n📋 Step 6: View Pending Relationships")
        pending_relationships = self.get_relationships(self.parent1_token, "pending")
        if not pending_relationships or pending_relationships.get('pending_count', 0) == 0:
            self.log("❌ No pending relationships found")
            return False
        
        # Step 7: Approve relationship (Parent 1)
        self.log("\n✅ Step 7: Approve Relationship")
        if not self.update_relationship_status(self.parent1_token, self.relationship_id, "active"):
            return False
        
        # Step 8: Verify active relationship (Parent 2)
        self.log("\n🔗 Step 8: Verify Active Relationship")
        active_relationships = self.get_relationships(self.parent2_token, "active")
        if not active_relationships or active_relationships.get('active_count', 0) == 0:
            self.log("❌ No active relationships found")
            return False
        
        # Step 9: Test decline functionality
        self.log("\n❌ Step 9: Test Decline Functionality")
        # Create another request to test decline
        decline_relationship_id = self.create_relationship_request(self.parent2_token, self.child_handle)
        if decline_relationship_id:
            if self.update_relationship_status(self.parent1_token, decline_relationship_id, "declined"):
                self.log("✅ Decline functionality working")
            else:
                self.log("❌ Decline functionality failed")
        
        self.log("\n🎉 All relationship tests completed successfully!")
        return True

def main():
    """Main test function"""
    tester = RelationshipTester()
    
    try:
        success = tester.run_full_test()
        if success:
            print("\n✅ Phase 2 Relationship Testing: PASSED")
            sys.exit(0)
        else:
            print("\n❌ Phase 2 Relationship Testing: FAILED")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test execution failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
