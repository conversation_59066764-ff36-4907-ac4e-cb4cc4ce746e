# Start-All.ps1
# This script starts both the backend and frontend servers

# Set execution policy for this process
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process

# Function to check if a port is in use
function Test-PortInUse {
    param (
        [int]$Port
    )

    $connections = Get-NetTCPConnection -State Listen -ErrorAction SilentlyContinue | Where-Object { $_.LocalPort -eq $Port }
    return ($null -ne $connections)
}

# Check if backend port is already in use
if (Test-PortInUse -Port 8000) {
    Write-Host "Port 8000 is already in use. Backend server may already be running." -ForegroundColor Yellow
    $startBackend = Read-Host "Do you want to try starting the backend server anyway? (y/n)"
    if ($startBackend -ne "y") {
        Write-Host "Skipping backend server startup." -ForegroundColor Yellow
    }
} else {
    $startBackend = "y"
}

# Check if frontend port is already in use
if (Test-PortInUse -Port 19000) {
    Write-Host "Port 19000 is already in use. Frontend server may already be running." -ForegroundColor Yellow
    $startFrontend = Read-Host "Do you want to try starting the frontend server anyway? (y/n)"
    if ($startFrontend -ne "y") {
        Write-Host "Skipping frontend server startup." -ForegroundColor Yellow
    }
} else {
    $startFrontend = "y"
}

# Start the backend server in a new PowerShell window if requested
if ($startBackend -eq "y") {
    Write-Host "Starting backend server..." -ForegroundColor Green
    $backendCommand = @"
cd '$PSScriptRoot'
.\venv\Scripts\activate
`$env:PYTHONPATH = '$PSScriptRoot'
Write-Host 'PYTHONPATH set to: ' `$env:PYTHONPATH -ForegroundColor Cyan
Write-Host 'Attempting to start the backend server...' -ForegroundColor Green
try {
    # First attempt: Run as a module
    Write-Host 'Attempt 1: Running as a module...' -ForegroundColor Cyan
    python -m app.main
} catch {
    Write-Host 'First attempt failed: ' `$_ -ForegroundColor Yellow

    try {
        # Second attempt: Run the file directly
        Write-Host 'Attempt 2: Running the file directly...' -ForegroundColor Cyan
        python '$PSScriptRoot\app\main.py'
    } catch {
        Write-Host 'Second attempt failed: ' `$_ -ForegroundColor Yellow

        # Third attempt: Use the simple_backend.py if it exists
        if (Test-Path '$PSScriptRoot\simple_backend.py') {
            Write-Host 'Attempt 3: Running simple_backend.py...' -ForegroundColor Cyan
            python '$PSScriptRoot\simple_backend.py'
        } else {
            Write-Host 'No simple_backend.py found. All attempts failed.' -ForegroundColor Red
        }
    }
}
"@
    Start-Process powershell -ArgumentList "-NoExit", "-Command", $backendCommand
}

# Start the frontend server in a new PowerShell window if requested
if ($startFrontend -eq "y") {
    Write-Host "Starting frontend server..." -ForegroundColor Green
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PSScriptRoot\kidvest-app-new'; npm start"
}

Write-Host "Servers started. Check the opened PowerShell windows for details." -ForegroundColor Green
Write-Host "Backend URL: http://localhost:8000" -ForegroundColor Cyan
Write-Host "Frontend URL: http://localhost:19000" -ForegroundColor Cyan
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
