import requests
import json

def get_auth_token(email, password):
    """Get an authentication token from the API"""
    url = "http://localhost:8080/api/token"
    
    # The API expects form data, not JSON
    data = {
        "username": email,  # OAuth2 uses 'username' even though we're using email
        "password": password
    }
    
    try:
        response = requests.post(url, data=data)
        
        if response.status_code == 200:
            token_data = response.json()
            return token_data["access_token"]
        else:
            print(f"Error getting token: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"Exception: {str(e)}")
        return None

if __name__ == "__main__":
    # Get email and password from user
    email = input("Enter your email: ")
    password = input("Enter your password: ")
    
    # Get token
    token = get_auth_token(email, password)
    
    if token:
        print(f"\nAuthentication successful!")
        print(f"Token: {token}")
        
        # Save token to localStorage
        print("\nTo use this token in the browser, open the developer console and run:")
        print(f"localStorage.setItem('access_token', '{token}')")
    else:
        print("\nAuthentication failed.")
