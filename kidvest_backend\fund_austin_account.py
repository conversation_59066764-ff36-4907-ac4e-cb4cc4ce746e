#!/usr/bin/env python3

"""
Fund Austin's Alpaca account for testing
"""

import requests
import json
import os
import base64
from datetime import datetime

AUSTIN_ACCOUNT_ID = "25aa2245-9d53-42f9-b3b3-43cb899c1798"

def check_account_balance():
    """Check current account balance"""
    print("💰 Checking Austin's account balance...")
    
    api_key = os.getenv("ALPACA_API_KEY")
    api_secret = os.getenv("ALPACA_SECRET_KEY")
    
    auth_string = f"{api_key}:{api_secret}"
    encoded_auth = base64.b64encode(auth_string.encode()).decode()
    
    headers = {
        "Authorization": f"Basic {encoded_auth}",
        "Content-Type": "application/json"
    }
    
    url = f"https://broker-api.sandbox.alpaca.markets/v1/trading/accounts/{AUSTIN_ACCOUNT_ID}/account"
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   Account Details: {json.dumps(result, indent=2)}")
            
            buying_power = result.get('buying_power', '0')
            cash = result.get('cash', '0')
            equity = result.get('equity', '0')
            
            print(f"   💰 Cash: ${cash}")
            print(f"   💰 Buying Power: ${buying_power}")
            print(f"   💰 Equity: ${equity}")
            
            return float(buying_power)
        else:
            print(f"   Error: {response.text}")
            return 0
            
    except Exception as e:
        print(f"   Error: {str(e)}")
        return 0

def try_fund_account():
    """Try to fund the account (sandbox specific)"""
    print("\n💳 Attempting to fund account...")
    
    api_key = os.getenv("ALPACA_API_KEY")
    api_secret = os.getenv("ALPACA_SECRET_KEY")
    
    auth_string = f"{api_key}:{api_secret}"
    encoded_auth = base64.b64encode(auth_string.encode()).decode()
    
    headers = {
        "Authorization": f"Basic {encoded_auth}",
        "Content-Type": "application/json"
    }
    
    # Try different funding endpoints
    funding_endpoints = [
        {
            "name": "ACH Transfer",
            "url": f"https://broker-api.sandbox.alpaca.markets/v1/accounts/{AUSTIN_ACCOUNT_ID}/ach_relationships",
            "method": "POST",
            "payload": {
                "account_owner_name": "austin glusac",
                "bank_account_type": "CHECKING",
                "bank_account_number": "*********",
                "bank_routing_number": "*********",
                "nickname": "Test Bank"
            }
        },
        {
            "name": "Journal Entry",
            "url": f"https://broker-api.sandbox.alpaca.markets/v1/journals",
            "method": "POST",
            "payload": {
                "entry_type": "JNLC",
                "from_account": "master",
                "to_account": AUSTIN_ACCOUNT_ID,
                "amount": "1000.00",
                "description": "Test funding"
            }
        },
        {
            "name": "Sandbox Funding",
            "url": f"https://broker-api.sandbox.alpaca.markets/v1/accounts/{AUSTIN_ACCOUNT_ID}/funding",
            "method": "POST",
            "payload": {
                "amount": "1000.00",
                "type": "deposit",
                "method": "sandbox"
            }
        }
    ]
    
    for endpoint in funding_endpoints:
        print(f"\n   Trying {endpoint['name']}...")
        print(f"   URL: {endpoint['url']}")
        print(f"   Payload: {json.dumps(endpoint['payload'], indent=2)}")
        
        try:
            if endpoint['method'] == 'POST':
                response = requests.post(
                    endpoint['url'], 
                    headers=headers, 
                    json=endpoint['payload'], 
                    timeout=30
                )
            else:
                response = requests.get(endpoint['url'], headers=headers, timeout=30)
            
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.text}")
            
            if response.status_code in [200, 201]:
                print(f"   ✅ {endpoint['name']} successful!")
                return True
            else:
                print(f"   ❌ {endpoint['name']} failed")
                
        except Exception as e:
            print(f"   ❌ {endpoint['name']} error: {str(e)}")
    
    return False

def check_sandbox_limitations():
    """Check sandbox account limitations"""
    print("\n🔍 Checking sandbox limitations...")
    
    print("   📋 Alpaca Sandbox Account Limitations:")
    print("   - Accounts start with $0 balance")
    print("   - No real money transfers")
    print("   - Limited funding options")
    print("   - May require manual funding via Alpaca dashboard")
    print("   - Some features may be disabled")
    
    print("\n   💡 Possible Solutions:")
    print("   1. Check Alpaca dashboard for funding options")
    print("   2. Contact Alpaca support for sandbox funding")
    print("   3. Use paper trading account instead")
    print("   4. Simulate trades without actual execution")

def simulate_successful_trade():
    """Simulate a successful trade for testing"""
    print("\n🎭 Simulating successful trade for testing...")
    
    # Create a mock successful response
    mock_response = {
        "id": "mock_order_123",
        "symbol": "SPY",
        "qty": "0.027",
        "side": "buy",
        "status": "filled",
        "submitted_at": datetime.now().isoformat(),
        "filled_qty": "0.027",
        "filled_avg_price": "550.00"
    }
    
    print(f"   Mock Order Response: {json.dumps(mock_response, indent=2)}")
    print("   ✅ This is what a successful order would look like")
    
    return mock_response

def main():
    """Main function"""
    print("💰 Austin Account Funding Test")
    print("=" * 50)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Check current balance
    current_balance = check_account_balance()
    
    # Step 2: Try to fund account
    if current_balance == 0:
        print(f"\n❌ Account has $0 buying power - funding required")
        funding_success = try_fund_account()
        
        if not funding_success:
            print(f"\n❌ Could not fund account automatically")
            check_sandbox_limitations()
            
            # Step 3: Simulate for testing
            print(f"\n🎭 For testing purposes, let's simulate a successful trade:")
            mock_trade = simulate_successful_trade()
            
            print(f"\n📋 NEXT STEPS:")
            print(f"1. 💰 Fund Austin's account manually:")
            print(f"   - Login to Alpaca dashboard")
            print(f"   - Navigate to account {AUSTIN_ACCOUNT_ID}")
            print(f"   - Add test funds (sandbox)")
            print(f"")
            print(f"2. 🔄 Alternative: Modify backend to simulate trades:")
            print(f"   - Skip actual Alpaca order placement")
            print(f"   - Record investment in database")
            print(f"   - Show success message to user")
            print(f"")
            print(f"3. 🧪 Test with simulation:")
            print(f"   - Frontend will work normally")
            print(f"   - Backend records investment")
            print(f"   - No real money involved")
            
            return False
    else:
        print(f"✅ Account has ${current_balance} buying power")
        return True

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted")
        exit(1)
    except Exception as e:
        print(f"\n💥 Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        exit(1)
