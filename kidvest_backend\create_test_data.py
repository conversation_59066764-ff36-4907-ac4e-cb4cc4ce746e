from sqlalchemy.orm import Session
from app.database import SessionLocal, engine
from app import models
import uuid

def create_test_data():
    """Create test data in the database"""
    db = SessionLocal()
    
    try:
        # Create a parent user
        parent = models.User(
            id=uuid.uuid4(),
            name="<PERSON>",
            email="<EMAIL>",
            user_type="parent",
            phone_number="************",
            address="123 Main St",
            city="Anytown",
            state="CA",
            postal_code="12345",
            country="USA"
        )
        db.add(parent)
        db.commit()
        db.refresh(parent)
        print(f"Created parent user: {parent.name} ({parent.id})")
        
        # Create a child profile
        child = models.ChildProfile(
            id=uuid.uuid4(),
            parent_id=parent.id,
            name="<PERSON>",
            age=10,
            handle="jane_doe",
            is_public=True,
            bio="I love investing and learning about money!"
        )
        db.add(child)
        db.commit()
        db.refresh(child)
        print(f"Created child profile: {child.name} (@{child.handle})")
        
        print("\nSetup completed successfully!")
        print(f"Parent: {parent.name} ({parent.id})")
        print(f"Child: {child.name} (@{child.handle})")
        print(f"\nYou can now access the gift wall at: http://localhost:8082/wall/{child.handle}")
        
    except Exception as e:
        db.rollback()
        print(f"Error creating test data: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    create_test_data()
