from pydantic import BaseModel, EmailStr, Field
from uuid import UUID
from enum import Enum
from datetime import datetime, date
from typing import Optional, List

class UserType(str, Enum):
    parent = "parent"
    child = "child"

class RelationshipType(str, Enum):
    parent = "parent"
    grandparent = "grandparent"
    aunt_uncle = "aunt_uncle"
    sibling = "sibling"
    cousin = "cousin"
    family_friend = "family_friend"
    godparent = "godparent"
    other = "other"

class RelationshipStatus(str, Enum):
    pending = "pending"
    active = "active"
    declined = "declined"
    blocked = "blocked"

# User schemas
class UserBase(BaseModel):
    name: str
    email: EmailStr
    user_type: UserType

class UserCreate(UserBase):
    password: str
    phone_number: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None

class UserUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone_number: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None
    is_active: Optional[bool] = None

class UserResponse(UserBase):
    id: UUID
    created_at: datetime
    is_active: bool
    phone_number: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None

    class Config:
        from_attributes = True

# Child Profile schemas
class ChildProfileBase(BaseModel):
    name: str
    age: Optional[int] = None
    handle: str
    is_public: bool = True
    bio: Optional[str] = None
    dob: Optional[date] = None

class ChildProfileCreate(ChildProfileBase):
    pass

class ChildProfileUpdate(BaseModel):
    name: Optional[str] = None
    age: Optional[int] = None
    handle: Optional[str] = None
    is_public: Optional[bool] = None
    bio: Optional[str] = None
    avatar: Optional[str] = None
    dob: Optional[date] = None

class ChildProfileResponse(ChildProfileBase):
    id: UUID
    parent_id: UUID
    avatar: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Gift schemas
class GiftBase(BaseModel):
    from_name: Optional[str] = None
    from_email: Optional[EmailStr] = None
    amount_usd: float = Field(..., gt=0)
    message: Optional[str] = None
    is_anonymous: bool = False

class GiftCreate(GiftBase):
    child_profile_id: UUID

class GiftWallCreate(GiftBase):
    child_profile_handle: str

class GiftUpdate(BaseModel):
    payment_status: Optional[str] = None
    payment_intent_id: Optional[str] = None
    checkout_session_id: Optional[str] = None

class GiftResponse(GiftBase):
    id: UUID
    child_profile_id: UUID
    payment_status: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class GiftWallResponse(BaseModel):
    id: UUID
    from_name: Optional[str] = None
    amount_usd: float
    message: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True

# Investment schemas
class InvestmentBase(BaseModel):
    amount_usd: float = Field(..., gt=0)
    symbol: str
    shares: float = Field(..., gt=0)
    purchase_price: float = Field(..., gt=0)

class InvestmentCreate(InvestmentBase):
    child_profile_id: UUID
    gift_id: Optional[UUID] = None

class InvestmentUpdate(BaseModel):
    status: Optional[str] = None
    transaction_id: Optional[str] = None

class InvestmentResponse(InvestmentBase):
    id: UUID
    child_profile_id: UUID
    gift_id: Optional[UUID] = None
    status: str
    transaction_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Gift Wall response
class GiftWallProfileResponse(BaseModel):
    profile: ChildProfileResponse
    gifts: List[GiftWallResponse]
    total_gifts: int
    total_amount: float

# Checkout session response
class CheckoutSessionResponse(BaseModel):
    success: bool
    gift_id: str
    checkout_url: str

# Portfolio and Analytics schemas
class ChildPortfolioSummary(BaseModel):
    child_profile_id: UUID
    child_name: str
    total_gifts_received: float
    total_invested: float
    available_balance: float
    total_portfolio_value: float
    total_gifts_count: int
    total_investments_count: int

    class Config:
        from_attributes = True

class InvestmentSummary(BaseModel):
    symbol: str
    total_shares: float
    average_purchase_price: float
    total_invested: float
    current_value: Optional[float] = None
    gain_loss: Optional[float] = None
    gain_loss_percentage: Optional[float] = None

class ChildPortfolioDetail(BaseModel):
    profile: ChildProfileResponse
    portfolio_summary: ChildPortfolioSummary
    recent_gifts: List[GiftResponse]
    investments_by_symbol: List[InvestmentSummary]
    recent_investments: List[InvestmentResponse]

    class Config:
        from_attributes = True

# Enhanced Gift Response with Investment Status
class GiftResponseWithInvestments(GiftResponse):
    invested_amount: float = 0.0
    remaining_amount: float = 0.0
    investments: List[InvestmentResponse] = []

    class Config:
        from_attributes = True

# Investment Creation from Gift
class InvestmentFromGiftCreate(BaseModel):
    gift_id: UUID
    symbol: str
    amount_usd: float = Field(..., gt=0)

    class Config:
        from_attributes = True

# Original onboarding request (keeping for backward compatibility)
class AlpacaOnboardingRequest(BaseModel):
    full_name: str
    email: EmailStr
    dob: date
    phone_number: str
    street_address: str
    city: str
    state: str
    postal_code: str
    country: str
    ssn: str
    employment_status: str
    funding_source: str

# Multi-step onboarding schemas
class OnboardingStep1(BaseModel):
    """Basic account creation"""
    full_name: str
    email: EmailStr

class OnboardingStep2(BaseModel):
    """Identity verification"""
    dob: date
    phone_number: str
    street_address: str
    city: str
    state: str
    postal_code: str
    country: str = "USA"
    ssn: str
    session_token: Optional[str] = None  # Added for redundancy

class OnboardingStep3(BaseModel):
    """Financial profile"""
    employment_status: str
    income_range: str  # under_50k, 50k_100k, 100k_250k, over_250k
    net_worth_range: str  # under_50k, 50k_100k, 100k_250k, over_250k
    funding_source: str
    investment_experience: str  # none, some, experienced
    risk_tolerance: str  # conservative, moderate, aggressive
    session_token: Optional[str] = None  # Added for redundancy

class OnboardingStep4(BaseModel):
    """Disclosures and agreements"""
    is_control_person: bool = False
    is_affiliated_exchange_or_finra: bool = False
    is_politically_exposed: bool = False
    immediate_family_exposed: bool = False
    customer_agreement_accepted: bool = False
    margin_agreement_accepted: bool = False
    account_agreement_accepted: bool = False
    session_token: Optional[str] = None  # Added for redundancy

class OnboardingSession(BaseModel):
    """Session token for tracking onboarding progress"""
    session_token: str
    current_step: int
    next_step: int

class OnboardingResponse(BaseModel):
    """Response for onboarding steps"""
    success: bool
    message: str
    session_token: Optional[str] = None
    current_step: Optional[int] = None
    next_step: Optional[int] = None
    details: Optional[dict] = None

# Relationship schemas
class RelationshipBase(BaseModel):
    relationship_type: RelationshipType
    description: Optional[str] = None

class RelationshipCreate(RelationshipBase):
    to_child_handle: str  # Use handle to identify child

class RelationshipUpdate(BaseModel):
    status: RelationshipStatus
    description: Optional[str] = None

class RelationshipResponse(RelationshipBase):
    id: UUID
    from_user_id: UUID
    to_child_id: UUID
    status: RelationshipStatus
    requested_by_user_id: UUID
    created_at: datetime
    updated_at: datetime
    approved_at: Optional[datetime] = None

    # Include related data for convenience
    from_user_name: Optional[str] = None
    from_user_email: Optional[str] = None
    child_name: Optional[str] = None
    child_handle: Optional[str] = None
    requested_by_name: Optional[str] = None

    class Config:
        from_attributes = True

class RelationshipSearchRequest(BaseModel):
    child_handle: str
    relationship_type: RelationshipType
    description: Optional[str] = None

class RelationshipListResponse(BaseModel):
    relationships: List[RelationshipResponse]
    total_count: int
    pending_count: int
    active_count: int
