#!/usr/bin/env python3

"""
Database migration script to add credential fields to BrokerAccount table
"""

import os
import sys
from sqlalchemy import text
from app.database import SessionLocal, engine

def run_migration():
    """Add new credential fields to broker_accounts table"""
    print("🔄 Starting BrokerAccount credentials migration...")
    
    db = SessionLocal()
    try:
        # Check if columns already exist
        print("1. Checking existing table structure...")
        
        result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'broker_accounts' 
            AND column_name IN ('bearer_token', 'api_key_id', 'api_secret_key', 'trading_enabled')
        """))
        
        existing_columns = [row[0] for row in result.fetchall()]
        print(f"   Existing credential columns: {existing_columns}")
        
        # Add missing columns
        columns_to_add = [
            ("bearer_token", "VARCHAR"),
            ("api_key_id", "VARCHAR"),
            ("api_secret_key", "VARCHAR"),
            ("trading_enabled", "BOOLEAN DEFAULT FALSE")
        ]
        
        for column_name, column_type in columns_to_add:
            if column_name not in existing_columns:
                print(f"2. Adding column: {column_name}")
                
                if column_name == "trading_enabled":
                    # Special handling for boolean with default
                    db.execute(text(f"""
                        ALTER TABLE broker_accounts 
                        ADD COLUMN {column_name} {column_type}
                    """))
                else:
                    db.execute(text(f"""
                        ALTER TABLE broker_accounts 
                        ADD COLUMN {column_name} {column_type}
                    """))
                
                db.commit()
                print(f"   ✅ Added {column_name}")
            else:
                print(f"   ⏭️ Column {column_name} already exists")
        
        # Update existing accounts to enable trading (for development)
        print("3. Enabling trading for existing accounts...")
        result = db.execute(text("""
            UPDATE broker_accounts 
            SET trading_enabled = TRUE 
            WHERE status = 'active' AND trading_enabled IS NULL
        """))
        
        updated_count = result.rowcount
        db.commit()
        print(f"   ✅ Enabled trading for {updated_count} existing accounts")
        
        # Show final table structure
        print("4. Final table structure:")
        result = db.execute(text("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'broker_accounts'
            ORDER BY ordinal_position
        """))
        
        for row in result.fetchall():
            print(f"   - {row[0]}: {row[1]} (nullable: {row[2]}, default: {row[3]})")
        
        print("\n✅ BrokerAccount credentials migration completed successfully!")
        
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def verify_migration():
    """Verify the migration was successful"""
    print("\n🔍 Verifying migration...")
    
    db = SessionLocal()
    try:
        # Check if all required columns exist
        result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'broker_accounts'
        """))
        
        columns = [row[0] for row in result.fetchall()]
        required_columns = [
            'id', 'user_id', 'broker_type', 'external_account_id', 
            'bearer_token', 'api_key_id', 'api_secret_key', 'trading_enabled',
            'status', 'created_at', 'updated_at'
        ]
        
        missing_columns = [col for col in required_columns if col not in columns]
        
        if missing_columns:
            print(f"❌ Missing columns: {missing_columns}")
            return False
        else:
            print("✅ All required columns present")
        
        # Check if we can query the table
        result = db.execute(text("SELECT COUNT(*) FROM broker_accounts"))
        count = result.scalar()
        print(f"✅ Table accessible, {count} broker accounts found")
        
        return True
        
    except Exception as e:
        print(f"❌ Verification failed: {str(e)}")
        return False
    finally:
        db.close()

def show_usage():
    """Show usage instructions"""
    print("""
🔧 BrokerAccount Credentials Migration

This script adds the following fields to the broker_accounts table:
- bearer_token: Account-specific bearer token (encrypted)
- api_key_id: Account-specific API key
- api_secret_key: Account-specific secret key (encrypted)
- trading_enabled: Whether trading is enabled for this account

Usage:
    python migrate_broker_account_credentials.py

After running this migration:
1. Existing accounts will have trading_enabled set to TRUE
2. New credential fields will be available for account-specific trading
3. The manual investment flow will use account-specific credentials

Security Note:
- Implement proper encryption for bearer_token and api_secret_key
- Update encrypt_secret() and decrypt_secret() functions in alpaca_service.py
""")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help"]:
        show_usage()
        sys.exit(0)
    
    print("🚀 BrokerAccount Credentials Migration")
    print("=" * 50)
    
    try:
        run_migration()
        
        if verify_migration():
            print("\n🎉 Migration completed successfully!")
            print("\n📋 Next Steps:")
            print("1. Implement proper encryption in alpaca_service.py")
            print("2. Update onboarding to store account credentials")
            print("3. Test manual investment flow")
            print("4. Restart your backend server")
        else:
            print("\n❌ Migration verification failed!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Migration failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
