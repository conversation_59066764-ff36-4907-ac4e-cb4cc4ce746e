import subprocess
import os
import webbrowser
import time
import signal
import sys

# Define the base directory
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# Define the server commands
SERVERS = [
    {
        "name": "Backend Server",
        "command": ["python", "-m", "uvicorn", "app.main:app", "--reload", "--log-level", "debug"],
        "cwd": BASE_DIR,
        "port": 8000
    },
    {
        "name": "Unified UI Server",
        "command": ["python", "simple_unified_server.py"],
        "cwd": BASE_DIR,
        "port": 8080
    }
]

# Start the servers
processes = []

def signal_handler(sig, frame):
    print("\nStopping all servers...")
    for process in processes:
        process.terminate()
    sys.exit(0)

signal.signal(signal.SIGINT, signal_handler)

try:
    for server in SERVERS:
        print(f"Starting {server['name']}...")
        process = subprocess.Popen(
            server["command"],
            cwd=server["cwd"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        processes.append(process)
        print(f"{server['name']} started on port {server['port']}")
        time.sleep(1)  # Give the server time to start

    # Open the unified UI in the browser
    print("\nOpening unified UI in browser...")
    webbrowser.open("http://localhost:8080")

    # Keep the script running
    print("\nAll servers started. Press Ctrl+C to stop all servers.")
    while True:
        time.sleep(1)
except KeyboardInterrupt:
    signal_handler(None, None)
except Exception as e:
    print(f"Error: {str(e)}")
    signal_handler(None, None)
