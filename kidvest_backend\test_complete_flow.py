#!/usr/bin/env python3

import requests
import json
import time
from datetime import datetime

def test_complete_payment_flow():
    """Test the complete payment flow from gift creation to webhook"""
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🧪 Testing Complete Payment Flow")
    print("=" * 60)
    
    # Step 1: Create a test gift
    print("🎁 Step 1: Creating test gift...")
    
    test_handle = "test-child-165125"
    gift_data = {
        "child_profile_handle": test_handle,
        "from_name": "Flow Test User",
        "from_email": "<EMAIL>",
        "amount_usd": 15.0,
        "message": "Complete flow test gift",
        "is_anonymous": False
    }
    
    try:
        api_url = f"http://localhost:8000/api/wall/{test_handle}/gift"
        response = requests.post(
            api_url,
            json=gift_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            response_data = response.json()
            gift_id = response_data.get('gift_id')
            checkout_url = response_data.get('checkout_url')
            
            print(f"✅ Gift created successfully!")
            print(f"🆔 Gift ID: {gift_id}")
            print(f"🔗 Checkout URL: {checkout_url}")
            
            # Extract session ID
            if checkout_url and 'checkout.stripe.com' in checkout_url:
                session_id = checkout_url.split('/')[-1].split('#')[0]
                print(f"🎫 Stripe Session ID: {session_id}")
            else:
                print(f"❌ Invalid checkout URL format")
                return False
                
        else:
            print(f"❌ Failed to create gift: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error creating gift: {str(e)}")
        return False
    
    # Step 2: Check initial gift status
    print(f"\n💾 Step 2: Checking initial gift status...")
    
    try:
        from app.database import SessionLocal
        from app import models
        from uuid import UUID
        
        db = SessionLocal()
        try:
            gift = db.query(models.Gift).filter(models.Gift.id == UUID(gift_id)).first()
            if gift:
                print(f"✅ Gift found in database")
                print(f"📊 Initial status: {gift.payment_status}")
                print(f"💰 Amount: ${gift.amount_usd}")
                
                if gift.payment_status != "pending":
                    print(f"⚠️ Expected 'pending' status, got '{gift.payment_status}'")
                    
            else:
                print(f"❌ Gift not found in database")
                return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Error checking gift status: {str(e)}")
        return False
    
    # Step 3: Display next steps
    print(f"\n🎯 Step 3: Manual Payment Test")
    print("-" * 40)
    print(f"Now you need to:")
    print(f"1. 🌐 Open this URL in your browser:")
    print(f"   {checkout_url}")
    print(f"2. 💳 Complete payment with test card:")
    print(f"   Card: ************** 4242")
    print(f"   Expiry: 12/25")
    print(f"   CVC: 123")
    print(f"3. 👀 Watch your backend terminal for webhook logs")
    print(f"4. 🔍 Check ngrok interface: http://localhost:4040")
    print(f"5. ⏰ Wait for webhook to update gift status")
    
    # Step 4: Wait and check for webhook
    print(f"\n⏳ Step 4: Waiting for webhook (60 seconds)...")
    print(f"Complete the payment now and watch for webhook activity...")
    
    # Monitor for 60 seconds
    for i in range(12):  # 12 * 5 seconds = 60 seconds
        time.sleep(5)
        
        # Check gift status
        try:
            db = SessionLocal()
            try:
                gift = db.query(models.Gift).filter(models.Gift.id == UUID(gift_id)).first()
                if gift and gift.payment_status == "completed":
                    print(f"\n✅ SUCCESS! Gift status updated to 'completed'")
                    print(f"🎉 Webhook processing worked!")
                    print(f"💳 Payment Intent: {gift.payment_intent_id}")
                    return True
                else:
                    print(f".", end="", flush=True)
            finally:
                db.close()
        except:
            print(f".", end="", flush=True)
    
    print(f"\n⏰ Timeout reached. Checking final status...")
    
    # Final status check
    try:
        db = SessionLocal()
        try:
            gift = db.query(models.Gift).filter(models.Gift.id == UUID(gift_id)).first()
            if gift:
                print(f"📊 Final status: {gift.payment_status}")
                if gift.payment_status == "completed":
                    print(f"✅ Payment processed successfully!")
                    return True
                else:
                    print(f"❌ Payment not completed. Status still: {gift.payment_status}")
                    return False
            else:
                print(f"❌ Gift not found")
                return False
        finally:
            db.close()
    except Exception as e:
        print(f"❌ Error in final check: {str(e)}")
        return False

def check_webhook_setup():
    """Check if webhook is properly configured"""
    print(f"\n🔧 Webhook Setup Check")
    print("-" * 40)
    
    # Check ngrok
    try:
        response = requests.get("http://localhost:4040/api/tunnels", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('tunnels'):
                tunnel = data['tunnels'][0]
                webhook_url = f"{tunnel['public_url']}/webhook/"
                print(f"✅ ngrok running: {webhook_url}")
                
                print(f"\n🔧 Stripe Configuration Needed:")
                print(f"1. Go to: https://dashboard.stripe.com/webhooks")
                print(f"2. Add endpoint: {webhook_url}")
                print(f"3. Select events: checkout.session.completed")
                print(f"4. Copy webhook secret to .env file")
                
                return webhook_url
            else:
                print(f"❌ No ngrok tunnels found")
                return None
        else:
            print(f"❌ ngrok not responding")
            return None
    except:
        print(f"❌ ngrok not running")
        return None

if __name__ == "__main__":
    print("🚀 Complete Payment Flow Test")
    print("=" * 60)
    
    # Check webhook setup first
    webhook_url = check_webhook_setup()
    
    if not webhook_url:
        print("\n❌ Please start ngrok first: ngrok http 8000")
        exit(1)
    
    print(f"\n🎯 Make sure Stripe webhook is configured:")
    print(f"   URL: {webhook_url}")
    print(f"   Events: checkout.session.completed")
    print(f"   Secret: Updated in .env file")
    
    input(f"\nPress Enter when Stripe webhook is configured...")
    
    # Run the complete flow test
    success = test_complete_payment_flow()
    
    print(f"\n" + "=" * 60)
    print(f"🏁 FINAL RESULT")
    print(f"=" * 60)
    
    if success:
        print(f"✅ COMPLETE FLOW: WORKING")
        print(f"✅ Gift creation: WORKING")
        print(f"✅ Stripe checkout: WORKING")
        print(f"✅ Webhook processing: WORKING")
        print(f"✅ Gift status update: WORKING")
        print(f"\n🎉 Your webhook integration is fully functional!")
        print(f"Gifts will now automatically appear in child portfolios after payment!")
    else:
        print(f"❌ COMPLETE FLOW: FAILED")
        print(f"\n🔧 Troubleshooting:")
        print(f"1. Check Stripe webhook configuration")
        print(f"2. Verify webhook secret in .env file")
        print(f"3. Check backend logs for webhook errors")
        print(f"4. Ensure ngrok tunnel is stable")
    
    print(f"\n🧪 Test completed!")
