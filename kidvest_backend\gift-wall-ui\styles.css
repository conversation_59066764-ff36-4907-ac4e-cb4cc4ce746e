:root {
    --primary-color: #4361ee;
    --primary-light: #edf2ff;
    --secondary-color: #3a0ca3;
    --success-color: #4cc9a0;
    --danger-color: #ef476f;
    --warning-color: #ffd166;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --gray-color: #6c757d;
    --light-gray: #e9ecef;
    --border-color: #dee2e6;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #f5f7fb;
}

.app-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 30px;
}

.logo h1 {
    color: var(--primary-color);
    font-size: 24px;
}

/* Profile Container */
.profile-container {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 30px;
    margin-bottom: 30px;
    text-align: center;
}

.profile-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.profile-name {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 5px;
}

.profile-handle {
    color: var(--gray-color);
    font-size: 16px;
    margin-bottom: 15px;
}

.profile-bio {
    font-size: 18px;
    max-width: 600px;
    margin: 0 auto 20px;
}

.profile-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 20px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
}

.stat-label {
    color: var(--gray-color);
    font-size: 14px;
}

/* Gifts Container */
.gifts-container {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 30px;
    margin-bottom: 30px;
}

.gifts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.gifts-header h2 {
    font-size: 24px;
    font-weight: 600;
}

.gifts-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.gift-card {
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    padding: 20px;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.gift-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow);
}

.gift-amount {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.gift-message {
    margin-bottom: 15px;
    font-style: italic;
}

.gift-from {
    color: var(--gray-color);
    font-size: 14px;
}

.gift-date {
    color: var(--gray-color);
    font-size: 12px;
    margin-top: 10px;
}

.empty-gifts {
    text-align: center;
    padding: 50px 0;
    color: var(--gray-color);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: white;
    border-radius: var(--border-radius);
    width: 100%;
    max-width: 500px;
    box-shadow: var(--box-shadow);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: 20px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--gray-color);
}

.modal-body {
    padding: 20px;
}

/* Form */
.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

input[type="text"],
input[type="email"],
input[type="number"],
textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="number"]:focus,
textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

/* Gift Form Header */
.gift-form-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.gift-form-header .profile-avatar {
    width: 50px;
    height: 50px;
    font-size: 20px;
    margin-bottom: 0;
    margin-right: 15px;
}

.gift-form-header .profile-info {
    flex: 1;
}

.gift-form-header .profile-name {
    font-size: 18px;
    margin-bottom: 0;
}

.gift-form-header .profile-handle {
    font-size: 14px;
    margin-bottom: 0;
}

.gift-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 8px 16px;
    font-weight: 500;
    cursor: pointer;
}

/* Message Group */
.message-group textarea {
    border: none;
    border-bottom: 1px solid var(--border-color);
    border-radius: 0;
    padding: 10px 0;
    resize: none;
}

.message-group textarea:focus {
    box-shadow: none;
    border-color: var(--primary-color);
}

/* Amount Group */
.amount-group {
    display: flex;
    align-items: center;
}

.amount-group label {
    margin-bottom: 0;
    margin-right: 10px;
    width: 80px;
}

.amount-group input {
    flex: 1;
}

.hidden {
    display: none;
}

small {
    display: block;
    color: var(--gray-color);
    font-size: 12px;
    margin-top: 5px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox-label input {
    margin-right: 10px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

/* Buttons */
.btn {
    padding: 10px 16px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
}

.btn-secondary {
    background-color: var(--light-gray);
    color: var(--dark-color);
}

.btn-secondary:hover {
    background-color: var(--border-color);
}

/* Toast */
.toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    transform: translateY(100px);
    opacity: 0;
    transition: var(--transition);
    z-index: 1001;
}

.toast.active {
    transform: translateY(0);
    opacity: 1;
}

.toast-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.close-toast {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--gray-color);
    margin-left: 15px;
}

/* Loading */
.loading {
    text-align: center;
    padding: 20px;
    color: var(--gray-color);
}

/* Responsive */
@media (max-width: 768px) {
    .gifts-list {
        grid-template-columns: 1fr;
    }

    .profile-stats {
        flex-direction: column;
        gap: 15px;
    }

    .amount-options {
        grid-template-columns: repeat(2, 1fr);
    }
}
