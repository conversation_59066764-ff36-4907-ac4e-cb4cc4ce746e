from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import os
from dotenv import load_dotenv
from app.database import Base

# Load environment variables
load_dotenv()

# Get database connection details
DATABASE_URL = os.getenv("DATABASE_URL")

def reset_database():
    """Reset the database by dropping all tables and recreating them"""
    print("=== Database Reset Script ===")
    
    # Create engine
    engine = create_engine(DATABASE_URL)
    
    # Create a session
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Drop all tables
        print("Dropping all tables...")
        Base.metadata.drop_all(engine)
        print("All tables dropped successfully")
        
        # Create all tables
        print("Creating all tables...")
        Base.metadata.create_all(engine)
        print("All tables created successfully")
        
        print("=== Database reset completed successfully! ===")
    except Exception as e:
        print(f"Error during database reset: {e}")
        import traceback
        traceback.print_exc()
    finally:
        session.close()

if __name__ == "__main__":
    # Ask for confirmation
    confirm = input("This will DELETE ALL DATA in the database. Are you sure? (y/n): ")
    if confirm.lower() == 'y':
        reset_database()
        print("\nDone.")
    else:
        print("Operation cancelled.")
