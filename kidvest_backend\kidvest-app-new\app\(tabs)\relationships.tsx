import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { relationshipsAPI } from '@/services/api';
import { useRouter } from 'expo-router';

interface Relationship {
  id: string;
  from_user_id: string;
  to_child_id: string;
  relationship_type: string;
  status: string;
  description?: string;
  created_at: string;
  from_user_name?: string;
  from_user_email?: string;
  child_name?: string;
  child_handle?: string;
  requested_by_name?: string;
}

interface RelationshipListResponse {
  relationships: Relationship[];
  total_count: number;
  pending_count: number;
  active_count: number;
}

export default function RelationshipsTabScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const [relationships, setRelationships] = useState<RelationshipListResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'all' | 'pending' | 'active'>('all');

  const loadRelationships = async () => {
    try {
      const status = selectedTab === 'all' ? undefined : selectedTab;
      const data = await relationshipsAPI.getUserRelationships(status);
      setRelationships(data);
    } catch (error) {
      console.error('Error loading relationships:', error);
      Alert.alert('Error', 'Failed to load relationships');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadRelationships();
  }, [selectedTab]);

  const onRefresh = () => {
    setRefreshing(true);
    loadRelationships();
  };

  const handleApproveRelationship = async (relationshipId: string) => {
    try {
      await relationshipsAPI.updateRelationshipStatus(relationshipId, 'active');
      Alert.alert('Success', 'Relationship approved successfully');
      loadRelationships();
    } catch (error) {
      console.error('Error approving relationship:', error);
      Alert.alert('Error', 'Failed to approve relationship');
    }
  };

  const handleDeclineRelationship = async (relationshipId: string) => {
    try {
      await relationshipsAPI.updateRelationshipStatus(relationshipId, 'declined');
      Alert.alert('Success', 'Relationship declined');
      loadRelationships();
    } catch (error) {
      console.error('Error declining relationship:', error);
      Alert.alert('Error', 'Failed to decline relationship');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return '#4CAF50';
      case 'pending':
        return '#FF9800';
      case 'declined':
        return '#F44336';
      default:
        return Colors[colorScheme].text;
    }
  };

  const getRelationshipIcon = (type: string) => {
    switch (type) {
      case 'parent':
        return 'user-friends';
      case 'grandparent':
        return 'heart';
      case 'aunt_uncle':
        return 'users';
      case 'sibling':
        return 'child';
      case 'cousin':
        return 'user';
      case 'family_friend':
        return 'handshake';
      case 'godparent':
        return 'star';
      default:
        return 'user';
    }
  };

  const formatRelationshipType = (type: string) => {
    return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const renderRelationship = (relationship: Relationship) => (
    <View key={relationship.id} style={[styles.relationshipCard, { backgroundColor: Colors[colorScheme].background }]}>
      <View style={styles.relationshipHeader}>
        <View style={styles.relationshipInfo}>
          <FontAwesome5 
            name={getRelationshipIcon(relationship.relationship_type)} 
            size={20} 
            color={Colors[colorScheme].primary} 
            style={styles.relationshipIcon}
          />
          <View style={styles.relationshipDetails}>
            <Text style={[styles.relationshipName, { color: Colors[colorScheme].text }]}>
              {relationship.from_user_name || relationship.from_user_email}
            </Text>
            <Text style={[styles.relationshipType, { color: Colors[colorScheme].tabIconDefault }]}>
              {formatRelationshipType(relationship.relationship_type)} of {relationship.child_name}
            </Text>
            {relationship.description && (
              <Text style={[styles.relationshipDescription, { color: Colors[colorScheme].tabIconDefault }]}>
                {relationship.description}
              </Text>
            )}
          </View>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(relationship.status) }]}>
          <Text style={styles.statusText}>{relationship.status.toUpperCase()}</Text>
        </View>
      </View>

      {relationship.status === 'pending' && (
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.actionButton, styles.approveButton]}
            onPress={() => handleApproveRelationship(relationship.id)}
          >
            <FontAwesome5 name="check" size={16} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>Approve</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, styles.declineButton]}
            onPress={() => handleDeclineRelationship(relationship.id)}
          >
            <FontAwesome5 name="times" size={16} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>Decline</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: Colors[colorScheme].background }]}>
        <ActivityIndicator size="large" color={Colors[colorScheme].primary} />
        <Text style={[styles.loadingText, { color: Colors[colorScheme].text }]}>Loading relationships...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: Colors[colorScheme].background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: Colors[colorScheme].text }]}>Relationships</Text>
        <Text style={[styles.subtitle, { color: Colors[colorScheme].tabIconDefault }]}>
          Manage connections with children
        </Text>
        
        <TouchableOpacity
          style={[styles.requestButton, { backgroundColor: Colors[colorScheme].primary }]}
          onPress={() => router.push('/relationships/request')}
        >
          <FontAwesome5 name="plus" size={16} color="#FFFFFF" />
          <Text style={styles.requestButtonText}>Request Connection</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.tabContainer}>
        {(['all', 'pending', 'active'] as const).map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[
              styles.tab,
              selectedTab === tab && { backgroundColor: Colors[colorScheme].primary },
            ]}
            onPress={() => setSelectedTab(tab)}
          >
            <Text
              style={[
                styles.tabText,
                { color: selectedTab === tab ? '#FFFFFF' : Colors[colorScheme].text },
              ]}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
              {relationships && tab !== 'all' && (
                <Text style={styles.tabCount}>
                  {' '}({tab === 'pending' ? relationships.pending_count : relationships.active_count})
                </Text>
              )}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {relationships && relationships.relationships.length > 0 ? (
          relationships.relationships.map(renderRelationship)
        ) : (
          <View style={styles.emptyState}>
            <FontAwesome5 name="users" size={48} color={Colors[colorScheme].tabIconDefault} />
            <Text style={[styles.emptyStateText, { color: Colors[colorScheme].text }]}>
              No relationships found
            </Text>
            <Text style={[styles.emptyStateSubtext, { color: Colors[colorScheme].tabIconDefault }]}>
              Relationships will appear here when users request to connect with your children
            </Text>
            <TouchableOpacity
              style={[styles.emptyActionButton, { backgroundColor: Colors[colorScheme].primary }]}
              onPress={() => router.push('/relationships/request')}
            >
              <FontAwesome5 name="plus" size={16} color="#FFFFFF" />
              <Text style={styles.emptyActionButtonText}>Request Connection</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 16,
  },
  requestButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  requestButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginHorizontal: 4,
    borderRadius: 8,
    alignItems: 'center',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  tabCount: {
    fontSize: 12,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  relationshipCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  relationshipHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  relationshipInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  relationshipIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  relationshipDetails: {
    flex: 1,
  },
  relationshipName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  relationshipType: {
    fontSize: 14,
    marginBottom: 2,
  },
  relationshipDescription: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 8,
    gap: 6,
  },
  approveButton: {
    backgroundColor: '#4CAF50',
  },
  declineButton: {
    backgroundColor: '#F44336',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  emptyActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    gap: 8,
  },
  emptyActionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
