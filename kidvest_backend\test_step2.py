import requests
import json
import uuid
import datetime

# Base URL for API
BASE_URL = "http://localhost:8000/api/onboarding"

# Use the session token from step 1
session_token = "36a59f55-6baf-47fb-96f5-031245dd08ff"

# Step 2: Identity verification
step2_data = {
    "dob": "1992-03-15",
    "phone_number": "************",
    "street_address": "789 Pine Street",
    "city": "Boston",
    "state": "MA",
    "postal_code": "02108",
    "country": "USA",
    "ssn": "***********"
}

print(f"Step 2: Identity verification with session token: {session_token}")
response = requests.post(f"{BASE_URL}/step2?session_token={session_token}", json=step2_data)
print(f"Status Code: {response.status_code}")
print(f"Response Text: {response.text}")
try:
    step2_response = response.json()
    print(f"Response JSON: {json.dumps(step2_response, indent=2)}")
except Exception as e:
    print(f"Error parsing JSON: {str(e)}")
