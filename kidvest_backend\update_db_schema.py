import os
import sys
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database connection details from environment variables
DATABASE_URL = os.getenv("DATABASE_URL")

# Parse the DATABASE_URL to get connection parameters
# Format: postgresql://username:password@host:port/database
url_parts = DATABASE_URL.replace("postgresql://", "").split("@")
credentials = url_parts[0].split(":")
host_port_db = url_parts[1].split("/")
host_port = host_port_db[0].split(":")

username = credentials[0]
password = credentials[1]
host = host_port[0]
port = host_port[1] if len(host_port) > 1 else "5432"
database = host_port_db[1]

def update_gifts_table():
    """Update gifts table schema to match the model"""
    try:
        # Connect to the database
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=password
        )

        # Create a cursor
        cur = conn.cursor()

        # Check if kid_profile_id column exists
        cur.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'gifts' AND column_name = 'kid_profile_id'
        """)

        kid_profile_id_exists = cur.fetchone() is not None

        if not kid_profile_id_exists:
            print("Adding kid_profile_id column to gifts table...")

            # Add the column
            cur.execute("""
                ALTER TABLE gifts
                ADD COLUMN kid_profile_id UUID REFERENCES kid_profiles(id)
            """)

            print("kid_profile_id column added successfully!")
        else:
            print("kid_profile_id column already exists in gifts table.")

        # Check if from_email column exists
        cur.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'gifts' AND column_name = 'from_email'
        """)

        from_email_exists = cur.fetchone() is not None

        if not from_email_exists:
            print("Adding from_email column to gifts table...")

            # Add the column
            cur.execute("""
                ALTER TABLE gifts
                ADD COLUMN from_email VARCHAR
            """)

            print("from_email column added successfully!")
        else:
            print("from_email column already exists in gifts table.")

        # Check if is_anonymous column exists
        cur.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'gifts' AND column_name = 'is_anonymous'
        """)

        is_anonymous_exists = cur.fetchone() is not None

        if not is_anonymous_exists:
            print("Adding is_anonymous column to gifts table...")

            # Add the column
            cur.execute("""
                ALTER TABLE gifts
                ADD COLUMN is_anonymous BOOLEAN DEFAULT FALSE
            """)

            print("is_anonymous column added successfully!")
        else:
            print("is_anonymous column already exists in gifts table.")

        # Check if payment_intent_id column exists
        cur.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'gifts' AND column_name = 'payment_intent_id'
        """)

        payment_intent_id_exists = cur.fetchone() is not None

        if not payment_intent_id_exists:
            print("Adding payment_intent_id column to gifts table...")

            # Add the column
            cur.execute("""
                ALTER TABLE gifts
                ADD COLUMN payment_intent_id VARCHAR
            """)

            print("payment_intent_id column added successfully!")
        else:
            print("payment_intent_id column already exists in gifts table.")

        # Check if checkout_session_id column exists
        cur.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'gifts' AND column_name = 'checkout_session_id'
        """)

        checkout_session_id_exists = cur.fetchone() is not None

        if not checkout_session_id_exists:
            print("Adding checkout_session_id column to gifts table...")

            # Add the column
            cur.execute("""
                ALTER TABLE gifts
                ADD COLUMN checkout_session_id VARCHAR
            """)

            print("checkout_session_id column added successfully!")
        else:
            print("checkout_session_id column already exists in gifts table.")

        # Check if updated_at column exists
        cur.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'gifts' AND column_name = 'updated_at'
        """)

        updated_at_exists = cur.fetchone() is not None

        if not updated_at_exists:
            print("Adding updated_at column to gifts table...")

            # Add the column
            cur.execute("""
                ALTER TABLE gifts
                ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            """)

            print("updated_at column added successfully!")
        else:
            print("updated_at column already exists in gifts table.")

        # Check if invested column exists
        cur.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'gifts' AND column_name = 'invested'
        """)

        invested_exists = cur.fetchone() is not None

        if not invested_exists:
            print("Adding invested column to gifts table...")

            # Add the column
            cur.execute("""
                ALTER TABLE gifts
                ADD COLUMN invested BOOLEAN DEFAULT FALSE
            """)

            print("invested column added successfully!")
        else:
            print("invested column already exists in gifts table.")

        # Commit the transaction
        conn.commit()

        # Close the cursor and connection
        cur.close()
        conn.close()

        return True

    except Exception as e:
        print(f"Error updating database schema: {str(e)}")
        return False

if __name__ == "__main__":
    print("=== Updating Database Schema ===")

    success = update_gifts_table()

    if success:
        print("Database schema updated successfully!")
    else:
        print("Failed to update database schema.")

    print("\nDone.")
