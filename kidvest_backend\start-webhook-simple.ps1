# Simple KidVest Backend with Webhook Support
Write-Host "Starting KidVest Backend with Webhook Support..." -ForegroundColor Green

# Set execution policy
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process -Force

# Check if virtual environment exists
if (-not (Test-Path ".\venv\Scripts\activate.ps1")) {
    Write-Host "Error: Virtual environment not found. Please run setup first." -ForegroundColor Red
    exit 1
}

# Activate virtual environment
Write-Host "Activating virtual environment..." -ForegroundColor Yellow
& ".\venv\Scripts\activate.ps1"

# Function to check if ngrok is installed
function Test-NgrokInstalled {
    try {
        $null = Get-Command ngrok -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Function to start ngrok
function Start-NgrokTunnel {
    Write-Host "Starting ngrok tunnel..." -ForegroundColor Cyan
    
    if (-not (Test-NgrokInstalled)) {
        Write-Host "Error: ngrok not found. Please install ngrok first:" -ForegroundColor Red
        Write-Host "1. Download from https://ngrok.com/download" -ForegroundColor Yellow
        Write-Host "2. Extract to a folder in your PATH" -ForegroundColor Yellow
        Write-Host "3. Run 'ngrok authtoken YOUR_TOKEN'" -ForegroundColor Yellow
        return $false
    }
    
    # Kill existing ngrok processes
    Get-Process -Name "ngrok" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 2
    
    # Start ngrok
    Start-Process -FilePath "ngrok" -ArgumentList @("http", "8000") -WindowStyle Minimized
    
    # Wait for ngrok to start
    Write-Host "Waiting for ngrok to start..." -ForegroundColor Gray
    Start-Sleep -Seconds 8
    
    # Get ngrok URL
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:4040/api/tunnels" -Method Get -TimeoutSec 10
        $publicUrl = $response.tunnels[0].public_url
        
        if ($publicUrl) {
            Write-Host "Success: ngrok tunnel started!" -ForegroundColor Green
            Write-Host "Public URL: $publicUrl" -ForegroundColor Cyan
            
            # Save webhook URL
            $webhookUrl = "$publicUrl/webhook/"
            $webhookUrl | Out-File -FilePath "webhook_url.txt" -Encoding UTF8
            
            Write-Host ""
            Write-Host "STRIPE WEBHOOK SETUP:" -ForegroundColor Yellow
            Write-Host "1. Go to: https://dashboard.stripe.com/webhooks" -ForegroundColor White
            Write-Host "2. Add endpoint: $webhookUrl" -ForegroundColor Cyan
            Write-Host "3. Select events: checkout.session.completed" -ForegroundColor White
            Write-Host "4. Copy webhook secret to .env file" -ForegroundColor White
            Write-Host ""
            
            return $publicUrl
        } else {
            Write-Host "Error: Failed to get ngrok URL" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "Error: Cannot connect to ngrok API" -ForegroundColor Red
        return $false
    }
}

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "Creating .env template..." -ForegroundColor Yellow
    
    $envTemplate = "DATABASE_URL=postgresql://username:password@localhost/kidvest`n"
    $envTemplate += "STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here`n"
    $envTemplate += "STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here`n"
    $envTemplate += "ALPACA_API_KEY=your_alpaca_api_key_here`n"
    $envTemplate += "ALPACA_SECRET_KEY=your_alpaca_secret_key_here`n"
    $envTemplate += "ALPACA_BASE_URL=https://paper-api.alpaca.markets"
    
    $envTemplate | Out-File -FilePath ".env" -Encoding UTF8
    Write-Host "Template .env file created. Please update with your values." -ForegroundColor Yellow
}

# Start ngrok tunnel
Write-Host ""
Write-Host "Setting up ngrok tunnel..." -ForegroundColor Cyan
$ngrokUrl = Start-NgrokTunnel

# Display server information
Write-Host ""
Write-Host "Server Information:" -ForegroundColor Yellow
Write-Host "API Documentation: http://localhost:8000/docs" -ForegroundColor White
Write-Host "Gift Wall UI: http://localhost:8000/wall/test-child-165125" -ForegroundColor White
Write-Host "React Native App: http://localhost:8081" -ForegroundColor White

if ($ngrokUrl) {
    Write-Host "Public Webhook URL: $ngrokUrl/webhook/" -ForegroundColor White
    Write-Host "ngrok Web Interface: http://localhost:4040" -ForegroundColor White
} else {
    Write-Host "Warning: ngrok tunnel failed. Webhooks will not work." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Starting backend server..." -ForegroundColor Green

# Set PYTHONPATH
$env:PYTHONPATH = "$PSScriptRoot"

# Start the backend server
try {
    uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
} catch {
    Write-Host "Error starting backend server: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting:" -ForegroundColor Yellow
    Write-Host "1. Make sure Python and uvicorn are installed" -ForegroundColor Gray
    Write-Host "2. Check if port 8000 is available" -ForegroundColor Gray
    Write-Host "3. Verify virtual environment is activated" -ForegroundColor Gray
}
