from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, EmailStr
from typing import Optional, List
import sqlite3
import os
import uuid
from datetime import datetime, timedelta
import hashlib
import jwt
from fastapi.security import OA<PERSON>2<PERSON>asswordBearer, OAuth2PasswordRequestForm

# Create FastAPI app
app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# SQLite setup
DB_PATH = "test.db"

def get_db():
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    try:
        yield conn
    finally:
        conn.close()

# Create tables if they don't exist
def init_db():
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Create users table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        hashed_password TEXT NOT NULL,
        user_type TEXT NOT NULL,
        phone_number TEXT,
        address TEXT,
        city TEXT,
        state TEXT,
        postal_code TEXT,
        country TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    conn.commit()
    conn.close()

# Initialize database
init_db()

# JWT settings
SECRET_KEY = "your-secret-key"  # In production, use a secure key
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/token")

# Models
class UserType(str):
    parent = "parent"
    child = "child"

class UserBase(BaseModel):
    name: str
    email: EmailStr
    user_type: str

class UserCreate(UserBase):
    password: str
    phone_number: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None

class UserResponse(UserBase):
    id: str
    created_at: str
    is_active: bool
    phone_number: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None

class Token(BaseModel):
    access_token: str
    token_type: str

# Helper functions
def hash_password(password: str) -> str:
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return hash_password(plain_password) == hashed_password

def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def get_user_by_email(conn, email: str):
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users WHERE email = ?", (email,))
    user = cursor.fetchone()
    return user

def authenticate_user(conn, email: str, password: str):
    user = get_user_by_email(conn, email)
    if not user:
        return False
    if not verify_password(password, user["hashed_password"]):
        return False
    return user

async def get_current_user(token: str = Depends(oauth2_scheme), conn = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
    except jwt.PyJWTError:
        raise credentials_exception
    user = get_user_by_email(conn, email)
    if user is None:
        raise credentials_exception
    return user

# Routes
@app.get("/")
def read_root():
    return {"message": "Welcome to KidVest Backend!"}

@app.post("/api/register", response_model=UserResponse)
def register_user(user_data: UserCreate, conn = Depends(get_db)):
    print(f"\n=== Registration Request ===\nData: {user_data}")
    
    # Check if user already exists
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users WHERE email = ?", (user_data.email,))
    existing_user = cursor.fetchone()
    
    if existing_user:
        print(f"Email already registered: {user_data.email}")
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Create new user with hashed password
    user_id = str(uuid.uuid4())
    hashed_password = hash_password(user_data.password)
    created_at = datetime.utcnow().isoformat()
    
    try:
        cursor.execute(
            """
            INSERT INTO users (
                id, name, email, hashed_password, user_type, 
                phone_number, address, city, state, postal_code, country, 
                is_active, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
            (
                user_id, user_data.name, user_data.email, hashed_password, user_data.user_type,
                user_data.phone_number, user_data.address, user_data.city, user_data.state, 
                user_data.postal_code, user_data.country, 1, created_at
            )
        )
        conn.commit()
        
        # Return the created user
        return {
            "id": user_id,
            "name": user_data.name,
            "email": user_data.email,
            "user_type": user_data.user_type,
            "phone_number": user_data.phone_number,
            "address": user_data.address,
            "city": user_data.city,
            "state": user_data.state,
            "postal_code": user_data.postal_code,
            "country": user_data.country,
            "is_active": True,
            "created_at": created_at
        }
    except Exception as e:
        conn.rollback()
        print(f"Error registering user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error registering user: {str(e)}")

@app.post("/api/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), conn = Depends(get_db)):
    user = authenticate_user(conn, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=401,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["email"]}, expires_delta=access_token_expires
    )
    
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/api/users/me", response_model=UserResponse)
async def read_users_me(current_user = Depends(get_current_user)):
    return {
        "id": current_user["id"],
        "name": current_user["name"],
        "email": current_user["email"],
        "user_type": current_user["user_type"],
        "phone_number": current_user["phone_number"],
        "address": current_user["address"],
        "city": current_user["city"],
        "state": current_user["state"],
        "postal_code": current_user["postal_code"],
        "country": current_user["country"],
        "is_active": bool(current_user["is_active"]),
        "created_at": current_user["created_at"]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
