import os
import stripe
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_stripe_checkout():
    """Test creating a Stripe checkout session"""
    print("Testing Stripe checkout session creation...")
    
    # Set Stripe API key
    stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
    print(f"Using Stripe API key: {stripe.api_key[:5]}...")
    
    try:
        # Create a checkout session
        checkout_session = stripe.checkout.Session.create(
            payment_method_types=["card"],
            line_items=[{
                "price_data": {
                    "currency": "usd",
                    "unit_amount": 5000,  # $50.00
                    "product_data": {
                        "name": "Test Gift",
                        "description": "Test gift description"
                    },
                },
                "quantity": 1,
            }],
            mode="payment",
            success_url="http://localhost:8082/success",
            cancel_url="http://localhost:8082/cancel",
            metadata={"test": "true"},
        )
        
        print(f"Checkout session created successfully!")
        print(f"Session ID: {checkout_session.id}")
        print(f"Checkout URL: {checkout_session.url}")
        
        return checkout_session
    except Exception as e:
        print(f"Error creating checkout session: {str(e)}")
        return None

if __name__ == "__main__":
    test_stripe_checkout()
