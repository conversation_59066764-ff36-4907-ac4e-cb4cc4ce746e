t=2025-04-17T20:36:35-0500 lvl=info msg="no configuration paths supplied"
t=2025-04-17T20:36:35-0500 lvl=info msg="using configuration at default config path" path=C:\\Users\\<USER>\\AppData\\Local/ngrok/ngrok.yml
t=2025-04-17T20:36:35-0500 lvl=info msg="open config file" path=C:\\Users\\<USER>\\AppData\\Local\\ngrok\\ngrok.yml err=nil
t=2025-04-17T20:36:35-0500 lvl=info msg="starting web service" obj=web addr=127.0.0.1:4040 allow_hosts=[]
t=2025-04-17T20:36:35-0500 lvl=info msg="update available" obj=updater
t=2025-04-17T20:36:35-0500 lvl=info msg="client session established" obj=tunnels.session
t=2025-04-17T20:36:35-0500 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-04-17T20:36:35-0500 lvl=info msg="started tunnel" obj=tunnels name=command_line addr=http://localhost:8000 url=https://c696-2600-1700-70d0-b680-b9cd-61c0-a242-b3e3.ngrok-free.app
t=2025-04-18T04:17:36-0500 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=21d8c56fe224 clientid=d44a767035688f1ac05f20b78057cc19
t=2025-04-18T04:17:36-0500 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=97b325e92983 err="write tcp [2600:1700:70d0:b680:b9cd:61c0:a242:b3e3]:54375->[2600:1f16:d83:1201::6e74:4]:443: wsasend: An existing connection was forcibly closed by the remote host."
t=2025-04-18T04:17:38-0500 lvl=info msg="client session established" obj=tunnels.session
t=2025-04-18T04:17:38-0500 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-04-18T04:17:40-0500 lvl=info msg="update available" obj=updater
t=2025-04-18T23:58:06-0500 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=41b05bd2d66f clientid=d44a767035688f1ac05f20b78057cc19
t=2025-04-18T23:58:08-0500 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=97b325e92983 err="session closed"
t=2025-04-18T23:58:08-0500 lvl=warn msg="failed to check for update" obj=updater err="Post \"https://update.equinox.io/check\": dial tcp: lookup update.equinox.io: no such host"
t=2025-04-18T23:58:08-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-18T23:58:09-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-18T23:58:10-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-18T23:58:12-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to send authentication request: failed to fetch CRL. errors encountered: Get \"http://crl.ngrok-agent.com/ngrok.crl\": dial tcp: lookup crl.ngrok-agent.com: no such host"
t=2025-04-18T23:58:26-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp [2600:1f16:d83:1200::6e74:3]:443: i/o timeout"
t=2025-04-18T23:58:44-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp ************:443: i/o timeout"
t=2025-04-18T23:59:10-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp ************:443: i/o timeout"
t=2025-04-18T23:59:40-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:00:10-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:00:40-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:01:10-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:01:40-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:02:10-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:02:40-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:03:10-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:03:40-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:04:10-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:04:40-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:05:10-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:05:40-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:06:10-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:06:40-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:07:10-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:07:40-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:08:10-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:08:40-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:09:10-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:09:40-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:10:10-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:10:40-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:11:10-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:11:40-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-04-19T00:12:10-0500 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
