<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KidVest - Launcher</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 50px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .card {
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #4a6cf7;
            color: white;
            font-weight: bold;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }
        .btn-primary {
            background-color: #4a6cf7;
            border-color: #4a6cf7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h2 class="mb-0">KidVest Launcher</h2>
            </div>
            <div class="card-body">
                <p class="lead">Welcome to the KidVest Launcher. Please select a UI to launch:</p>

                <div class="list-group mt-4">
                    <a href="http://localhost:8081" target="_blank" class="list-group-item list-group-item-action">
                        <h5 class="mb-1">Authentication UI</h5>
                        <p class="mb-1">Login or register to access the KidVest platform</p>
                    </a>
                    <a href="http://localhost:8082" target="_blank" class="list-group-item list-group-item-action">
                        <h5 class="mb-1">Onboarding UI</h5>
                        <p class="mb-1">Complete the KYC onboarding process</p>
                    </a>
                    <a href="http://localhost:8083" target="_blank" class="list-group-item list-group-item-action">
                        <h5 class="mb-1">Child Profile UI</h5>
                        <p class="mb-1">Manage your child profiles</p>
                    </a>
                    <a href="http://localhost:8084" target="_blank" class="list-group-item list-group-item-action">
                        <h5 class="mb-1">Gift Wall UI</h5>
                        <p class="mb-1">View and share gift walls</p>
                    </a>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="mb-0">Start Servers</h2>
            </div>
            <div class="card-body">
                <p>There are two ways to start the servers:</p>

                <div class="mb-4">
                    <h5>Option 1: Use the start_servers.py script</h5>
                    <p>This script will start all the servers for you:</p>
                    <pre class="bg-light p-3 rounded">python start_servers.py</pre>
                </div>

                <div class="mb-4">
                    <h5>Option 2: Start servers manually</h5>
                    <p>Run the following commands in separate terminals:</p>

                    <div class="mb-3">
                        <h6>Backend Server</h6>
                        <pre class="bg-light p-3 rounded">python -m uvicorn app.main:app --reload --log-level debug</pre>
                    </div>

                    <div class="mb-3">
                        <h6>Authentication UI Server</h6>
                        <pre class="bg-light p-3 rounded">cd auth-ui && python server.py</pre>
                    </div>

                    <div class="mb-3">
                        <h6>Onboarding UI Server</h6>
                        <pre class="bg-light p-3 rounded">cd onboarding-ui && python server.py</pre>
                    </div>

                    <div class="mb-3">
                        <h6>Child Profile UI Server</h6>
                        <pre class="bg-light p-3 rounded">cd child-profile-ui && python server.py</pre>
                    </div>

                    <div class="mb-3">
                        <h6>Gift Wall UI Server</h6>
                        <pre class="bg-light p-3 rounded">cd gift-wall-ui && python server.py</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
