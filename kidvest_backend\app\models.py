from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON>ole<PERSON>, DateT<PERSON>, Enum, Foreign<PERSON>ey, Float, Text, Integer
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, timezone
from .database import Base
from sqlalchemy.orm import relationship

# Helper function for timezone-aware UTC timestamps
def utc_now():
    return datetime.now(timezone.utc)

import enum

# Define user types
class UserType(str, enum.Enum):
    parent = "parent"  # Changed from "custodian" to "parent" for clarity
    child = "child"    # Changed from "kid" to "child" for consistency

# Define relationship types
class RelationshipType(str, enum.Enum):
    parent = "parent"
    grandparent = "grandparent"
    aunt_uncle = "aunt_uncle"
    sibling = "sibling"
    cousin = "cousin"
    family_friend = "family_friend"
    godparent = "godparent"
    other = "other"

# Define relationship status
class RelationshipStatus(str, enum.Enum):
    pending = "pending"
    active = "active"
    declined = "declined"
    blocked = "blocked"

# User model (Parent)
class User(Base):
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    email = Column(String, unique=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    user_type = Column(Enum(UserType), nullable=False)
    created_at = Column(DateTime, default=utc_now)
    is_active = Column(Boolean, default=True)

    # Profile information
    phone_number = Column(String, nullable=True)
    address = Column(String, nullable=True)
    city = Column(String, nullable=True)
    state = Column(String, nullable=True)
    postal_code = Column(String, nullable=True)
    country = Column(String, nullable=True)

    # Relationships
    children = relationship("ChildProfile", back_populates="parent")
    broker_accounts = relationship("BrokerAccount", back_populates="user")

# Child Profile model (replaces KidProfile)
class ChildProfile(Base):
    __tablename__ = "child_profiles"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    parent_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    name = Column(String, nullable=False)
    age = Column(Integer, nullable=True)
    handle = Column(String, unique=True, nullable=False)  # For social sharing
    is_public = Column(Boolean, default=True)
    avatar = Column(String, nullable=True)
    bio = Column(String, nullable=True)
    dob = Column(String, nullable=True)  # Store as ISO format string (YYYY-MM-DD)
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    # Relationships
    parent = relationship("User", back_populates="children")
    gifts = relationship("Gift", back_populates="child_profile")
    investments = relationship("Investment", back_populates="child_profile")

# Gift model
class Gift(Base):
    __tablename__ = "gifts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    child_profile_id = Column(UUID(as_uuid=True), ForeignKey("child_profiles.id"), nullable=False)
    from_name = Column(String, nullable=True)
    from_email = Column(String, nullable=True)
    amount_usd = Column(Float, nullable=False)
    message = Column(Text, nullable=True)
    payment_status = Column(String, default="pending")  # pending, completed, failed
    payment_intent_id = Column(String, nullable=True)
    checkout_session_id = Column(String, nullable=True)
    is_anonymous = Column(Boolean, default=False)
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    # Relationships
    child_profile = relationship("ChildProfile", back_populates="gifts")
    investments = relationship("Investment", back_populates="gift")

# Investment model (new)
class Investment(Base):
    __tablename__ = "investments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    child_profile_id = Column(UUID(as_uuid=True), ForeignKey("child_profiles.id"), nullable=False)
    gift_id = Column(UUID(as_uuid=True), ForeignKey("gifts.id"), nullable=True)
    amount_usd = Column(Float, nullable=False)
    symbol = Column(String, nullable=False)  # Stock symbol
    shares = Column(Float, nullable=False)
    purchase_price = Column(Float, nullable=False)
    status = Column(String, default="pending")  # pending, completed, failed
    transaction_id = Column(String, nullable=True)  # Broker transaction ID
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    # Relationships
    child_profile = relationship("ChildProfile", back_populates="investments")
    gift = relationship("Gift", back_populates="investments")

# BrokerAccount model
class BrokerAccount(Base):
    __tablename__ = "broker_accounts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    broker_type = Column(String, nullable=False)  # e.g. "alpaca", "drivewealth"
    external_account_id = Column(String, nullable=True)
    status = Column(String, nullable=False, default="pending")  # pending, active, rejected
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    user = relationship("User", back_populates="broker_accounts")

# OnboardingSession model for multi-step onboarding
class OnboardingSession(Base):
    __tablename__ = "onboarding_sessions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_token = Column(String, unique=True, nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)  # Can be null until user is created
    current_step = Column(Integer, default=1)

    # Step completion timestamps
    step1_completed_at = Column(DateTime, nullable=True)
    step2_completed_at = Column(DateTime, nullable=True)
    step3_completed_at = Column(DateTime, nullable=True)
    step4_completed_at = Column(DateTime, nullable=True)

    # Step data (stored as JSON)
    step1_data = Column(Text, nullable=True)  # JSON string
    step2_data = Column(Text, nullable=True)  # JSON string
    step3_data = Column(Text, nullable=True)  # JSON string
    step4_data = Column(Text, nullable=True)  # JSON string

    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)
    expires_at = Column(DateTime)  # Session expiration

    # Relationships
    user = relationship("User", backref="onboarding_sessions")

# Relationship model for user-child connections
class Relationship(Base):
    __tablename__ = "relationships"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    from_user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    to_child_id = Column(UUID(as_uuid=True), ForeignKey("child_profiles.id"), nullable=False)
    relationship_type = Column(Enum(RelationshipType), nullable=False)
    status = Column(Enum(RelationshipStatus), default=RelationshipStatus.pending)

    # Optional fields for additional context
    description = Column(String, nullable=True)  # Custom description of relationship
    requested_by_user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)  # Who initiated the request

    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)
    approved_at = Column(DateTime, nullable=True)  # When relationship was approved

    # Relationships
    from_user = relationship("User", foreign_keys=[from_user_id], backref="sent_relationships")
    to_child = relationship("ChildProfile", backref="relationships")
    requested_by = relationship("User", foreign_keys=[requested_by_user_id], backref="requested_relationships")
