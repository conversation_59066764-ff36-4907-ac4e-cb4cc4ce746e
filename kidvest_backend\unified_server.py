import os
import http.server
import socketserver
import json
from urllib.parse import urlparse, parse_qs

PORT = 8080
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# Define UI directories
UI_DIRS = {
    "auth": os.path.join(BASE_DIR, "auth-ui"),
    "onboarding": os.path.join(BASE_DIR, "onboarding-ui"),
    "child-profile": os.path.join(BASE_DIR, "child-profile-ui"),
    "gift-wall": os.path.join(BASE_DIR, "gift-wall-ui"),
    "dashboard": os.path.join(BASE_DIR, "dashboard-ui")
}

class UnifiedHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # Start with the base directory
        super().__init__(*args, directory=BASE_DIR, **kwargs)

    def log_message(self, format, *args):
        # Enhanced logging
        print(f"[{self.log_date_time_string()}] {format % args}")

    def do_GET(self):
        # Parse the URL
        parsed_url = urlparse(self.path)
        path = parsed_url.path

        print(f"Handling request for path: {path}")

        # Default to dashboard UI for root path
        if path == "/" or path == "":
            self.path = "/dashboard-ui/index.html"
            print(f"Root path detected, redirecting to: {self.path}")

        # Handle specific UI paths
        elif path.startswith("/auth"):
            self.directory = UI_DIRS["auth"]
            self.path = path.replace("/auth", "")
            if self.path == "":
                self.path = "/index.html"
            print(f"Auth path detected, directory: {self.directory}, new path: {self.path}")

        elif path.startswith("/onboarding"):
            self.directory = UI_DIRS["onboarding"]
            self.path = path.replace("/onboarding", "")
            if self.path == "":
                self.path = "/index.html"

        elif path.startswith("/child-profile"):
            self.directory = UI_DIRS["child-profile"]
            self.path = path.replace("/child-profile", "")
            if self.path == "":
                self.path = "/index.html"

        elif path.startswith("/gift-wall"):
            self.directory = UI_DIRS["gift-wall"]
            self.path = path.replace("/gift-wall", "")
            if self.path == "":
                self.path = "/index.html"

        elif path.startswith("/dashboard"):
            self.directory = UI_DIRS["dashboard"]
            self.path = path.replace("/dashboard", "")
            if self.path == "":
                self.path = "/index.html"

        # Handle wall URLs
        elif path.startswith("/wall/"):
            self.directory = UI_DIRS["gift-wall"]
            # Keep the original path for the gift wall UI to handle

        # Serve static files from their respective directories
        elif path.startswith("/auth-ui/"):
            self.directory = UI_DIRS["auth"]
            self.path = path.replace("/auth-ui", "")

        elif path.startswith("/onboarding-ui/"):
            self.directory = UI_DIRS["onboarding"]
            self.path = path.replace("/onboarding-ui", "")

        elif path.startswith("/child-profile-ui/"):
            self.directory = UI_DIRS["child-profile"]
            self.path = path.replace("/child-profile-ui", "")

        elif path.startswith("/gift-wall-ui/"):
            self.directory = UI_DIRS["gift-wall"]
            self.path = path.replace("/gift-wall-ui", "")

        elif path.startswith("/dashboard-ui/"):
            self.directory = UI_DIRS["dashboard"]
            self.path = path.replace("/dashboard-ui", "")

        # Call the parent class's do_GET method
        return super().do_GET()

    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()

def run_server():
    with socketserver.TCPServer(("", PORT), UnifiedHandler) as httpd:
        print(f"Unified server running at http://localhost:{PORT}")
        print(f"Available UIs:")
        print(f"  - Authentication: http://localhost:{PORT}/auth")
        print(f"  - Onboarding: http://localhost:{PORT}/onboarding")
        print(f"  - Child Profiles: http://localhost:{PORT}/child-profile")
        print(f"  - Gift Wall: http://localhost:{PORT}/gift-wall")
        httpd.serve_forever()

if __name__ == "__main__":
    run_server()
