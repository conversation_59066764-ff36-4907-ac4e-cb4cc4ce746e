#!/usr/bin/env python3

import requests
import json
import os
import time
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_ngrok_status():
    """Check if ngrok is running and get tunnel info"""
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🔍 Checking ngrok Status")
    print("=" * 60)
    
    try:
        # Get ngrok tunnels
        response = requests.get("http://localhost:4040/api/tunnels", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            tunnels = data.get('tunnels', [])
            
            if tunnels:
                tunnel = tunnels[0]
                public_url = tunnel['public_url']
                config = tunnel['config']
                
                print(f"✅ ngrok is running")
                print(f"🔗 Public URL: {public_url}")
                print(f"🎯 Webhook URL: {public_url}/webhook/")
                print(f"📊 Local Address: {config['addr']}")
                print(f"🌐 Protocol: {tunnel['proto']}")
                
                # Test if the public URL is accessible
                try:
                    test_response = requests.get(f"{public_url}/docs", timeout=10)
                    if test_response.status_code == 200:
                        print(f"✅ Public URL is accessible")
                    else:
                        print(f"⚠️ Public URL responded with status {test_response.status_code}")
                except Exception as e:
                    print(f"❌ Public URL is not accessible: {str(e)}")
                
                return public_url
            else:
                print(f"❌ No ngrok tunnels found")
                return None
        else:
            print(f"❌ ngrok API responded with status {response.status_code}")
            return None
            
    except requests.exceptions.ConnectionError:
        print(f"❌ ngrok is not running (no API on port 4040)")
        return None
    except Exception as e:
        print(f"❌ Error checking ngrok: {str(e)}")
        return None

def check_backend_webhook_endpoint():
    """Test if the backend webhook endpoint is working"""
    print(f"\n🔧 Testing Backend Webhook Endpoint")
    print("-" * 40)
    
    try:
        # Test local webhook endpoint
        webhook_url = "http://localhost:8000/webhook/"
        
        # Create a test webhook payload
        test_payload = {
            "type": "checkout.session.completed",
            "data": {
                "object": {
                    "id": "cs_test_debug_session",
                    "object": "checkout.session",
                    "payment_status": "paid",
                    "payment_intent": "pi_test_debug_intent",
                    "metadata": {
                        "gift_id": "test-debug-gift-id"
                    }
                }
            }
        }
        
        print(f"📡 Testing webhook endpoint: {webhook_url}")
        
        # Send test webhook (without signature for debugging)
        response = requests.post(
            webhook_url,
            json=test_payload,
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'Debug-Webhook-Test'
            },
            timeout=10
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"✅ Webhook endpoint is working")
                print(f"📄 Response: {json.dumps(response_data, indent=2)}")
                return True
            except:
                print(f"✅ Webhook endpoint responded but no JSON data")
                return True
        else:
            print(f"❌ Webhook endpoint failed")
            try:
                error_data = response.text
                print(f"❌ Error: {error_data}")
            except:
                pass
            return False
            
    except Exception as e:
        print(f"❌ Error testing webhook endpoint: {str(e)}")
        return False

def check_stripe_webhook_config():
    """Check Stripe webhook configuration"""
    print(f"\n🔑 Checking Stripe Configuration")
    print("-" * 40)
    
    stripe_secret = os.getenv("STRIPE_SECRET_KEY")
    webhook_secret = os.getenv("STRIPE_WEBHOOK_SECRET")
    
    print(f"🔐 Stripe Secret Key: {'✅ Set' if stripe_secret else '❌ Missing'}")
    if stripe_secret:
        print(f"   Key starts with: {stripe_secret[:7]}...")
    
    print(f"🔐 Webhook Secret: {'✅ Set' if webhook_secret else '❌ Missing'}")
    if webhook_secret:
        print(f"   Secret starts with: {webhook_secret[:7]}...")
    
    return bool(stripe_secret and webhook_secret)

def create_test_gift_and_get_checkout():
    """Create a test gift and get the checkout URL"""
    print(f"\n🎁 Creating Test Gift for Webhook Testing")
    print("-" * 40)
    
    test_handle = "test-child-165125"
    
    gift_data = {
        "child_profile_handle": test_handle,
        "from_name": "Webhook Debug User",
        "from_email": "<EMAIL>",
        "amount_usd": 5.0,
        "message": "Debug webhook test gift",
        "is_anonymous": False
    }
    
    try:
        api_url = f"http://localhost:8000/api/wall/{test_handle}/gift"
        print(f"📡 Creating gift via: {api_url}")
        
        response = requests.post(
            api_url,
            json=gift_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            gift_id = response_data.get('gift_id')
            checkout_url = response_data.get('checkout_url')
            
            print(f"✅ Test gift created successfully!")
            print(f"🆔 Gift ID: {gift_id}")
            print(f"🔗 Checkout URL: {checkout_url}")
            
            # Extract session ID from checkout URL
            if checkout_url and 'checkout.stripe.com' in checkout_url:
                session_id = checkout_url.split('/')[-1].split('#')[0]
                print(f"🎫 Session ID: {session_id}")
                
                return gift_id, checkout_url, session_id
            else:
                print(f"❌ Invalid checkout URL format")
                return gift_id, checkout_url, None
        else:
            print(f"❌ Failed to create gift: {response.status_code}")
            try:
                error_data = response.json()
                print(f"❌ Error: {json.dumps(error_data, indent=2)}")
            except:
                print(f"❌ Error text: {response.text}")
            return None, None, None
            
    except Exception as e:
        print(f"❌ Error creating test gift: {str(e)}")
        return None, None, None

def check_gift_status_in_db(gift_id):
    """Check the current status of a gift in the database"""
    print(f"\n💾 Checking Gift Status in Database")
    print("-" * 40)
    
    if not gift_id:
        print(f"❌ No gift ID provided")
        return None
    
    try:
        from app.database import SessionLocal
        from app import models
        from uuid import UUID
        
        db = SessionLocal()
        try:
            gift = db.query(models.Gift).filter(models.Gift.id == UUID(gift_id)).first()
            
            if gift:
                print(f"✅ Gift found in database")
                print(f"🆔 Gift ID: {gift.id}")
                print(f"📊 Payment Status: {gift.payment_status}")
                print(f"💰 Amount: ${gift.amount_usd}")
                print(f"👤 From: {gift.from_name} ({gift.from_email})")
                print(f"📅 Created: {gift.created_at}")
                print(f"🔗 Payment Intent: {gift.payment_intent_id or 'None'}")
                
                return gift.payment_status
            else:
                print(f"❌ Gift not found in database")
                return None
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Error checking gift in database: {str(e)}")
        return None

def monitor_webhook_logs():
    """Instructions for monitoring webhook logs"""
    print(f"\n📋 Webhook Monitoring Instructions")
    print("-" * 40)
    
    print(f"To monitor webhook activity:")
    print(f"1. 🖥️ Watch your backend terminal for webhook logs")
    print(f"2. 🌐 Check ngrok web interface: http://localhost:4040")
    print(f"3. 🔍 Look for these log messages:")
    print(f"   • '=== Stripe Webhook Received ==='")
    print(f"   • '🎯 Checkout session completed!'")
    print(f"   • '✅ Updated gift {{id}} to completed'")
    print(f"4. 📊 Check Stripe Dashboard webhook logs")
    print(f"5. 🔔 Verify webhook endpoint is receiving requests")

def main():
    """Main debugging function"""
    print("🔍 Webhook Issue Debugging Tool")
    print("=" * 60)
    
    # Step 1: Check ngrok status
    ngrok_url = check_ngrok_status()
    
    # Step 2: Check backend webhook endpoint
    webhook_working = check_backend_webhook_endpoint()
    
    # Step 3: Check Stripe configuration
    stripe_configured = check_stripe_webhook_config()
    
    # Step 4: Create test gift
    gift_id, checkout_url, session_id = create_test_gift_and_get_checkout()
    
    # Step 5: Check initial gift status
    if gift_id:
        initial_status = check_gift_status_in_db(gift_id)
    
    # Step 6: Provide monitoring instructions
    monitor_webhook_logs()
    
    # Summary and next steps
    print(f"\n" + "=" * 60)
    print(f"🔍 DEBUGGING SUMMARY")
    print(f"=" * 60)
    
    print(f"ngrok Status: {'✅ Running' if ngrok_url else '❌ Not Running'}")
    print(f"Webhook Endpoint: {'✅ Working' if webhook_working else '❌ Failed'}")
    print(f"Stripe Config: {'✅ Configured' if stripe_configured else '❌ Missing'}")
    print(f"Test Gift: {'✅ Created' if gift_id else '❌ Failed'}")
    
    if ngrok_url and webhook_working and stripe_configured and gift_id:
        print(f"\n✅ SETUP LOOKS GOOD!")
        print(f"🎯 Next Steps:")
        print(f"1. Complete payment for test gift: {checkout_url}")
        print(f"2. Watch backend terminal for webhook logs")
        print(f"3. Check ngrok web interface: http://localhost:4040")
        print(f"4. Verify gift status updates to 'completed'")
        
        if ngrok_url:
            print(f"\n🔧 Stripe Webhook Configuration:")
            print(f"   URL: {ngrok_url}/webhook/")
            print(f"   Events: checkout.session.completed")
            print(f"   Secret: {os.getenv('STRIPE_WEBHOOK_SECRET', 'Not set')[:10]}...")
    else:
        print(f"\n❌ ISSUES DETECTED!")
        print(f"🔧 Fix these issues:")
        if not ngrok_url:
            print(f"   • Start ngrok: ngrok http 8000")
        if not webhook_working:
            print(f"   • Check backend server is running")
        if not stripe_configured:
            print(f"   • Update .env file with Stripe keys")
        if not gift_id:
            print(f"   • Fix gift creation API")
    
    print(f"\n🧪 Debug completed!")

if __name__ == "__main__":
    main()
