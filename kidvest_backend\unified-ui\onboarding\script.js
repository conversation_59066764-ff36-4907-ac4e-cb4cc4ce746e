document.addEventListener('DOMContentLoaded', function() {
    // API endpoint
    const API_BASE_URL = 'http://localhost:8080/api/onboarding';

    // For debugging
    console.log('Onboarding UI script loaded');

    // Check if user is authenticated
    const token = localStorage.getItem('access_token');
    if (!token) {
        console.warn('No access token found. Using test token for development.');
        // Uncomment this in production
        // alert('Please log in to continue');
        // window.location.href = '/auth';
        // return;
    }

    // DOM elements
    const steps = document.querySelectorAll('.form-step');
    const progressBar = document.getElementById('progress');
    const stepCircles = document.querySelectorAll('.step');
    const stepLabels = document.querySelectorAll('.step-label');
    const apiResponse = document.getElementById('api-response');

    // Session token
    let sessionToken = '';

    // Current step
    let currentStep = 1;

    // Update progress bar and step indicators
    function updateProgress(step) {
        const percent = ((step - 1) / (stepCircles.length - 1)) * 100;
        progressBar.style.width = `${percent}%`;

        // Update step circles
        stepCircles.forEach((circle, idx) => {
            if (idx + 1 < step) {
                circle.classList.add('completed');
                circle.classList.remove('active');
            } else if (idx + 1 === step) {
                circle.classList.add('active');
                circle.classList.remove('completed');
            } else {
                circle.classList.remove('active', 'completed');
            }
        });

        // Update step labels
        stepLabels.forEach((label, idx) => {
            if (idx + 1 === step) {
                label.classList.add('active');
            } else {
                label.classList.remove('active');
            }
        });
    }

    // Show current step
    function showStep(step) {
        steps.forEach((s, idx) => {
            if (idx + 1 === step) {
                s.classList.add('active');
            } else {
                s.classList.remove('active');
            }
        });

        updateProgress(step);
    }

    // Display API response
    function displayResponse(response) {
        apiResponse.textContent = JSON.stringify(response, null, 2);
    }

    // Handle API errors
    function handleApiError(error) {
        console.error('API Error:', error);
        apiResponse.textContent = JSON.stringify({
            success: false,
            message: 'API Error',
            details: error.message
        }, null, 2);
    }

    // Step 1: Basic Account Creation
    document.getElementById('step1-next').addEventListener('click', async function() {
        const fullName = document.getElementById('full_name').value.trim();
        const email = document.getElementById('email').value.trim();

        if (!fullName || !email) {
            alert('Please fill in all fields');
            return;
        }

        // Validate that full name contains both first and last name
        const nameParts = fullName.split(' ');
        const validNameParts = nameParts.filter(part => part.trim().length > 0);

        if (validNameParts.length < 2) {
            alert('Please enter both first and last name');
            return;
        }

        try {
            console.log('Submitting step 1 data to API...');
            const response = await fetch(`${API_BASE_URL}/step1`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token || 'test-token'}`
                },
                body: JSON.stringify({
                    full_name: fullName.trim(),
                    email: email.trim()
                })
            });

            const data = await response.json();
            displayResponse(data);

            if (data.success) {
                console.log('Step 1 completed successfully');
                sessionToken = data.session_token;
                console.log('Session token received:', sessionToken);
                currentStep = 2;
                showStep(currentStep);
            } else {
                console.error('API Error:', data);
                alert(`Error: ${data.message}`);
            }
        } catch (error) {
            console.error('Network Error:', error);
            handleApiError(error);

            // Fallback for development/testing
            if (confirm('API call failed. Would you like to continue with mock data for testing?')) {
                console.log('Using mock data to continue');
                sessionToken = 'test-session-token';
                currentStep = 2;
                showStep(currentStep);
                displayResponse({
                    success: true,
                    message: 'Step 1 completed successfully (MOCK)',
                    session_token: 'test-session-token'
                });
            }
        }
    });

    // Step 2: Identity Verification
    document.getElementById('step2-prev').addEventListener('click', function() {
        currentStep = 1;
        showStep(currentStep);
    });

    document.getElementById('step2-next').addEventListener('click', async function() {
        const dob = document.getElementById('dob').value;
        const phoneNumber = document.getElementById('phone_number').value;
        const streetAddress = document.getElementById('street_address').value;
        const city = document.getElementById('city').value;
        const state = document.getElementById('state').value;
        const postalCode = document.getElementById('postal_code').value;
        const country = document.getElementById('country').value;
        let ssn = document.getElementById('ssn').value;

        // Format SSN if needed (add dashes if missing)
        if (ssn && ssn.length === 9 && !ssn.includes('-')) {
            ssn = `${ssn.substring(0, 3)}-${ssn.substring(3, 5)}-${ssn.substring(5, 9)}`;
            document.getElementById('ssn').value = ssn;
        }

        // Validate SSN format
        const ssnRegex = /^\d{3}-\d{2}-\d{4}$/;
        if (!ssnRegex.test(ssn)) {
            alert('Please enter a valid SSN in the format XXX-XX-XXXX');
            return;
        }

        if (!dob || !phoneNumber || !streetAddress || !city || !state || !postalCode || !country || !ssn) {
            alert('Please fill in all fields');
            return;
        }

        try {
            console.log('Submitting step 2 data to API with session token:', sessionToken);
            // Include session token in both URL and request body
            const url = new URL(`${API_BASE_URL}/step2`);
            url.searchParams.append('session_token', sessionToken);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token || 'test-token'}`
                },
                body: JSON.stringify({
                    dob: dob,
                    phone_number: phoneNumber,
                    street_address: streetAddress,
                    city: city,
                    state: state,
                    postal_code: postalCode,
                    country: country,
                    ssn: ssn,
                    session_token: sessionToken  // Include session token in request body too
                })
            });

            const data = await response.json();
            displayResponse(data);

            if (data.success) {
                console.log('Step 2 completed successfully');
                currentStep = 3;
                showStep(currentStep);
            } else {
                console.error('API Error:', data);
                alert(`Error: ${data.message}`);
            }
        } catch (error) {
            console.error('Network Error:', error);
            handleApiError(error);

            // Fallback for development/testing
            if (confirm('API call failed. Would you like to continue with mock data for testing?')) {
                console.log('Using mock data to continue');
                currentStep = 3;
                showStep(currentStep);
                displayResponse({
                    success: true,
                    message: 'Step 2 completed successfully (MOCK)'
                });
            }
        }
    });

    // Step 3: Financial Profile
    document.getElementById('step3-prev').addEventListener('click', function() {
        currentStep = 2;
        showStep(currentStep);
    });

    document.getElementById('step3-next').addEventListener('click', async function() {
        const employmentStatus = document.getElementById('employment_status').value;
        const incomeRange = document.getElementById('income_range').value;
        const netWorthRange = document.getElementById('net_worth_range').value;
        const fundingSource = document.getElementById('funding_source').value;
        const investmentExperience = document.getElementById('investment_experience').value;
        const riskTolerance = document.getElementById('risk_tolerance').value;

        try {
            console.log('Submitting step 3 data to API with session token:', sessionToken);
            // Include session token in both URL and request body
            const url = new URL(`${API_BASE_URL}/step3`);
            url.searchParams.append('session_token', sessionToken);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token || 'test-token'}`
                },
                body: JSON.stringify({
                    employment_status: employmentStatus,
                    income_range: incomeRange,
                    net_worth_range: netWorthRange,
                    funding_source: fundingSource,
                    investment_experience: investmentExperience,
                    risk_tolerance: riskTolerance,
                    session_token: sessionToken  // Include session token in request body too
                })
            });

            const data = await response.json();
            displayResponse(data);

            if (data.success) {
                console.log('Step 3 completed successfully');
                currentStep = 4;
                showStep(currentStep);
            } else {
                console.error('API Error:', data);
                alert(`Error: ${data.message}`);
            }
        } catch (error) {
            console.error('Network Error:', error);
            handleApiError(error);

            // Fallback for development/testing
            if (confirm('API call failed. Would you like to continue with mock data for testing?')) {
                console.log('Using mock data to continue');
                currentStep = 4;
                showStep(currentStep);
                displayResponse({
                    success: true,
                    message: 'Step 3 completed successfully (MOCK)'
                });
            }
        }
    });

    // Step 4: Disclosures and Agreements
    document.getElementById('step4-prev').addEventListener('click', function() {
        currentStep = 3;
        showStep(currentStep);
    });

    document.getElementById('step4-next').addEventListener('click', async function() {
        const isControlPerson = document.querySelector('input[name="is_control_person"]:checked').value === 'true';
        const isAffiliatedExchange = document.querySelector('input[name="is_affiliated_exchange_or_finra"]:checked').value === 'true';
        const isPoliticallyExposed = document.querySelector('input[name="is_politically_exposed"]:checked').value === 'true';
        const isFamilyExposed = document.querySelector('input[name="immediate_family_exposed"]:checked').value === 'true';
        const customerAgreement = document.getElementById('customer_agreement_accepted').checked;
        const marginAgreement = document.getElementById('margin_agreement_accepted').checked;
        const accountAgreement = document.getElementById('account_agreement_accepted').checked;

        if (!customerAgreement || !marginAgreement || !accountAgreement) {
            alert('Please accept all agreements');
            return;
        }

        try {
            console.log('Submitting step 4 data to API with session token:', sessionToken);
            // Include session token in both URL and request body
            const url = new URL(`${API_BASE_URL}/step4`);
            url.searchParams.append('session_token', sessionToken);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token || 'test-token'}`
                },
                body: JSON.stringify({
                    is_control_person: isControlPerson,
                    is_affiliated_exchange_or_finra: isAffiliatedExchange,
                    is_politically_exposed: isPoliticallyExposed,
                    immediate_family_exposed: isFamilyExposed,
                    customer_agreement_accepted: customerAgreement,
                    margin_agreement_accepted: marginAgreement,
                    account_agreement_accepted: accountAgreement,
                    session_token: sessionToken  // Include session token in request body too
                })
            });

            const data = await response.json();
            displayResponse(data);

            if (data.success) {
                console.log('Step 4 completed successfully');
                // Submit to Alpaca
                submitToAlpaca();
            } else {
                console.error('API Error:', data);
                alert(`Error: ${data.message}`);
            }
        } catch (error) {
            console.error('Network Error:', error);
            handleApiError(error);

            // Fallback for development/testing
            if (confirm('API call failed. Would you like to continue with mock data for testing?')) {
                console.log('Using mock data to continue');
                // Set mock account data
                document.getElementById('account-id').textContent = 'TEST123456';
                document.getElementById('account-status').textContent = 'PENDING';
                currentStep = 5;
                showStep(currentStep);
                displayResponse({
                    success: true,
                    message: 'Account created successfully (MOCK)',
                    account_id: 'TEST123456',
                    status: 'PENDING'
                });
            }
        }
    });

    // Submit to Alpaca
    async function submitToAlpaca() {
        try {
            console.log('Submitting to Alpaca with session token:', sessionToken);
            // Include session token in both URL and request body
            const url = new URL(`${API_BASE_URL}/submit`);
            url.searchParams.append('session_token', sessionToken);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token || 'test-token'}`
                },
                body: JSON.stringify({
                    session_token: sessionToken  // Include session token in request body too
                })
            });

            const data = await response.json();
            displayResponse(data);

            if (data.success) {
                console.log('Submission to Alpaca successful');
                // Show completion screen
                document.getElementById('account-id').textContent = data.account_id;
                document.getElementById('account-status').textContent = data.status;
                currentStep = 5;
                showStep(currentStep);
            } else {
                console.error('Alpaca API Error:', data);
                alert(`Error: ${data.message}`);
            }
        } catch (error) {
            console.error('Network Error during Alpaca submission:', error);
            handleApiError(error);

            // Fallback for development/testing
            if (confirm('Alpaca API call failed. Would you like to continue with mock data for testing?')) {
                console.log('Using mock data to continue');
                // Set mock account data
                document.getElementById('account-id').textContent = 'TEST123456';
                document.getElementById('account-status').textContent = 'PENDING';
                currentStep = 5;
                showStep(currentStep);
                displayResponse({
                    success: true,
                    message: 'Account created successfully (MOCK)',
                    account_id: 'TEST123456',
                    status: 'PENDING'
                });
            }
        }
    }

    // Navigate to Dashboard page
    document.getElementById('create-child-profile').addEventListener('click', function() {
        // Store the token in localStorage if it's not already there
        const token = localStorage.getItem('access_token');
        if (!token) {
            // If we have a session token from onboarding, we can use it as a temporary token
            // This is just for demo purposes - in a real app, you'd use a proper auth flow
            if (sessionToken) {
                localStorage.setItem('temp_session_token', sessionToken);
            }
        }

        // Navigate to dashboard page
        window.location.href = '/dashboard';
    });

    // Start over
    document.getElementById('start-over').addEventListener('click', function() {
        // Reset form
        document.querySelectorAll('input').forEach(input => {
            if (input.type === 'text' || input.type === 'email' || input.type === 'date') {
                input.value = '';
            } else if (input.type === 'checkbox') {
                input.checked = false;
            } else if (input.type === 'radio' && input.value === 'false') {
                input.checked = true;
            }
        });

        document.querySelectorAll('select').forEach(select => {
            select.selectedIndex = 0;
        });

        // Reset session
        sessionToken = '';
        apiResponse.textContent = '';

        // Go to step 1
        currentStep = 1;
        showStep(currentStep);
    });

    // Initialize
    showStep(currentStep);
});
