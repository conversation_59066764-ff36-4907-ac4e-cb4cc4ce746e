# KidVest Architecture Documentation

## Overview

KidVest is a platform that allows parents to create investment accounts for children, receive monetary gifts through a social wall, and invest using Alpaca's trading API. This document outlines the architecture of the KidVest application, including its components, data flow, and integration points.

## System Architecture

KidVest follows a two-server architecture:
1. **Backend Server**: FastAPI-based REST API server that handles business logic, database operations, and third-party integrations
2. **Frontend Server**: React Native with Expo for cross-platform mobile and web UI

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Mobile Client  │◄───►│  Backend API    │◄───►│   Database      │
│  (React Native) │     │  (FastAPI)      │     │  (PostgreSQL)   │
│                 │     │                 │     │                 │
└─────────────────┘     └────────┬────────┘     └─────────────────┘
                                 │
                                 │
                        ┌────────▼────────┐     ┌─────────────────┐
                        │                 │     │                 │
                        │  Payment        │◄───►│  Stripe         │
                        │  Processing     │     │                 │
                        │                 │     │                 │
                        └────────┬────────┘     └─────────────────┘
                                 │
                                 │
                        ┌────────▼────────┐     ┌─────────────────┐
                        │                 │     │                 │
                        │  Brokerage      │◄───►│  Alpaca API     │
                        │  Integration    │     │                 │
                        │                 │     │                 │
                        └─────────────────┘     └─────────────────┘
```

## Backend Architecture

The backend follows a layered architecture pattern:

```
┌─────────────────────────────────────────────────────────────┐
│                      FastAPI Application                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐   ┌─────────────┐   ┌─────────────────┐    │
│  │             │   │             │   │                 │    │
│  │  API Routes │──►│  Services   │──►│  Data Access   │    │
│  │             │   │             │   │                 │    │
│  └─────────────┘   └─────────────┘   └─────────────────┘    │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│                      SQLAlchemy ORM                         │
├─────────────────────────────────────────────────────────────┤
│                      PostgreSQL Database                    │
└─────────────────────────────────────────────────────────────┘
```

### Components

1. **API Routes (main.py)**
   - Defines HTTP endpoints
   - <PERSON>les request validation
   - Manages authentication and authorization
   - Routes requests to appropriate services

2. **Services**
   - **onboarding_service.py**: Handles multi-step KYC onboarding
   - **alpaca_service.py**: Integrates with Alpaca brokerage API
   - **auth.py**: Manages authentication and token generation

3. **Data Access (crud.py)**
   - Implements CRUD operations for all models
   - Abstracts database interactions

4. **Models (models.py)**
   - Defines SQLAlchemy ORM models
   - Maps to database tables

5. **Schemas (schemas.py)**
   - Defines Pydantic models for request/response validation
   - Handles data serialization/deserialization

## Frontend Architecture

The frontend uses React Native with Expo for cross-platform compatibility:

```
┌─────────────────────────────────────────────────────────────┐
│                      React Native App                       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐   ┌─────────────┐   ┌─────────────────┐    │
│  │             │   │             │   │                 │    │
│  │  Screens    │──►│  Components │◄──│  Context/State  │    │
│  │             │   │             │   │                 │    │
│  └─────────────┘   └─────────────┘   └─────────────────┘    │
│                                             ▲               │
│                                             │               │
│  ┌─────────────────────────────────────────┐│               │
│  │                                         ││               │
│  │              API Services               │◄───────────────┤
│  │                                         │                │
│  └─────────────────────────────────────────┘                │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### Components

1. **Screens**
   - **app/(tabs)/index.tsx**: Dashboard screen
   - **app/(tabs)/settings.tsx**: Settings screen
   - **app/auth/login.tsx**: Login screen
   - **app/auth/register.tsx**: Registration screen
   - **app/onboarding/kyc.tsx**: KYC onboarding flow
   - **app/onboarding/child-profile.tsx**: Child profile creation

2. **Components**
   - Reusable UI components
   - Form components
   - Navigation components

3. **Context/State**
   - **context/AuthContext.tsx**: Authentication state management
   - React hooks for state management

4. **API Services**
   - **services/api.ts**: API client with Axios
   - Authentication token management
   - API endpoint wrappers

## Authentication Flow

```
┌─────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│         │     │             │     │             │     │             │
│  User   │────►│  Login Form │────►│  /api/token │────►│  JWT Token  │
│         │     │             │     │             │     │             │
└─────────┘     └─────────────┘     └─────────────┘     └──────┬──────┘
                                                               │
                                                               ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │     │             │
│  Protected  │◄────│  API        │◄────│  Auth       │◄────│  Token      │
│  Resources  │     │  Endpoints  │     │  Middleware │     │  Validation │
│             │     │             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
```

1. User submits login credentials
2. Backend validates credentials and issues JWT token
3. Token is stored in AsyncStorage on the client
4. Token is included in Authorization header for subsequent requests
5. Backend validates token for protected endpoints

## Onboarding Flow

```
┌─────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│         │     │             │     │             │     │             │
│  User   │────►│  Step 1:    │────►│  Step 2:    │────►│  Step 3:    │
│         │     │  Basic Info │     │  Identity   │     │  Financial  │
└─────────┘     └─────────────┘     └─────────────┘     └──────┬──────┘
                                                               │
                                                               ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │     │             │
│  Brokerage  │◄────│  Alpaca     │◄────│  Account    │◄────│  Step 4:    │
│  Account    │     │  API        │     │  Creation   │     │  Agreements │
│             │     │             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
```

1. User completes basic information (name, email)
2. User provides identity verification details (address, SSN, DOB)
3. User provides financial profile information
4. User accepts agreements and disclosures
5. Backend submits information to Alpaca API
6. Brokerage account is created

## Gift Flow

```
┌─────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│         │     │             │     │             │     │             │
│  Gifter │────►│  Gift Wall  │────►│  Gift Form  │────►│  Stripe     │
│         │     │             │     │             │     │  Checkout   │
└─────────┘     └─────────────┘     └─────────────┘     └──────┬──────┘
                                                               │
                                                               ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │     │             │
│  Investment │◄────│  Gift       │◄────│  Payment    │◄────│  Webhook    │
│  Options    │     │  Received   │     │  Confirmed  │     │  Event      │
│             │     │             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
```

1. Gifter visits child's gift wall
2. Gifter fills out gift form with amount and message
3. Gifter is redirected to Stripe checkout
4. Stripe sends webhook event on payment completion
5. Backend updates gift status to completed
6. Parent can invest the gift money

## Database Schema

```
┌───────────────┐       ┌───────────────┐       ┌───────────────┐
│ users         │       │ child_profiles │       │ gifts         │
├───────────────┤       ├───────────────┤       ├───────────────┤
│ id            │       │ id            │       │ id            │
│ name          │       │ parent_id     │──┐    │ child_profile_id
│ email         │       │ name          │  │    │ from_name     │
│ hashed_password       │ age           │  │    │ from_email    │
│ user_type     │       │ handle        │  │    │ amount_usd    │
│ created_at    │       │ is_public     │  │    │ message       │
│ is_active     │       │ avatar        │  │    │ payment_status│
│ phone_number  │       │ bio           │  │    │ payment_intent_id
│ address       │       │ dob           │  │    │ checkout_session_id
│ city          │       │ created_at    │  │    │ is_anonymous  │
│ state         │       │ updated_at    │  │    │ created_at    │
│ postal_code   │       └───────┬───────┘  │    │ updated_at    │
│ country       │               │          │    └───────┬───────┘
└───────┬───────┘               │          │            │
        │                       │          │            │
        │                       │          │            │
        │                       │          │            │
┌───────▼───────┐       ┌───────▼───────┐  │    ┌───────▼───────┐
│ broker_accounts│       │ investments   │◄─┘    │ onboarding_   │
├───────────────┤       ├───────────────┤       │ sessions      │
│ id            │       │ id            │       ├───────────────┤
│ user_id       │◄──────│ child_profile_id      │ id            │
│ broker_type   │       │ gift_id       │◄──────│ session_token │
│ external_account_id   │ amount_usd    │       │ user_id       │◄─┐
│ status        │       │ symbol        │       │ current_step  │  │
│ created_at    │       │ shares        │       │ step1_completed_at
│ updated_at    │       │ purchase_price│       │ step2_completed_at
└───────────────┘       │ status        │       │ step3_completed_at
                        │ transaction_id│       │ step4_completed_at
                        │ created_at    │       │ step1_data    │
                        │ updated_at    │       │ step2_data    │
                        └───────────────┘       │ step3_data    │
                                                │ step4_data    │
                                                │ created_at    │
                                                │ updated_at    │
                                                │ expires_at    │
                                                └───────────────┘
```

## Integration Points

### Stripe Integration

- **Payment Processing**: Uses Stripe Checkout for secure payment processing
- **Webhook Handling**: Processes Stripe webhook events to update gift payment status
- **Environment Variables**:
  - `STRIPE_SECRET_KEY`: API key for Stripe API calls
  - `STRIPE_WEBHOOK_SECRET`: Secret for webhook signature verification

### Alpaca Integration

- **Brokerage Account Creation**: Creates brokerage accounts for users
- **Investment Execution**: Executes investment orders
- **Environment Variables**:
  - `ALPACA_API_KEY`: API key for Alpaca API calls
  - `ALPACA_API_SECRET`: Secret for Alpaca API authentication
  - `ALPACA_API_URL`: URL for Alpaca API (sandbox or production)

## Deployment Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                      Internet                               │
│                                                             │
└───────────────────────────┬─────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                      Load Balancer                          │
│                                                             │
└───────────────┬─────────────────────────────┬───────────────┘
                │                             │
                ▼                             ▼
┌───────────────────────────┐   ┌───────────────────────────┐
│                           │   │                           │
│   Frontend Server         │   │   Backend API Server      │
│   (React Native/Expo)     │   │   (FastAPI)               │
│                           │   │                           │
└───────────────────────────┘   └───────────────┬───────────┘
                                                │
                                                │
                                ┌───────────────▼───────────┐
                                │                           │
                                │   Database                │
                                │   (PostgreSQL)            │
                                │                           │
                                └───────────────────────────┘
```

## Security Considerations

1. **Authentication**: JWT-based authentication with token expiration
2. **Password Security**: Passwords are hashed using secure algorithms
3. **API Security**: CORS configuration to restrict API access
4. **Payment Security**: Stripe handles payment information securely
5. **Sensitive Data**: PII and financial data are handled according to regulations

## Performance Considerations

1. **Database Indexing**: Key fields are indexed for faster queries
2. **API Response Caching**: Responses can be cached for improved performance
3. **Asynchronous Processing**: Long-running tasks are handled asynchronously
4. **Connection Pooling**: Database connections are pooled for efficiency
