<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KidVest Onboarding</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="navbar-brand">KidVest</a>
            <ul class="navbar-nav">
                <li class="nav-item"><a href="/dashboard" class="nav-link">Dashboard</a></li>
                <li class="nav-item"><a href="/child-profile" class="nav-link">Child Profiles</a></li>
                <li class="nav-item"><a href="/gift-wall" class="nav-link">Gift Walls</a></li>
                <li class="nav-item active"><a href="/onboarding" class="nav-link">Onboarding</a></li>
            </ul>
        </div>
    </nav>

    <div class="container">
        <header>
            <h1>KidVest Onboarding</h1>
            <p>Create a brokerage account in just a few steps</p>
        </header>

        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress" id="progress"></div>
                <div class="step active">1</div>
                <div class="step">2</div>
                <div class="step">3</div>
                <div class="step">4</div>
                <div class="step">✓</div>
            </div>
            <div class="step-labels">
                <div class="step-label active">Account</div>
                <div class="step-label">Identity</div>
                <div class="step-label">Financial</div>
                <div class="step-label">Agreements</div>
                <div class="step-label">Complete</div>
            </div>
        </div>

        <div class="form-container">
            <!-- Step 1: Basic Account Creation -->
            <div class="form-step" id="step1">
                <h2>Basic Account Information</h2>
                <div class="form-group">
                    <label for="full_name">Full Name</label>
                    <input type="text" id="full_name" name="full_name" placeholder="John Doe">
                </div>
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" placeholder="<EMAIL>">
                </div>
                <div class="button-group">
                    <button class="btn btn-primary" id="step1-next">Next</button>
                </div>
            </div>

            <!-- Step 2: Identity Verification -->
            <div class="form-step" id="step2">
                <h2>Identity Verification</h2>
                <div class="form-group">
                    <label for="dob">Date of Birth</label>
                    <input type="date" id="dob" name="dob">
                </div>
                <div class="form-group">
                    <label for="phone_number">Phone Number</label>
                    <input type="text" id="phone_number" name="phone_number" placeholder="************">
                </div>
                <div class="form-group">
                    <label for="street_address">Street Address</label>
                    <input type="text" id="street_address" name="street_address" placeholder="123 Main St">
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="city">City</label>
                        <input type="text" id="city" name="city" placeholder="New York">
                    </div>
                    <div class="form-group">
                        <label for="state">State</label>
                        <input type="text" id="state" name="state" placeholder="NY">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="postal_code">Postal Code</label>
                        <input type="text" id="postal_code" name="postal_code" placeholder="10001">
                    </div>
                    <div class="form-group">
                        <label for="country">Country</label>
                        <input type="text" id="country" name="country" value="USA" readonly>
                    </div>
                </div>
                <div class="form-group">
                    <label for="ssn">Social Security Number</label>
                    <input type="text" id="ssn" name="ssn" placeholder="***********" pattern="\d{3}-\d{2}-\d{4}" title="Please use format: XXX-XX-XXXX">
                    <small class="form-hint">Format: XXX-XX-XXXX (include dashes)</small>
                </div>
                <div class="button-group">
                    <button class="btn btn-secondary" id="step2-prev">Previous</button>
                    <button class="btn btn-primary" id="step2-next">Next</button>
                </div>
            </div>

            <!-- Step 3: Financial Profile -->
            <div class="form-step" id="step3">
                <h2>Financial Profile</h2>
                <div class="form-group">
                    <label for="employment_status">Employment Status</label>
                    <select id="employment_status" name="employment_status">
                        <option value="EMPLOYED">Employed</option>
                        <option value="UNEMPLOYED">Unemployed</option>
                        <option value="RETIRED">Retired</option>
                        <option value="STUDENT">Student</option>
                        <option value="OTHER">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="income_range">Annual Income</label>
                    <select id="income_range" name="income_range">
                        <option value="under_50k">Under $50,000</option>
                        <option value="50k_100k">$50,000 - $100,000</option>
                        <option value="100k_250k">$100,000 - $250,000</option>
                        <option value="over_250k">Over $250,000</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="net_worth_range">Net Worth</label>
                    <select id="net_worth_range" name="net_worth_range">
                        <option value="under_50k">Under $50,000</option>
                        <option value="50k_100k">$50,000 - $100,000</option>
                        <option value="100k_250k">$100,000 - $250,000</option>
                        <option value="over_250k">Over $250,000</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="funding_source">Primary Source of Funds</label>
                    <select id="funding_source" name="funding_source">
                        <option value="employment_income">Employment Income</option>
                        <option value="investments">Investments</option>
                        <option value="inheritance">Inheritance</option>
                        <option value="business_income">Business Income</option>
                        <option value="savings">Savings</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="investment_experience">Investment Experience</label>
                    <select id="investment_experience" name="investment_experience">
                        <option value="none">None</option>
                        <option value="some">Some</option>
                        <option value="experienced">Experienced</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="risk_tolerance">Risk Tolerance</label>
                    <select id="risk_tolerance" name="risk_tolerance">
                        <option value="conservative">Conservative</option>
                        <option value="moderate">Moderate</option>
                        <option value="aggressive">Aggressive</option>
                    </select>
                </div>
                <div class="button-group">
                    <button class="btn btn-secondary" id="step3-prev">Previous</button>
                    <button class="btn btn-primary" id="step3-next">Next</button>
                </div>
            </div>

            <!-- Step 4: Disclosures and Agreements -->
            <div class="form-step" id="step4">
                <h2>Disclosures and Agreements</h2>
                <div class="form-group checkbox-group">
                    <p class="disclosure-question">Are you a control person of a publicly traded company?</p>
                    <div class="radio-options">
                        <label>
                            <input type="radio" name="is_control_person" value="false" checked> No
                        </label>
                        <label>
                            <input type="radio" name="is_control_person" value="true"> Yes
                        </label>
                    </div>
                </div>
                <div class="form-group checkbox-group">
                    <p class="disclosure-question">Are you affiliated with a securities exchange or FINRA?</p>
                    <div class="radio-options">
                        <label>
                            <input type="radio" name="is_affiliated_exchange_or_finra" value="false" checked> No
                        </label>
                        <label>
                            <input type="radio" name="is_affiliated_exchange_or_finra" value="true"> Yes
                        </label>
                    </div>
                </div>
                <div class="form-group checkbox-group">
                    <p class="disclosure-question">Are you a politically exposed person?</p>
                    <div class="radio-options">
                        <label>
                            <input type="radio" name="is_politically_exposed" value="false" checked> No
                        </label>
                        <label>
                            <input type="radio" name="is_politically_exposed" value="true"> Yes
                        </label>
                    </div>
                </div>
                <div class="form-group checkbox-group">
                    <p class="disclosure-question">Do you have an immediate family member who is politically exposed?</p>
                    <div class="radio-options">
                        <label>
                            <input type="radio" name="immediate_family_exposed" value="false" checked> No
                        </label>
                        <label>
                            <input type="radio" name="immediate_family_exposed" value="true"> Yes
                        </label>
                    </div>
                </div>
                <div class="form-group checkbox-group">
                    <label class="checkbox-container">
                        <input type="checkbox" id="customer_agreement_accepted" name="customer_agreement_accepted">
                        <span class="checkmark"></span>
                        I accept the <a href="#" class="agreement-link">Customer Agreement</a>
                    </label>
                </div>
                <div class="form-group checkbox-group">
                    <label class="checkbox-container">
                        <input type="checkbox" id="margin_agreement_accepted" name="margin_agreement_accepted">
                        <span class="checkmark"></span>
                        I accept the <a href="#" class="agreement-link">Margin Agreement</a>
                    </label>
                </div>
                <div class="form-group checkbox-group">
                    <label class="checkbox-container">
                        <input type="checkbox" id="account_agreement_accepted" name="account_agreement_accepted">
                        <span class="checkmark"></span>
                        I accept the <a href="#" class="agreement-link">Account Agreement</a>
                    </label>
                </div>
                <div class="button-group">
                    <button class="btn btn-secondary" id="step4-prev">Previous</button>
                    <button class="btn btn-primary" id="step4-next">Submit</button>
                </div>
            </div>

            <!-- Step 5: Completion -->
            <div class="form-step" id="step5">
                <div class="completion-container">
                    <div class="completion-icon">✓</div>
                    <h2>Account Created Successfully!</h2>
                    <p>Your brokerage account has been submitted for approval.</p>
                    <div class="account-details">
                        <div class="detail-row">
                            <span class="detail-label">Account ID:</span>
                            <span class="detail-value" id="account-id"></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Status:</span>
                            <span class="detail-value" id="account-status"></span>
                        </div>
                    </div>
                    <p class="completion-message">You will receive an email when your account is approved.</p>
                    <p class="next-steps-message">Next Steps: Go to your dashboard to manage your account and create child profiles.</p>
                    <div class="button-group">
                        <button class="btn btn-secondary" id="start-over">Start Over</button>
                        <button class="btn btn-primary" id="create-child-profile">Go to Dashboard</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="api-response-container">
            <h3>API Response</h3>
            <pre id="api-response"></pre>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
