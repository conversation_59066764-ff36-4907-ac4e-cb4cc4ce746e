import os
import psycopg2
from dotenv import load_dotenv
import uuid

# Load environment variables
load_dotenv()

# Get database connection details from DATABASE_URL
DATABASE_URL = os.getenv("DATABASE_URL")

print("=== Database Migration Script ===")

# Parse the DATABASE_URL
if DATABASE_URL.startswith("postgresql://"):
    # Format: postgresql://username:password@host:port/database
    url_without_protocol = DATABASE_URL.replace("postgresql://", "")
    user_pass, host_port_db = url_without_protocol.split("@")
    
    if ":" in user_pass:
        username, password = user_pass.split(":")
    else:
        username = user_pass
        password = ""
    
    host_port, database = host_port_db.split("/")
    
    if ":" in host_port:
        host, port = host_port.split(":")
    else:
        host = host_port
        port = "5432"
else:
    print("Invalid DATABASE_URL format")
    exit(1)

def migrate_database():
    """Migrate the database schema"""
    try:
        # Connect to the database
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=password
        )
        
        # Create a cursor
        cur = conn.cursor()
        
        # Step 1: Migrate data from kid_profiles to child_profiles if needed
        print("\n=== Step 1: Migrating kid_profiles to child_profiles ===")
        
        # Check if kid_profiles table exists and has data
        cur.execute("SELECT COUNT(*) FROM kid_profiles")
        kid_profiles_count = cur.fetchone()[0]
        print(f"Found {kid_profiles_count} records in kid_profiles table")
        
        if kid_profiles_count > 0:
            # Check if the data has already been migrated
            cur.execute("SELECT COUNT(*) FROM child_profiles")
            child_profiles_count = cur.fetchone()[0]
            print(f"Found {child_profiles_count} records in child_profiles table")
            
            if child_profiles_count == 0:
                print("Migrating data from kid_profiles to child_profiles...")
                
                # Get all kid profiles
                cur.execute("""
                    SELECT id, parent_id, name, age, handle, is_public, avatar, bio, created_at, updated_at
                    FROM kid_profiles
                """)
                
                kid_profiles = cur.fetchall()
                
                # Insert into child_profiles
                for profile in kid_profiles:
                    cur.execute("""
                        INSERT INTO child_profiles (id, parent_id, name, age, handle, is_public, avatar, bio, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, profile)
                
                print(f"Migrated {len(kid_profiles)} profiles from kid_profiles to child_profiles")
            else:
                print("Child profiles already exist, skipping migration")
        else:
            print("No kid profiles found, skipping migration")
        
        # Step 2: Update gifts table to use child_profile_id
        print("\n=== Step 2: Updating gifts table ===")
        
        # Check if gifts table has data with kid_profile_id but not child_profile_id
        cur.execute("""
            SELECT COUNT(*) FROM gifts 
            WHERE kid_profile_id IS NOT NULL AND child_profile_id IS NULL
        """)
        
        gifts_to_update_count = cur.fetchone()[0]
        print(f"Found {gifts_to_update_count} gifts that need updating")
        
        if gifts_to_update_count > 0:
            print("Updating gifts to use child_profile_id...")
            
            # Update gifts to use child_profile_id instead of kid_profile_id
            cur.execute("""
                UPDATE gifts
                SET child_profile_id = kid_profile_id
                WHERE kid_profile_id IS NOT NULL AND child_profile_id IS NULL
            """)
            
            print(f"Updated {gifts_to_update_count} gifts")
        else:
            print("No gifts need updating")
        
        # Step 3: Update investments table to reference child_profiles
        print("\n=== Step 3: Updating investments table ===")
        
        # Check if investments table has foreign key to kid_profiles
        cur.execute("""
            SELECT
                ccu.table_name AS foreign_table_name
            FROM
                information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                  ON tc.constraint_name = kcu.constraint_name
                  AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                  ON ccu.constraint_name = tc.constraint_name
                  AND ccu.table_schema = tc.table_schema
            WHERE tc.constraint_type = 'FOREIGN KEY' 
                AND tc.table_name='investments'
                AND kcu.column_name='child_profile_id'
                AND ccu.table_name='kid_profiles'
        """)
        
        has_kid_profiles_fk = cur.fetchone() is not None
        
        if has_kid_profiles_fk:
            print("Investments table references kid_profiles, updating...")
            
            # Drop the foreign key constraint
            cur.execute("""
                ALTER TABLE investments
                DROP CONSTRAINT IF EXISTS investments_child_profile_id_fkey
            """)
            
            # Add new foreign key constraint to child_profiles
            cur.execute("""
                ALTER TABLE investments
                ADD CONSTRAINT investments_child_profile_id_fkey
                FOREIGN KEY (child_profile_id) REFERENCES child_profiles(id)
            """)
            
            print("Updated investments table foreign key")
        else:
            print("Investments table already references child_profiles or has no foreign key")
        
        # Step 4: Make child_profile_id NOT NULL in gifts table
        print("\n=== Step 4: Updating gifts table constraints ===")
        
        # Check if child_profile_id is nullable
        cur.execute("""
            SELECT is_nullable
            FROM information_schema.columns
            WHERE table_name = 'gifts' AND column_name = 'child_profile_id'
        """)
        
        is_nullable = cur.fetchone()[0] == 'YES'
        
        if is_nullable:
            print("Making child_profile_id NOT NULL in gifts table...")
            
            # First, check if there are any NULL values
            cur.execute("SELECT COUNT(*) FROM gifts WHERE child_profile_id IS NULL")
            null_count = cur.fetchone()[0]
            
            if null_count > 0:
                print(f"Warning: {null_count} gifts have NULL child_profile_id")
                print("These will need to be fixed manually or deleted")
            else:
                # Make the column NOT NULL
                cur.execute("""
                    ALTER TABLE gifts
                    ALTER COLUMN child_profile_id SET NOT NULL
                """)
                
                print("Updated child_profile_id to NOT NULL")
        else:
            print("child_profile_id is already NOT NULL")
        
        # Commit the transaction
        conn.commit()
        print("\n=== Migration completed successfully! ===")
        
    except Exception as e:
        print(f"Error during migration: {e}")
        conn.rollback()
    finally:
        # Close the cursor and connection
        cur.close()
        conn.close()

if __name__ == "__main__":
    migrate_database()
    print("\nDone.")
