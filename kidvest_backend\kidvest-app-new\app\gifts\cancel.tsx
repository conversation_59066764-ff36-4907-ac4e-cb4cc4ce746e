import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { FontAwesome5 } from '@expo/vector-icons';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';

export default function GiftCancelScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { child_handle } = useLocalSearchParams();
  
  // Navigate back to the gift creation screen
  const handleTryAgain = () => {
    if (child_handle) {
      router.replace(`/gifts/create?childHandle=${child_handle}`);
    } else {
      router.replace('/gifts/create');
    }
  };
  
  // Navigate back to the gifts tab
  const handleBackToGifts = () => {
    router.replace('/(tabs)/gifts');
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={[styles.iconContainer, { backgroundColor: '#FFEBEE' }]}>
          <FontAwesome5 name="times-circle" size={64} color="#F44336" />
        </View>
        
        <Text style={styles.title}>Payment Cancelled</Text>
        
        <Text style={styles.description}>
          Your payment was cancelled and no charges were made to your card. 
          You can try again or return to the gifts page.
        </Text>
        
        <View style={styles.infoContainer}>
          <View style={styles.infoItem}>
            <View style={[styles.infoIconContainer, { backgroundColor: '#FFEBEE' }]}>
              <FontAwesome5 name="shield-alt" size={20} color="#F44336" />
            </View>
            <View style={styles.infoTextContainer}>
              <Text style={styles.infoTitle}>No Charges Made</Text>
              <Text style={styles.infoDescription}>
                Your payment method was not charged. You can safely try again.
              </Text>
            </View>
          </View>
          
          <View style={styles.infoItem}>
            <View style={[styles.infoIconContainer, { backgroundColor: Colors[colorScheme].primary + '20' }]}>
              <FontAwesome5 name="redo" size={20} color={Colors[colorScheme].primary} />
            </View>
            <View style={styles.infoTextContainer}>
              <Text style={styles.infoTitle}>Try Again</Text>
              <Text style={styles.infoDescription}>
                You can retry the payment process at any time.
              </Text>
            </View>
          </View>
        </View>
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.secondaryButton, { borderColor: Colors[colorScheme].primary }]}
            onPress={handleBackToGifts}
          >
            <FontAwesome5 name="arrow-left" size={16} color={Colors[colorScheme].primary} />
            <Text style={[styles.secondaryButtonText, { color: Colors[colorScheme].primary }]}>
              Back to Gifts
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: Colors[colorScheme].primary }]}
            onPress={handleTryAgain}
          >
            <FontAwesome5 name="redo" size={16} color="#FFFFFF" />
            <Text style={styles.primaryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
  },
  content: {
    padding: 24,
    alignItems: 'center',
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  infoContainer: {
    width: '100%',
    marginBottom: 32,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  infoIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  infoTextContainer: {
    flex: 1,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  infoDescription: {
    fontSize: 14,
    color: '#666',
  },
  buttonContainer: {
    width: '100%',
  },
  secondaryButton: {
    flexDirection: 'row',
    height: 48,
    borderRadius: 8,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  secondaryButtonText: {
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  primaryButton: {
    flexDirection: 'row',
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
});
