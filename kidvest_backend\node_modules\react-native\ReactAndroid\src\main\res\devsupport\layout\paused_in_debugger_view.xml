<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/button"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layoutDirection="ltr"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:background="@android:color/transparent"
    android:clickable="true"
    android:focusable="true"
    android:paddingStart="17dp"
    android:paddingEnd="11dp"
    tools:ignore="MissingDefaultResource"
    >
    <TextView
        android:id="@+id/button_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text=""
        android:textColor="#444"
        android:textSize="16dp"
        android:textStyle="bold"
        android:layout_marginBottom="1dp"
        />
    <ImageView
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/ic_resume"
        android:layout_marginStart="8dp"
        android:layout_marginVertical="8dp"
        />
</LinearLayout>
