@echo off
echo Starting KidVest Backend and React Native App...

echo.
echo Starting Backend Server...
start powershell -Command "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process; cd C:\Users\<USER>\kidvest_backend; .\venv\Scripts\activate; python -m app.main"

echo.
echo Starting React Native App...
start powershell -Command "cd C:\Users\<USER>\kidvest_backend\kidvest-app; npm start"

echo.
echo Servers started!
echo Backend: http://localhost:8000
echo React Native App: Check the Expo window that opened
echo.
echo Press 'w' in the Expo window to open the app in a web browser
echo.
