import os
import sys
from flask import Flask, send_from_directory, redirect, request, jsonify, Response
import logging
import requests

# Configure logging to stdout
logging.basicConfig(level=logging.DEBUG,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    handlers=[logging.StreamHandler(sys.stdout)])
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Define the base directory
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# Define the UI directories
UI_DIRS = {
    "auth": os.path.join(BASE_DIR, "unified-ui", "auth"),
    "onboarding": os.path.join(BASE_DIR, "unified-ui", "onboarding"),
    "child-profile": os.path.join(BASE_DIR, "unified-ui", "child-profile"),
    "dashboard": os.path.join(BASE_DIR, "unified-ui", "dashboard"),
    "gift-wall": os.path.join(BASE_DIR, "unified-ui", "gift-wall"),
    "shared": os.path.join(BASE_DIR, "unified-ui", "shared")
}

# Verify all directories exist
for name, path in UI_DIRS.items():
    if not os.path.exists(path):
        logger.warning(f"UI directory {name} not found at {path}")
    else:
        logger.info(f"Found UI directory {name} at {path}")

# Root route - redirect to auth by default
@app.route('/')
def index():
    logger.info("Accessing root route, redirecting to auth")
    print("Accessing root route, redirecting to auth")
    return redirect('/auth')

# Auth routes
@app.route('/auth')
@app.route('/auth/')
def auth():
    logger.info("Serving auth UI")
    print(f"Serving auth UI from {UI_DIRS['auth']}")
    try:
        return send_from_directory(UI_DIRS["auth"], "index.html")
    except Exception as e:
        print(f"Error serving auth UI: {str(e)}")
        logger.error(f"Error serving auth UI: {str(e)}")
        return jsonify(error="Error serving auth UI", details=str(e)), 500

@app.route('/auth/<path:path>')
def auth_files(path):
    logger.info(f"Serving auth file: {path}")
    print(f"Serving auth file: {path} from {UI_DIRS['auth']}")
    try:
        return send_from_directory(UI_DIRS["auth"], path)
    except Exception as e:
        print(f"Error serving auth file {path}: {str(e)}")
        logger.error(f"Error serving auth file {path}: {str(e)}")
        return jsonify(error=f"Error serving auth file {path}", details=str(e)), 500

# Dashboard routes
@app.route('/dashboard')
@app.route('/dashboard/')
def dashboard():
    logger.info("Serving dashboard UI")
    print(f"Serving dashboard UI from: {UI_DIRS['dashboard']}")
    try:
        response = send_from_directory(UI_DIRS["dashboard"], "index.html")
        # Add cache-busting headers
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        return response
    except Exception as e:
        logger.error(f"Error serving dashboard UI: {str(e)}")
        return jsonify(error="Error serving dashboard UI", details=str(e)), 500

@app.route('/dashboard/<path:path>')
def dashboard_files(path):
    logger.info(f"Serving dashboard file: {path}")
    print(f"Serving dashboard file: {path} from {UI_DIRS['dashboard']}")
    try:
        response = send_from_directory(UI_DIRS["dashboard"], path)
        # Add cache-busting headers for CSS and JS files
        if path.endswith('.css') or path.endswith('.js'):
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
        return response
    except Exception as e:
        logger.error(f"Error serving dashboard file {path}: {str(e)}")
        return jsonify(error=f"Error serving dashboard file {path}", details=str(e)), 500

# Onboarding routes
@app.route('/onboarding')
@app.route('/onboarding/')
@app.route('/onboarding/from-dashboard')
def onboarding():
    logger.info("Serving onboarding UI")
    print(f"Serving onboarding UI from: {UI_DIRS['onboarding']}")
    try:
        response = send_from_directory(UI_DIRS["onboarding"], "index.html")
        # Add cache-busting headers
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        return response
    except Exception as e:
        logger.error(f"Error serving onboarding UI: {str(e)}")
        return jsonify(error="Error serving onboarding UI", details=str(e)), 500

@app.route('/onboarding/<path:path>')
def onboarding_files(path):
    logger.info(f"Serving onboarding file: {path}")
    print(f"Serving onboarding file: {path} from {UI_DIRS['onboarding']}")
    try:
        response = send_from_directory(UI_DIRS["onboarding"], path)
        # Add cache-busting headers for CSS and JS files
        if path.endswith('.css') or path.endswith('.js'):
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
        return response
    except Exception as e:
        logger.error(f"Error serving onboarding file {path}: {str(e)}")
        return jsonify(error=f"Error serving onboarding file {path}", details=str(e)), 500

# Child profile routes
@app.route('/child-profile')
@app.route('/child-profile/')
@app.route('/child-profile/from-dashboard')
def child_profile():
    logger.info("Serving child profile UI")
    print(f"Serving child profile UI from: {UI_DIRS['child-profile']}")
    try:
        response = send_from_directory(UI_DIRS["child-profile"], "index.html")
        # Add cache-busting headers
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        return response
    except Exception as e:
        logger.error(f"Error serving child profile UI: {str(e)}")
        return jsonify(error="Error serving child profile UI", details=str(e)), 500

@app.route('/child-profile/<path:path>')
def child_profile_files(path):
    logger.info(f"Serving child profile file: {path}")
    print(f"Serving child profile file: {path} from {UI_DIRS['child-profile']}")
    try:
        response = send_from_directory(UI_DIRS["child-profile"], path)
        # Add cache-busting headers for CSS and JS files
        if path.endswith('.css') or path.endswith('.js'):
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
        return response
    except Exception as e:
        logger.error(f"Error serving child profile file {path}: {str(e)}")
        return jsonify(error=f"Error serving child profile file {path}", details=str(e)), 500

# Gift wall routes
@app.route('/gift-wall')
@app.route('/gift-wall/')
def gift_wall():
    logger.info("Serving gift wall UI")
    try:
        return send_from_directory(UI_DIRS["gift-wall"], "index.html")
    except Exception as e:
        logger.error(f"Error serving gift wall UI: {str(e)}")
        return jsonify(error="Error serving gift wall UI", details=str(e)), 500

@app.route('/gift-wall/<path:path>')
def gift_wall_files(path):
    logger.info(f"Serving gift wall file: {path}")
    try:
        return send_from_directory(UI_DIRS["gift-wall"], path)
    except Exception as e:
        logger.error(f"Error serving gift wall file {path}: {str(e)}")
        return jsonify(error=f"Error serving gift wall file {path}", details=str(e)), 500

# Wall routes for direct gift wall access
@app.route('/wall/<path:handle>')
def wall(handle):
    logger.info(f"Serving gift wall for handle: {handle}")
    # This would typically redirect to a specific gift wall page
    # For now, we'll just serve the gift wall UI
    try:
        return send_from_directory(UI_DIRS["gift-wall"], "index.html")
    except Exception as e:
        logger.error(f"Error serving gift wall for handle {handle}: {str(e)}")
        return jsonify(error=f"Error serving gift wall for handle {handle}", details=str(e)), 500

# Shared resources routes
@app.route('/shared/<path:path>')
def shared_files(path):
    logger.info(f"Serving shared file: {path}")
    print(f"Serving shared file: {path} from {UI_DIRS['shared']}")
    try:
        response = send_from_directory(UI_DIRS["shared"], path)
        # Add cache-busting headers for all shared files
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        return response
    except Exception as e:
        logger.error(f"Error serving shared file {path}: {str(e)}")
        return jsonify(error=f"Error serving shared file {path}", details=str(e)), 500

# Error handlers
@app.errorhandler(404)
def page_not_found(e):
    logger.error(f"404 error: {request.path}")
    return jsonify(error="Page not found", path=request.path), 404

@app.errorhandler(500)
def server_error(e):
    logger.error(f"500 error: {str(e)}")
    return jsonify(error="Internal server error", details=str(e)), 500

# API Proxy
@app.route('/api/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])
def proxy_api(path):
    logger.info(f"Proxying API request: {request.method} {path}")
    print(f"\n==== PROXY REQUEST ====\nMethod: {request.method}\nPath: {path}")

    # Forward the request to the backend API
    backend_url = f"http://localhost:8000/api/{path}"
    logger.info(f"Forwarding to: {backend_url}")
    print(f"Forwarding to: {backend_url}")

    # Print request details for debugging
    print(f"Request headers: {dict(request.headers)}")
    print(f"Request cookies: {dict(request.cookies)}")
    print(f"Request form data: {request.form}")
    print(f"Request query string: {request.query_string}")

    # Get the request data
    headers = {key: value for key, value in request.headers if key != 'Host'}
    data = request.get_data()
    form_data = request.form.to_dict() if request.form else None

    # Add timeout to requests to prevent hanging
    timeout = 10  # 10 seconds timeout

    # Special handling for token endpoint
    if path == 'token' and request.method == 'POST':
        print("\n==== TOKEN REQUEST DETECTED ====\n")
        # For token requests, we need to ensure the data is properly formatted
        if form_data:
            print(f"Form data: {form_data}")
            # Use the form data directly
            data = form_data
        else:
            # Try to parse the data as form data
            try:
                data_str = data.decode('utf-8')
                print(f"Raw data: {data_str}")
                # Parse the form data manually
                if '=' in data_str:
                    parsed_data = {}
                    for pair in data_str.split('&'):
                        key, value = pair.split('=')
                        parsed_data[key] = value
                    data = parsed_data
                    print(f"Parsed form data: {data}")
            except Exception as e:
                print(f"Error parsing form data: {str(e)}")

    # Log request details
    print(f"Request headers: {headers}")
    print(f"Request data type: {type(data)}")
    print(f"Request data: {data}")

    try:
        # Make the request to the backend API
        if path == 'token' and request.method == 'POST':
            # For token requests, use form data
            print("Sending token request with form data")
            response = requests.post(
                url=backend_url,
                data=data,
                headers=headers,
                cookies=request.cookies,
                allow_redirects=False,
                timeout=timeout
            )
        else:
            # For other requests, use the standard method
            response = requests.request(
                method=request.method,
                url=backend_url,
                headers=headers,
                data=data,
                cookies=request.cookies,
                allow_redirects=False,
                timeout=timeout
            )

        # Log the response
        logger.info(f"Backend API response: {response.status_code}")
        print(f"\n==== PROXY RESPONSE ====\nStatus: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")

        # Log response content for debugging
        content = response.content
        content_str = content.decode('utf-8') if content else ''
        print(f"Response content: {content_str[:500]}{'...' if len(content_str) > 500 else ''}")

        # Return the response from the backend API
        return Response(
            content,
            status=response.status_code,
            headers=dict(response.headers)
        )
    except Exception as e:
        logger.error(f"Error proxying API request: {str(e)}")
        print(f"\n==== PROXY ERROR ====\nError: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify(error="API proxy error", details=str(e)), 500

# Add CORS headers
@app.after_request
def add_cors_headers(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    # Add cache control headers to prevent caching
    response.headers.add('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
    response.headers.add('Pragma', 'no-cache')
    response.headers.add('Expires', '0')
    return response

if __name__ == '__main__':
    print("Starting unified UI server...")
    print("Available UIs:")
    print(f"  - Auth: http://localhost:8080/auth/")
    print(f"  - Dashboard: http://localhost:8080/dashboard/")
    print(f"  - Onboarding: http://localhost:8080/onboarding/")
    print(f"  - Child Profiles: http://localhost:8080/child-profile/")
    print(f"  - Gift Walls: http://localhost:8080/gift-wall/")

    # Run the Flask app
    app.run(host='0.0.0.0', port=8080, debug=True)
