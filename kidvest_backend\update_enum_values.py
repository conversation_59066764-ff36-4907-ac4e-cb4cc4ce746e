import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database connection details from DATABASE_URL
DATABASE_URL = os.getenv("DATABASE_URL")

print("=== Updating Enum Values ===")

# Parse the DATABASE_URL
if DATABASE_URL.startswith("postgresql://"):
    # Format: postgresql://username:password@host:port/database
    url_without_protocol = DATABASE_URL.replace("postgresql://", "")
    user_pass, host_port_db = url_without_protocol.split("@")
    
    if ":" in user_pass:
        username, password = user_pass.split(":")
    else:
        username = user_pass
        password = ""
    
    host_port, database = host_port_db.split("/")
    
    if ":" in host_port:
        host, port = host_port.split(":")
    else:
        host = host_port
        port = "5432"
else:
    print("Invalid DATABASE_URL format")
    exit(1)

def update_enum_values():
    """Update enum values in the database"""
    try:
        # Connect to the database
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=password
        )
        
        # Create a cursor
        cur = conn.cursor()
        
        # Check if the enum type exists
        cur.execute("""
            SELECT typname
            FROM pg_type
            WHERE typname = 'usertype'
        """)
        
        enum_exists = cur.fetchone() is not None
        
        if enum_exists:
            print("UserType enum exists, updating values...")
            
            # Update the enum type
            cur.execute("""
                ALTER TYPE usertype ADD VALUE IF NOT EXISTS 'parent';
                ALTER TYPE usertype ADD VALUE IF NOT EXISTS 'child';
            """)
            
            print("Enum values added successfully!")
        else:
            print("UserType enum does not exist, creating it...")
            
            # Create the enum type
            cur.execute("""
                CREATE TYPE usertype AS ENUM ('parent', 'child');
            """)
            
            print("Enum type created successfully!")
        
        # Commit the transaction
        conn.commit()
        
    except Exception as e:
        print(f"Error updating enum values: {e}")
        conn.rollback()
    finally:
        # Close the cursor and connection
        cur.close()
        conn.close()

if __name__ == "__main__":
    update_enum_values()
    print("\nDone.")
