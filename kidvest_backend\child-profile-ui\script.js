// API Base URL
const API_BASE_URL = 'http://localhost:8000/api';

// For debugging
console.log('Child Profile UI script loaded');

// DOM Elements
const profilesContainer = document.getElementById('profiles-container');
const loadingProfiles = document.getElementById('loading-profiles');
const noProfiles = document.getElementById('no-profiles');
const apiResponse = document.getElementById('api-response');
const userNameElement = document.getElementById('user-name');
const logoutBtn = document.getElementById('logout-btn');
const addProfileForm = document.getElementById('add-profile-form');
const saveProfileBtn = document.getElementById('save-profile-btn');
const editProfileForm = document.getElementById('edit-profile-form');
const updateProfileBtn = document.getElementById('update-profile-btn');
const confirmDeleteBtn = document.getElementById('confirm-delete-btn');

// Bootstrap Modals
const addProfileModal = new bootstrap.Modal(document.getElementById('addProfileModal'));
const editProfileModal = new bootstrap.Modal(document.getElementById('editProfileModal'));
const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));

// Check Authentication
document.addEventListener('DOMContentLoaded', function() {
    const token = localStorage.getItem('access_token');
    if (!token) {
        alert('Please log in to continue');
        window.location.href = '/auth';
        return;
    }

    // Load user profile
    loadUserProfile();

    // Load child profiles
    loadChildProfiles();
});

// Show API Response
function showResponse(response, isError = false) {
    apiResponse.style.display = 'block';
    apiResponse.style.backgroundColor = isError ? '#f8d7da' : '#d4edda';
    apiResponse.style.color = isError ? '#721c24' : '#155724';
    apiResponse.textContent = typeof response === 'object' ? JSON.stringify(response, null, 2) : response;
}

// Load User Profile
async function loadUserProfile() {
    try {
        const token = localStorage.getItem('access_token');
        const response = await fetch(`${API_BASE_URL}/users/me`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            const userData = await response.json();
            userNameElement.textContent = `Welcome, ${userData.name}`;
        } else {
            console.error('Failed to load user profile');
        }
    } catch (error) {
        console.error('Error loading user profile:', error);
    }
}

// Load Child Profiles
async function loadChildProfiles() {
    try {
        const token = localStorage.getItem('access_token');
        const response = await fetch(`${API_BASE_URL}/children/`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        const data = await response.json();

        // Hide loading indicator
        loadingProfiles.classList.add('d-none');

        if (response.ok) {
            if (data.length === 0) {
                // Show no profiles message
                noProfiles.classList.remove('d-none');
            } else {
                // Render profiles
                renderProfiles(data);
            }
        } else {
            showResponse(data, true);
        }
    } catch (error) {
        loadingProfiles.classList.add('d-none');
        showResponse(`Error: ${error.message}`, true);
    }
}

// Render Profiles
function renderProfiles(profiles) {
    profilesContainer.innerHTML = '';

    profiles.forEach(profile => {
        const profileCard = document.createElement('div');
        profileCard.className = 'col-md-4 mb-4';
        profileCard.innerHTML = `
            <div class="profile-card">
                <div class="profile-header">
                    <h5 class="mb-0">${profile.name}</h5>
                </div>
                <div class="profile-body">
                    <div class="profile-avatar">
                        ${profile.name.charAt(0).toUpperCase()}
                    </div>
                    <p class="text-center mb-1"><strong>Handle:</strong> ${profile.handle}</p>
                    <p class="text-center mb-1"><strong>Age:</strong> ${calculateAge(profile.dob)}</p>
                    <p class="text-center mb-3"><strong>Status:</strong> ${profile.is_public ? 'Public' : 'Private'}</p>
                    <div class="profile-actions">
                        <button class="btn btn-sm btn-outline-primary view-wall-btn" data-handle="${profile.handle}">
                            <i class="bi bi-gift"></i> Gift Wall
                        </button>
                        <div>
                            <button class="btn btn-sm btn-outline-secondary edit-profile-btn" data-profile='${JSON.stringify(profile)}'>
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger delete-profile-btn" data-id="${profile.id}" data-name="${profile.name}">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        profilesContainer.appendChild(profileCard);
    });

    // Add event listeners to buttons
    document.querySelectorAll('.view-wall-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const handle = btn.getAttribute('data-handle');
            window.open(`/wall/${handle}`, '_blank');
        });
    });

    document.querySelectorAll('.edit-profile-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const profile = JSON.parse(btn.getAttribute('data-profile'));
            openEditModal(profile);
        });
    });

    document.querySelectorAll('.delete-profile-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const id = btn.getAttribute('data-id');
            const name = btn.getAttribute('data-name');
            openDeleteModal(id, name);
        });
    });
}

// Calculate Age
function calculateAge(dob) {
    const birthDate = new Date(dob);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }

    return age;
}

// Open Edit Modal
function openEditModal(profile) {
    document.getElementById('edit-profile-id').value = profile.id;
    document.getElementById('edit-name').value = profile.name;
    document.getElementById('edit-handle').value = profile.handle;
    document.getElementById('edit-dob').value = profile.dob;
    document.getElementById('edit-bio').value = profile.bio || '';
    document.getElementById('edit-is_public').checked = profile.is_public;

    editProfileModal.show();
}

// Open Delete Modal
function openDeleteModal(id, name) {
    document.getElementById('delete-profile-id').value = id;
    document.getElementById('delete-profile-name').textContent = name;

    deleteConfirmModal.show();
}

// Save Profile
saveProfileBtn.addEventListener('click', async () => {
    const name = document.getElementById('name').value;
    const handle = document.getElementById('handle').value;
    const dob = document.getElementById('dob').value;
    const bio = document.getElementById('bio').value;
    const isPublic = document.getElementById('is_public').checked;

    // Validate handle format
    const handleRegex = /^[a-zA-Z0-9_]+$/;
    if (!handleRegex.test(handle)) {
        alert('Handle can only contain letters, numbers, and underscores. No spaces or special characters.');
        return;
    }

    try {
        const token = localStorage.getItem('access_token');
        const response = await fetch(`${API_BASE_URL}/children/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                name,
                handle,
                dob,
                bio,
                is_public: isPublic
            })
        });

        const data = await response.json();

        if (response.ok) {
            showResponse({
                message: 'Child profile created successfully!',
                profile: data
            });

            // Close modal and reset form
            addProfileModal.hide();
            addProfileForm.reset();

            // Reload profiles
            loadChildProfiles();
        } else {
            showResponse(data, true);
        }
    } catch (error) {
        showResponse(`Error: ${error.message}`, true);
    }
});

// Update Profile
updateProfileBtn.addEventListener('click', async () => {
    const id = document.getElementById('edit-profile-id').value;
    const name = document.getElementById('edit-name').value;
    const handle = document.getElementById('edit-handle').value;
    const dob = document.getElementById('edit-dob').value;
    const bio = document.getElementById('edit-bio').value;
    const isPublic = document.getElementById('edit-is_public').checked;

    // Validate handle format
    const handleRegex = /^[a-zA-Z0-9_]+$/;
    if (!handleRegex.test(handle)) {
        alert('Handle can only contain letters, numbers, and underscores. No spaces or special characters.');
        return;
    }

    try {
        const token = localStorage.getItem('access_token');
        const response = await fetch(`${API_BASE_URL}/children/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                name,
                handle,
                dob,
                bio,
                is_public: isPublic
            })
        });

        const data = await response.json();

        if (response.ok) {
            showResponse({
                message: 'Child profile updated successfully!',
                profile: data
            });

            // Close modal
            editProfileModal.hide();

            // Reload profiles
            loadChildProfiles();
        } else {
            showResponse(data, true);
        }
    } catch (error) {
        showResponse(`Error: ${error.message}`, true);
    }
});

// Delete Profile
confirmDeleteBtn.addEventListener('click', async () => {
    const id = document.getElementById('delete-profile-id').value;

    try {
        const token = localStorage.getItem('access_token');
        const response = await fetch(`${API_BASE_URL}/children/${id}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        const data = await response.json();

        if (response.ok) {
            showResponse({
                message: 'Child profile deleted successfully!'
            });

            // Close modal
            deleteConfirmModal.hide();

            // Reload profiles
            loadChildProfiles();
        } else {
            showResponse(data, true);
        }
    } catch (error) {
        showResponse(`Error: ${error.message}`, true);
    }
});

// Logout
logoutBtn.addEventListener('click', () => {
    localStorage.removeItem('access_token');
    window.location.href = '/auth';
});
