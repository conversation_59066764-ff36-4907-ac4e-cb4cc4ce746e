from sqlalchemy.orm import Session
from app.database import SessionLocal, engine, Base
from app import models
import uuid
from datetime import datetime, timezone

def create_test_data():
    """Create test data in the database"""
    print("=== Creating Test Data ===")
    
    # Recreate all tables
    print("Recreating database tables...")
    Base.metadata.drop_all(engine)
    Base.metadata.create_all(engine)
    
    db = SessionLocal()
    
    try:
        # Create a parent user
        parent = models.User(
            id=uuid.uuid4(),
            name="<PERSON>",
            email="<EMAIL>",
            user_type=models.UserType.parent,
            phone_number="************",
            address="123 Main St",
            city="Anytown",
            state="CA",
            postal_code="12345",
            country="USA"
        )
        db.add(parent)
        db.commit()
        db.refresh(parent)
        print(f"Created parent user: {parent.name} ({parent.id})")
        
        # Create a child profile
        child = models.ChildProfile(
            id=uuid.uuid4(),
            parent_id=parent.id,
            name="<PERSON>",
            age=10,
            handle="jane_doe",
            is_public=True,
            bio="I love investing and learning about money!"
        )
        db.add(child)
        db.commit()
        db.refresh(child)
        print(f"Created child profile: {child.name} (@{child.handle})")
        
        # Create a completed gift
        gift = models.Gift(
            id=uuid.uuid4(),
            child_profile_id=child.id,
            from_name="Uncle Bob",
            from_email="<EMAIL>",
            amount_usd=100.00,
            message="Happy birthday!",
            payment_status="completed",
            is_anonymous=False,
            created_at=datetime.now(timezone.utc)
        )
        db.add(gift)
        db.commit()
        db.refresh(gift)
        print(f"Created gift: ${gift.amount_usd} from {gift.from_name}")
        
        print("\nSetup completed successfully!")
        print(f"Parent: {parent.name} ({parent.id})")
        print(f"Child: {child.name} (@{child.handle})")
        print(f"\nYou can now access the gift wall at: http://localhost:8082/wall/{child.handle}")
        
    except Exception as e:
        db.rollback()
        print(f"Error creating test data: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    create_test_data()
