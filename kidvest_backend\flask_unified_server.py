from flask import Flask, send_from_directory, redirect, url_for, request, jsonify
import os
import logging
import sys

# Configure logging to stdout
logging.basicConfig(level=logging.DEBUG,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    handlers=[logging.StreamHandler(sys.stdout)])
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Define the base directory
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# Define the UI directories
UI_DIRS = {
    "auth": os.path.join(BASE_DIR, "auth-ui"),
    "onboarding": os.path.join(BASE_DIR, "onboarding-ui"),
    "child-profile": os.path.join(BASE_DIR, "child-profile-ui"),
    "dashboard": os.path.join(BASE_DIR, "dashboard-ui"),
    "gift-wall": os.path.join(BASE_DIR, "gift-wall-ui")
}

# Verify all directories exist
for name, path in UI_DIRS.items():
    if not os.path.exists(path):
        logger.warning(f"UI directory {name} not found at {path}")
    else:
        logger.info(f"Found UI directory {name} at {path}")

# Root route - redirect to auth by default
@app.route('/')
def index():
    logger.info("Accessing root route, redirecting to auth")
    print("Accessing root route, redirecting to auth")
    return redirect('/auth')

# Auth routes
@app.route('/auth')
@app.route('/auth/')
def auth():
    logger.info("Serving auth UI")
    print(f"Serving auth UI from {UI_DIRS['auth']}")
    try:
        return send_from_directory(UI_DIRS["auth"], "index.html")
    except Exception as e:
        print(f"Error serving auth UI: {str(e)}")
        logger.error(f"Error serving auth UI: {str(e)}")
        return jsonify(error="Error serving auth UI", details=str(e)), 500

@app.route('/auth/<path:path>')
def auth_files(path):
    logger.info(f"Serving auth file: {path}")
    print(f"Serving auth file: {path} from {UI_DIRS['auth']}")
    try:
        return send_from_directory(UI_DIRS["auth"], path)
    except Exception as e:
        print(f"Error serving auth file {path}: {str(e)}")
        logger.error(f"Error serving auth file {path}: {str(e)}")
        return jsonify(error=f"Error serving auth file {path}", details=str(e)), 500

# Dashboard routes
@app.route('/dashboard')
@app.route('/dashboard/')
def dashboard():
    logger.info("Serving dashboard UI")
    return send_from_directory(UI_DIRS["dashboard"], "index.html")

@app.route('/dashboard/<path:path>')
def dashboard_files(path):
    logger.info(f"Serving dashboard file: {path}")
    return send_from_directory(UI_DIRS["dashboard"], path)

# Onboarding routes
@app.route('/onboarding')
@app.route('/onboarding/')
def onboarding():
    logger.info("Serving onboarding UI")
    return send_from_directory(UI_DIRS["onboarding"], "index.html")

@app.route('/onboarding/<path:path>')
def onboarding_files(path):
    logger.info(f"Serving onboarding file: {path}")
    return send_from_directory(UI_DIRS["onboarding"], path)

# Child profile routes
@app.route('/child-profile')
@app.route('/child-profile/')
def child_profile():
    logger.info("Serving child profile UI")
    return send_from_directory(UI_DIRS["child-profile"], "index.html")

@app.route('/child-profile/<path:path>')
def child_profile_files(path):
    logger.info(f"Serving child profile file: {path}")
    return send_from_directory(UI_DIRS["child-profile"], path)

# Gift wall routes
@app.route('/gift-wall')
@app.route('/gift-wall/')
def gift_wall():
    logger.info("Serving gift wall UI")
    return send_from_directory(UI_DIRS["gift-wall"], "index.html")

@app.route('/gift-wall/<path:path>')
def gift_wall_files(path):
    logger.info(f"Serving gift wall file: {path}")
    return send_from_directory(UI_DIRS["gift-wall"], path)

# Wall routes for direct gift wall access
@app.route('/wall/<path:handle>')
def wall(handle):
    logger.info(f"Serving gift wall for handle: {handle}")
    # This would typically redirect to a specific gift wall page
    # For now, we'll just serve the gift wall UI
    return send_from_directory(UI_DIRS["gift-wall"], "index.html")

# Error handlers
@app.errorhandler(404)
def page_not_found(e):
    logger.error(f"404 error: {request.path}")
    return jsonify(error="Page not found", path=request.path), 404

@app.errorhandler(500)
def server_error(e):
    logger.error(f"500 error: {str(e)}")
    return jsonify(error="Internal server error", details=str(e)), 500

if __name__ == '__main__':
    logger.info("Starting unified server...")
    logger.info("Available UIs:")
    logger.info(f"  - Auth: http://localhost:8080/auth/")
    logger.info(f"  - Dashboard: http://localhost:8080/dashboard/")
    logger.info(f"  - Onboarding: http://localhost:8080/onboarding/")
    logger.info(f"  - Child Profiles: http://localhost:8080/child-profile/")
    logger.info(f"  - Gift Walls: http://localhost:8080/gift-wall/")

    # Run the Flask app
    app.run(host='0.0.0.0', port=8080, debug=True)
