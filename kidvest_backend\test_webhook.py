import os
import stripe
from dotenv import load_dotenv
import json
import uuid

# Load environment variables
load_dotenv()

# Set Stripe API key
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

def test_webhook_endpoint():
    """Test the webhook endpoint by creating a test event"""
    print("=== Testing Webhook Endpoint ===")
    
    # Get the webhook endpoint URL from ngrok
    ngrok_url = input("Enter the ngrok URL (e.g., https://xxxx-xx-xx.ngrok-free.app): ")
    webhook_url = f"{ngrok_url}/webhook/"
    
    print(f"Webhook URL: {webhook_url}")
    
    # Check if webhook endpoint already exists
    existing_webhooks = stripe.WebhookEndpoint.list(limit=100)
    
    webhook_endpoint = None
    for webhook in existing_webhooks.data:
        if webhook.url == webhook_url:
            webhook_endpoint = webhook
            print(f"Found existing webhook endpoint: {webhook.id}")
            break
    
    # Create a new webhook endpoint if it doesn't exist
    if not webhook_endpoint:
        print("Creating new webhook endpoint...")
        webhook_endpoint = stripe.WebhookEndpoint.create(
            url=webhook_url,
            enabled_events=[
                "checkout.session.completed",
                "checkout.session.expired",
                "payment_intent.succeeded",
                "payment_intent.payment_failed"
            ],
            description="KidVest webhook endpoint"
        )
        print(f"Created webhook endpoint: {webhook_endpoint.id}")
    
    # Get the webhook secret
    webhook_secret = webhook_endpoint.secret
    print(f"Webhook secret: {webhook_secret}")
    
    # Update the .env file with the webhook secret
    update_env_file("STRIPE_WEBHOOK_SECRET", webhook_secret)
    
    print("\n=== Webhook Endpoint Setup Complete ===")
    print(f"Webhook URL: {webhook_url}")
    print(f"Webhook Secret: {webhook_secret}")
    print("\nMake sure to restart your backend server to apply the changes.")
    
    return webhook_endpoint

def update_env_file(key, value):
    """Update a key in the .env file"""
    try:
        # Read the current .env file
        with open(".env", "r") as f:
            lines = f.readlines()
        
        # Check if the key already exists
        key_exists = False
        for i, line in enumerate(lines):
            if line.startswith(f"{key}="):
                lines[i] = f"{key}={value}\n"
                key_exists = True
                break
        
        # Add the key if it doesn't exist
        if not key_exists:
            lines.append(f"{key}={value}\n")
        
        # Write the updated .env file
        with open(".env", "w") as f:
            f.writelines(lines)
        
        print(f"Updated {key} in .env file")
    except Exception as e:
        print(f"Error updating .env file: {e}")

if __name__ == "__main__":
    test_webhook_endpoint()
