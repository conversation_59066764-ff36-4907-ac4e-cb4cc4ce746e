import { useEffect } from 'react';
import { ActivityIndicator, StyleSheet, View, Text } from 'react-native';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../../context/AuthContext';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';

export default function LogoutScreen() {
  const { signOut } = useAuth();
  const router = useRouter();
  const colorScheme = useColorScheme();

  useEffect(() => {
    const performLogout = async () => {
      try {
        console.log('Logout screen: Performing logout');

        // Clear AsyncStorage directly
        await AsyncStorage.clear();
        console.log('Logout screen: AsyncStorage cleared');

        // Call signOut function from AuthContext
        try {
          await signOut();
          console.log('Logout screen: signOut function completed');
        } catch (error) {
          console.error('Logout screen: Error in signOut function:', error);
          // Continue with logout even if signOut fails
        }

        // Navigate to login screen
        console.log('Logout screen: Navigating to login screen');
        setTimeout(() => {
          router.replace('/auth/login');
        }, 500);
      } catch (error) {
        console.error('Logout screen: Error during logout:', error);
        // Navigate to login screen even if there's an error
        router.replace('/auth/login');
      }
    };

    performLogout();
  }, []);

  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color={Colors[colorScheme].primary} />
      <Text style={styles.text}>Logging out...</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  text: {
    marginTop: 16,
    fontSize: 16,
    color: '#666666',
  },
});
