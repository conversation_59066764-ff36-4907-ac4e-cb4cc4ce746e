# KidVest Data Models Documentation

## Overview

This document provides a detailed description of all data models used in the KidVest application. Kid<PERSON>est uses SQLAlchemy ORM with PostgreSQL for database operations.

## Database Models

### User

The User model represents parent users who can create and manage child profiles.

| Field | Type | Description | Constraints |
|-------|------|-------------|------------|
| id | UUID | Unique identifier | Primary Key, Default: uuid4() |
| name | String | User's full name | Not Null |
| email | String | User's email address | Not Null, Unique |
| hashed_password | String | Hashed password | Not Null |
| user_type | Enum | Type of user (parent/child) | Not Null |
| created_at | DateTime | Account creation timestamp | Default: utc_now() |
| is_active | Boolean | Account status | Default: True |
| phone_number | String | User's phone number | Nullable |
| address | String | Street address | Nullable |
| city | String | City | Nullable |
| state | String | State/Province | Nullable |
| postal_code | String | ZIP/Postal code | Nullable |
| country | String | Country | Nullable |

**Relationships:**
- One-to-many with `ChildProfile` (parent -> children)
- One-to-many with `BrokerAccount` (user -> broker_accounts)

### ChildProfile

The ChildProfile model represents children's profiles created by parents.

| Field | Type | Description | Constraints |
|-------|------|-------------|------------|
| id | UUID | Unique identifier | Primary Key, Default: uuid4() |
| parent_id | UUID | Reference to parent user | Foreign Key (users.id), Not Null |
| name | String | Child's name | Not Null |
| age | Integer | Child's age | Nullable |
| handle | String | Unique handle for gift wall | Not Null, Unique |
| is_public | Boolean | Whether profile is publicly visible | Default: True |
| avatar | String | Avatar image URL | Nullable |
| bio | String | Short biography | Nullable |
| dob | String | Date of birth (YYYY-MM-DD) | Nullable |
| created_at | DateTime | Profile creation timestamp | Default: utc_now() |
| updated_at | DateTime | Profile update timestamp | Default: utc_now(), onupdate: utc_now() |

**Relationships:**
- Many-to-one with `User` (children -> parent)
- One-to-many with `Gift` (child_profile -> gifts)
- One-to-many with `Investment` (child_profile -> investments)

### Gift

The Gift model represents monetary gifts sent to children.

| Field | Type | Description | Constraints |
|-------|------|-------------|------------|
| id | UUID | Unique identifier | Primary Key, Default: uuid4() |
| child_profile_id | UUID | Reference to child profile | Foreign Key (child_profiles.id), Not Null |
| from_name | String | Gift sender's name | Nullable |
| from_email | String | Gift sender's email | Nullable |
| amount_usd | Float | Gift amount in USD | Not Null |
| message | Text | Gift message | Nullable |
| payment_status | String | Payment status (pending/completed/failed) | Default: "pending" |
| payment_intent_id | String | Stripe payment intent ID | Nullable |
| checkout_session_id | String | Stripe checkout session ID | Nullable |
| is_anonymous | Boolean | Whether gift is anonymous | Default: False |
| created_at | DateTime | Gift creation timestamp | Default: utc_now() |
| updated_at | DateTime | Gift update timestamp | Default: utc_now(), onupdate: utc_now() |

**Relationships:**
- Many-to-one with `ChildProfile` (gifts -> child_profile)
- One-to-many with `Investment` (gift -> investments)

### Investment

The Investment model represents investments made with gift money.

| Field | Type | Description | Constraints |
|-------|------|-------------|------------|
| id | UUID | Unique identifier | Primary Key, Default: uuid4() |
| child_profile_id | UUID | Reference to child profile | Foreign Key (child_profiles.id), Not Null |
| gift_id | UUID | Reference to gift | Foreign Key (gifts.id), Nullable |
| amount_usd | Float | Investment amount in USD | Not Null |
| symbol | String | Stock symbol | Not Null |
| shares | Float | Number of shares | Not Null |
| purchase_price | Float | Price per share in USD | Not Null |
| status | String | Investment status (pending/completed/failed) | Default: "pending" |
| transaction_id | String | Broker transaction ID | Nullable |
| created_at | DateTime | Investment creation timestamp | Default: utc_now() |
| updated_at | DateTime | Investment update timestamp | Default: utc_now(), onupdate: utc_now() |

**Relationships:**
- Many-to-one with `ChildProfile` (investments -> child_profile)
- Many-to-one with `Gift` (investments -> gift)

### BrokerAccount

The BrokerAccount model represents brokerage accounts linked to users.

| Field | Type | Description | Constraints |
|-------|------|-------------|------------|
| id | UUID | Unique identifier | Primary Key, Default: uuid4() |
| user_id | UUID | Reference to user | Foreign Key (users.id), Not Null |
| broker_type | String | Type of broker (e.g., "alpaca") | Not Null |
| external_account_id | String | Account ID in the broker's system | Nullable |
| status | String | Account status (pending/active/rejected) | Not Null, Default: "pending" |
| created_at | DateTime | Account creation timestamp | Default: utc_now() |
| updated_at | DateTime | Account update timestamp | Default: utc_now(), onupdate: utc_now() |

**Relationships:**
- Many-to-one with `User` (broker_accounts -> user)

### OnboardingSession

The OnboardingSession model represents multi-step KYC onboarding sessions.

| Field | Type | Description | Constraints |
|-------|------|-------------|------------|
| id | UUID | Unique identifier | Primary Key, Default: uuid4() |
| session_token | String | Unique session token | Not Null, Unique |
| user_id | UUID | Reference to user | Foreign Key (users.id), Nullable |
| current_step | Integer | Current onboarding step | Default: 1 |
| step1_completed_at | DateTime | Step 1 completion timestamp | Nullable |
| step2_completed_at | DateTime | Step 2 completion timestamp | Nullable |
| step3_completed_at | DateTime | Step 3 completion timestamp | Nullable |
| step4_completed_at | DateTime | Step 4 completion timestamp | Nullable |
| step1_data | Text | Step 1 data (JSON string) | Nullable |
| step2_data | Text | Step 2 data (JSON string) | Nullable |
| step3_data | Text | Step 3 data (JSON string) | Nullable |
| step4_data | Text | Step 4 data (JSON string) | Nullable |
| created_at | DateTime | Session creation timestamp | Default: utc_now() |
| updated_at | DateTime | Session update timestamp | Default: utc_now(), onupdate: utc_now() |
| expires_at | DateTime | Session expiration timestamp | Nullable |

**Relationships:**
- Many-to-one with `User` (onboarding_sessions -> user)

## Pydantic Schemas

### User Schemas

#### UserBase
- name: str
- email: EmailStr
- user_type: UserType

#### UserCreate (extends UserBase)
- password: str
- phone_number: Optional[str]
- address: Optional[str]
- city: Optional[str]
- state: Optional[str]
- postal_code: Optional[str]
- country: Optional[str]

#### UserUpdate
- name: Optional[str]
- email: Optional[EmailStr]
- phone_number: Optional[str]
- address: Optional[str]
- city: Optional[str]
- state: Optional[str]
- postal_code: Optional[str]
- country: Optional[str]
- is_active: Optional[bool]

#### UserResponse (extends UserBase)
- id: UUID
- created_at: datetime
- is_active: bool
- phone_number: Optional[str]
- address: Optional[str]
- city: Optional[str]
- state: Optional[str]
- postal_code: Optional[str]
- country: Optional[str]

### Child Profile Schemas

#### ChildProfileBase
- name: str
- age: Optional[int]
- handle: str
- is_public: bool = True
- bio: Optional[str]
- dob: Optional[date]

#### ChildProfileCreate (extends ChildProfileBase)
- (No additional fields)

#### ChildProfileUpdate
- name: Optional[str]
- age: Optional[int]
- handle: Optional[str]
- is_public: Optional[bool]
- bio: Optional[str]
- avatar: Optional[str]
- dob: Optional[date]

#### ChildProfileResponse (extends ChildProfileBase)
- id: UUID
- parent_id: UUID
- avatar: Optional[str]
- created_at: datetime
- updated_at: datetime

### Gift Schemas

#### GiftBase
- from_name: Optional[str]
- from_email: Optional[EmailStr]
- amount_usd: float (> 0)
- message: Optional[str]
- is_anonymous: bool = False

#### GiftCreate (extends GiftBase)
- child_profile_id: UUID

#### GiftWallCreate (extends GiftBase)
- child_profile_handle: str

#### GiftUpdate
- payment_status: Optional[str]
- payment_intent_id: Optional[str]
- checkout_session_id: Optional[str]

#### GiftResponse (extends GiftBase)
- id: UUID
- child_profile_id: UUID
- payment_status: str
- created_at: datetime
- updated_at: datetime

### Investment Schemas

#### InvestmentBase
- amount_usd: float (> 0)
- symbol: str
- shares: float (> 0)
- purchase_price: float (> 0)

#### InvestmentCreate (extends InvestmentBase)
- child_profile_id: UUID
- gift_id: Optional[UUID]

#### InvestmentUpdate
- status: Optional[str]
- transaction_id: Optional[str]

#### InvestmentResponse (extends InvestmentBase)
- id: UUID
- child_profile_id: UUID
- gift_id: Optional[UUID]
- status: str
- transaction_id: Optional[str]
- created_at: datetime
- updated_at: datetime

### Onboarding Schemas

#### OnboardingStep1
- full_name: str
- email: EmailStr

#### OnboardingStep2
- dob: date
- phone_number: str
- street_address: str
- city: str
- state: str
- postal_code: str
- country: str = "USA"
- ssn: str
- session_token: Optional[str]

#### OnboardingStep3
- employment_status: str
- income_range: str
- net_worth_range: str
- funding_source: str
- investment_experience: str
- risk_tolerance: str
- session_token: Optional[str]

#### OnboardingStep4
- is_control_person: bool = False
- is_affiliated_exchange_or_finra: bool = False
- is_politically_exposed: bool = False
- immediate_family_exposed: bool = False
- customer_agreement_accepted: bool = False
- margin_agreement_accepted: bool = False
- account_agreement_accepted: bool = False
- session_token: Optional[str]
