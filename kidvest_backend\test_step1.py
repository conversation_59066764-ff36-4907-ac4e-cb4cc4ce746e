import requests
import json
import uuid
import datetime

# Base URL for API
BASE_URL = "http://localhost:8000/api/onboarding"

# Generate unique data for testing
timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
unique_id = str(uuid.uuid4())[:8]
unique_email = f"test.user.{timestamp}.{unique_id}@example.com"

# Step 1: Basic account creation
step1_data = {
    "full_name": "<PERSON>",
    "email": unique_email
}

print(f"Step 1: Using unique email: {unique_email}")
response = requests.post(f"{BASE_URL}/step1", json=step1_data)
print(f"Status Code: {response.status_code}")
print(f"Response Text: {response.text}")
try:
    step1_response = response.json()
    print(f"Response JSON: {json.dumps(step1_response, indent=2)}")
except Exception as e:
    print(f"Error parsing JSON: {str(e)}")
