from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel, EmailStr
from typing import Optional
import uvicorn
import webbrowser
import threading
import time

# Create FastAPI app
app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# User model
class UserCreate(BaseModel):
    name: str
    email: EmailStr
    password: str
    user_type: str
    phone_number: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None

class UserResponse(BaseModel):
    id: str
    name: str
    email: str
    user_type: str
    is_active: bool = True
    created_at: str

# Root route
@app.get("/")
def read_root():
    print("Root endpoint called")
    return {"message": "Welcome to Standalone Test Backend!"}

# Register route
@app.post("/api/register", response_model=UserResponse)
async def register_user(user_data: UserCreate):
    print(f"Registration request received: {user_data.name}, {user_data.email}")

    # Simulate successful registration
    return {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": user_data.name,
        "email": user_data.email,
        "user_type": user_data.user_type,
        "is_active": True,
        "created_at": "2023-04-19T12:00:00"
    }

# Create HTML test page
HTML_PAGE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Standalone Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        #response {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Standalone Test</h1>

    <div class="form-group">
        <label for="name">Full Name</label>
        <input type="text" id="name" value="Test User">
    </div>

    <div class="form-group">
        <label for="email">Email</label>
        <input type="email" id="email" value="<EMAIL>">
    </div>

    <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" value="password123">
    </div>

    <button onclick="testRegister()">Test Register</button>

    <div id="response"></div>

    <script>
        async function testRegister() {
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            const responseDiv = document.getElementById('response');
            responseDiv.textContent = 'Sending request...';

            const data = {
                name,
                email,
                password,
                user_type: 'parent'
            };

            try {
                console.log('Sending request to: http://localhost:8002/api/register');
                console.log('Request data:', data);

                const response = await fetch('http://localhost:8002/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                console.log('Response status:', response.status);

                try {
                    const responseData = await response.json();
                    console.log('Response data:', responseData);
                    responseDiv.textContent = JSON.stringify(responseData, null, 2);
                } catch (jsonError) {
                    console.error('Error parsing JSON:', jsonError);
                    const responseText = await response.text();
                    responseDiv.textContent = `Status: ${response.status}\nResponse: ${responseText}`;
                }
            } catch (error) {
                console.error('Error:', error);
                responseDiv.textContent = `Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
"""

# Serve HTML page
@app.get("/test", response_class=HTMLResponse)
async def get_test_page():
    return HTMLResponse(content=HTML_PAGE)

# Open browser after server starts
def open_browser():
    time.sleep(2)  # Wait for server to start
    webbrowser.open("http://localhost:8002/test")

if __name__ == "__main__":
    # Start browser in a separate thread
    threading.Thread(target=open_browser).start()

    # Start server
    uvicorn.run(app, host="0.0.0.0", port=8002)
