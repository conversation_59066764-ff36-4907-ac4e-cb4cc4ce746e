import os
import requests
import json
import base64
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_alpaca_connection():
    # Use the correct Broker API endpoint
    url = "https://broker-api.sandbox.alpaca.markets/v1/accounts"
    
    # Get API keys from environment variables
    api_key = os.getenv("ALPACA_API_KEY")
    api_secret = os.getenv("ALPACA_SECRET_KEY")
    
    if not api_key or not api_secret:
        print("ERROR: Alpaca API keys not found in environment variables")
        return
    
    # Create HTTP Basic Authentication header
    auth_string = f"{api_key}:{api_secret}"
    encoded_auth = base64.b64encode(auth_string.encode()).decode()
    
    headers = {
        "Authorization": f"Basic {encoded_auth}",
        "Content-Type": "application/json"
    }
    
    # Simple test payload
    test_payload = {
        "contact": {
            "email_address": "<EMAIL>",
            "phone_number": "************",
            "street_address": ["123 Main St"],
            "city": "New York",
            "state": "NY",
            "postal_code": "10001",
            "country": "USA"
        },
        "identity": {
            "given_name": "<PERSON>",
            "family_name": "Doe",
            "date_of_birth": "1990-01-01",
            "tax_id": "***********",
            "tax_id_type": "USA_SSN",
            "country_of_citizenship": "USA",
            "country_of_birth": "USA",
            "country_of_tax_residence": "USA",
            "funding_source": ["employment_income"],
            "annual_income_min": "50000",
            "annual_income_max": "100000",
            "liquid_net_worth_min": "50000",
            "liquid_net_worth_max": "100000",
            "total_net_worth_min": "50000",
            "total_net_worth_max": "100000"
        },
        "disclosures": {
            "is_control_person": False,
            "is_affiliated_exchange_or_finra": False,
            "is_politically_exposed": False,
            "immediate_family_exposed": False
        },
        "agreements": [
            {
                "agreement": "customer_agreement",
                "signed_at": "2023-01-01T00:00:00Z",
                "ip_address": "127.0.0.1"
            },
            {
                "agreement": "margin_agreement",
                "signed_at": "2023-01-01T00:00:00Z",
                "ip_address": "127.0.0.1"
            },
            {
                "agreement": "account_agreement",
                "signed_at": "2023-01-01T00:00:00Z",
                "ip_address": "127.0.0.1"
            }
        ],
        "documents": [],
        "trusted_contact": {
            "given_name": "Jane",
            "family_name": "Doe",
            "email_address": "<EMAIL>"
        },
        "enabled_assets": ["us_equity"],
        "account_type": "trading"
    }
    
    print("\n==== TESTING ALPACA BROKER API CONNECTION ====")
    print(f"API Key: {api_key[:5]}...{api_key[-5:]}")
    print(f"API Secret: {api_secret[:5]}...{api_secret[-5:]}")
    print("Request URL:", url)
    
    try:
        # First, try a simple GET request to check authentication
        print("\n==== TESTING AUTHENTICATION ====")
        auth_response = requests.get(
            "https://broker-api.sandbox.alpaca.markets/v1/accounts", 
            headers=headers,
            timeout=30
        )
        
        print("Authentication Status Code:", auth_response.status_code)
        if auth_response.status_code == 200:
            print("Authentication successful!")
        else:
            print("Authentication failed!")
            try:
                print("Response:", json.dumps(auth_response.json(), indent=2))
            except:
                print("Response:", auth_response.text)
        
        # Now try the actual account creation
        print("\n==== TESTING ACCOUNT CREATION ====")
        response = requests.post(url, headers=headers, json=test_payload, timeout=30)
        
        print("Response Status Code:", response.status_code)
        print("Response Headers:", json.dumps(dict(response.headers), indent=2))
        
        # Get the request ID for troubleshooting
        request_id = response.headers.get("X-Request-ID", "Unknown")
        print(f"Request ID: {request_id} (save this for troubleshooting)")
        
        try:
            response_json = response.json()
            print("Response Body:", json.dumps(response_json, indent=2))
            
            if response.status_code in [200, 201]:
                print("\n✅ Account creation successful!")
            else:
                print("\n❌ Account creation failed!")
                
        except json.JSONDecodeError:
            print("Response Body (not JSON):", response.text)
            
    except requests.exceptions.RequestException as e:
        print("\n==== CONNECTION ERROR ====")
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    test_alpaca_connection()
