#!/usr/bin/env python3

import requests
import json
import time
from datetime import datetime

def trace_ui_payment_flow():
    """Trace the exact flow when creating a gift through UI"""
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🔍 Tracing UI Payment Flow")
    print("=" * 60)
    
    # Step 1: Create gift through UI API (same as React Native)
    print("🎁 Step 1: Creating gift through UI API...")
    
    test_handle = "test-child-165125"
    gift_data = {
        "child_profile_handle": test_handle,
        "from_name": "UI Flow Tracer",
        "from_email": "<EMAIL>",
        "amount_usd": 12.0,
        "message": "Tracing UI payment flow",
        "is_anonymous": False
    }
    
    try:
        # This is the exact API call that React Native makes
        api_url = f"http://localhost:8000/api/wall/{test_handle}/gift"
        print(f"📡 API URL: {api_url}")
        print(f"📋 Request Data: {json.dumps(gift_data, indent=2)}")
        
        response = requests.post(
            api_url,
            json=gift_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            response_data = response.json()
            gift_id = response_data.get('gift_id')
            checkout_url = response_data.get('checkout_url')
            
            print(f"✅ Gift created successfully!")
            print(f"🆔 Gift ID: {gift_id}")
            print(f"🔗 Checkout URL: {checkout_url}")
            
            # Extract Stripe session ID
            if checkout_url and 'checkout.stripe.com' in checkout_url:
                session_id = checkout_url.split('/')[-1].split('#')[0]
                print(f"🎫 Stripe Session ID: {session_id}")
                
                return gift_id, checkout_url, session_id
            else:
                print(f"❌ Invalid checkout URL format")
                return None, None, None
                
        else:
            print(f"❌ Gift creation failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"❌ Error: {json.dumps(error_data, indent=2)}")
            except:
                print(f"❌ Error text: {response.text}")
            return None, None, None
            
    except Exception as e:
        print(f"❌ Error in gift creation: {str(e)}")
        return None, None, None

def check_stripe_session_metadata(session_id):
    """Check if the Stripe session has the correct metadata"""
    print(f"\n🔍 Step 2: Checking Stripe Session Metadata...")
    
    try:
        import stripe
        import os
        from dotenv import load_dotenv
        
        load_dotenv()
        stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
        
        # Retrieve the session from Stripe
        session = stripe.checkout.Session.retrieve(session_id)
        
        print(f"✅ Retrieved Stripe session: {session.id}")
        print(f"📊 Payment Status: {session.payment_status}")
        print(f"💰 Amount Total: ${session.amount_total / 100}")
        print(f"🏷️ Metadata: {session.metadata}")
        
        # Check if gift_id is in metadata
        gift_id_in_metadata = session.metadata.get('gift_id')
        if gift_id_in_metadata:
            print(f"✅ Gift ID found in metadata: {gift_id_in_metadata}")
            return True, gift_id_in_metadata
        else:
            print(f"❌ No gift_id found in metadata!")
            print(f"❌ Available metadata keys: {list(session.metadata.keys())}")
            return False, None
            
    except Exception as e:
        print(f"❌ Error checking Stripe session: {str(e)}")
        return False, None

def check_webhook_endpoint_events():
    """Check what events the webhook endpoint is configured to receive"""
    print(f"\n🔧 Step 3: Checking Webhook Configuration...")
    
    try:
        import stripe
        import os
        from dotenv import load_dotenv
        
        load_dotenv()
        stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
        
        # Get webhook endpoints
        webhook_endpoints = stripe.WebhookEndpoint.list()
        
        print(f"📋 Found {len(webhook_endpoints.data)} webhook endpoints:")
        
        for endpoint in webhook_endpoints.data:
            print(f"\n🔗 Endpoint: {endpoint.url}")
            print(f"📊 Status: {endpoint.status}")
            print(f"🎯 Events: {endpoint.enabled_events}")
            
            # Check if our ngrok URL is configured
            if 'ngrok' in endpoint.url or 'localhost' in endpoint.url:
                print(f"✅ This looks like your development endpoint")
                
                # Check if checkout.session.completed is enabled
                if 'checkout.session.completed' in endpoint.enabled_events:
                    print(f"✅ checkout.session.completed event is enabled")
                else:
                    print(f"❌ checkout.session.completed event is NOT enabled")
                    print(f"❌ You need to add this event to your webhook")
                    
                return True
        
        print(f"⚠️ No development webhook endpoint found")
        print(f"⚠️ You may need to create a webhook endpoint")
        return False
        
    except Exception as e:
        print(f"❌ Error checking webhook endpoints: {str(e)}")
        return False

def monitor_backend_logs():
    """Instructions for monitoring backend logs during payment"""
    print(f"\n📋 Step 4: Backend Log Monitoring Instructions")
    print("-" * 40)
    
    print(f"To trace the webhook flow:")
    print(f"1. 🖥️ Watch your backend terminal for these logs:")
    print(f"   • 'Creating Stripe checkout session with amount: X cents'")
    print(f"   • 'Stripe checkout session created: cs_...'")
    print(f"   • 'Checkout URL: https://checkout.stripe.com/...'")
    print(f"2. 🌐 Complete payment in Stripe checkout")
    print(f"3. 👀 Watch for webhook logs:")
    print(f"   • '=== Stripe Webhook Received ==='")
    print(f"   • 'Received Stripe webhook event: checkout.session.completed'")
    print(f"   • '🎯 Checkout session completed!'")
    print(f"   • 'Gift ID from metadata: ...'")
    print(f"   • '✅ Updated gift ... to completed'")
    print(f"4. 🔍 Check ngrok web interface: http://localhost:4040")

def check_gift_in_database(gift_id):
    """Check the gift status in database"""
    print(f"\n💾 Step 5: Checking Gift in Database...")
    
    if not gift_id:
        print(f"❌ No gift ID provided")
        return None
    
    try:
        from app.database import SessionLocal
        from app import models
        from uuid import UUID
        
        db = SessionLocal()
        try:
            gift = db.query(models.Gift).filter(models.Gift.id == UUID(gift_id)).first()
            
            if gift:
                print(f"✅ Gift found in database")
                print(f"🆔 Gift ID: {gift.id}")
                print(f"📊 Payment Status: {gift.payment_status}")
                print(f"💰 Amount: ${gift.amount_usd}")
                print(f"👤 From: {gift.from_name} ({gift.from_email})")
                print(f"🎫 Checkout Session ID: {gift.checkout_session_id}")
                print(f"💳 Payment Intent ID: {gift.payment_intent_id or 'None'}")
                print(f"📅 Created: {gift.created_at}")
                
                return gift.payment_status
            else:
                print(f"❌ Gift not found in database")
                return None
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Error checking gift: {str(e)}")
        return None

def main():
    """Main tracing function"""
    print("🔍 UI Payment Flow Tracer")
    print("=" * 60)
    
    # Step 1: Create gift through UI API
    gift_id, checkout_url, session_id = trace_ui_payment_flow()
    
    if not gift_id:
        print("\n❌ Failed to create gift. Cannot continue tracing.")
        return
    
    # Step 2: Check Stripe session metadata
    metadata_ok, metadata_gift_id = check_stripe_session_metadata(session_id)
    
    # Step 3: Check webhook configuration
    webhook_configured = check_webhook_endpoint_events()
    
    # Step 4: Check initial gift status
    initial_status = check_gift_in_database(gift_id)
    
    # Step 5: Provide monitoring instructions
    monitor_backend_logs()
    
    # Summary and next steps
    print(f"\n" + "=" * 60)
    print(f"🔍 TRACING SUMMARY")
    print(f"=" * 60)
    
    print(f"Gift Creation: {'✅ Success' if gift_id else '❌ Failed'}")
    print(f"Stripe Session: {'✅ Created' if session_id else '❌ Failed'}")
    print(f"Metadata Check: {'✅ Gift ID Present' if metadata_ok else '❌ Missing Gift ID'}")
    print(f"Webhook Config: {'✅ Configured' if webhook_configured else '❌ Needs Setup'}")
    print(f"Initial Status: {initial_status or 'Unknown'}")
    
    if gift_id and session_id and metadata_ok:
        print(f"\n✅ SETUP LOOKS CORRECT!")
        print(f"🎯 The issue might be:")
        print(f"1. 🔧 Webhook endpoint URL mismatch")
        print(f"2. 🔑 Webhook secret mismatch")
        print(f"3. 🎯 Event not enabled (checkout.session.completed)")
        print(f"4. 🌐 ngrok tunnel changed URL")
        
        print(f"\n🧪 Next Steps:")
        print(f"1. Complete payment: {checkout_url}")
        print(f"2. Watch backend logs for webhook activity")
        print(f"3. Check ngrok interface for incoming requests")
        print(f"4. Verify webhook secret matches Stripe dashboard")
        
    else:
        print(f"\n❌ ISSUES DETECTED!")
        if not metadata_ok:
            print(f"🔧 Critical: Gift ID not in Stripe session metadata")
        if not webhook_configured:
            print(f"🔧 Critical: Webhook not properly configured")
    
    print(f"\n🧪 Tracing completed!")

if __name__ == "__main__":
    main()
