import React from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

import { Text, View } from '@/components/Themed';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';
import { useAuth } from '../../context/AuthContext';

export default function OnboardingCompleteScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { completeOnboarding } = useAuth();

  const handleContinue = async () => {
    try {
      console.log('Onboarding complete: Marking onboarding as complete');
      // Mark onboarding as complete in the auth context
      await completeOnboarding();
      console.log('Onboarding complete: Successfully marked as complete');

      // Navigate to the dashboard
      console.log('Onboarding complete: Navigating to dashboard');
      router.replace('/(tabs)');
    } catch (error) {
      console.error('Onboarding complete: Error completing onboarding:', error);
    }
  };

  const handleAddChildProfile = () => {
    router.push('/onboarding/child-profile');
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={[styles.iconContainer, { backgroundColor: Colors[colorScheme].primary + '20' }]}>
          <FontAwesome5 name="check-circle" size={64} color={Colors[colorScheme].primary} />
        </View>

        <Text style={styles.title}>Verification Complete!</Text>

        <Text style={styles.description}>
          Your KYC verification has been successfully completed. You can now create child profiles and start investing.
        </Text>

        <View style={styles.infoContainer}>
          <View style={styles.infoItem}>
            <View style={[styles.infoIconContainer, { backgroundColor: Colors[colorScheme].primary + '20' }]}>
              <FontAwesome5 name="user-check" size={20} color={Colors[colorScheme].primary} />
            </View>
            <View style={styles.infoTextContainer}>
              <Text style={styles.infoTitle}>Identity Verified</Text>
              <Text style={styles.infoDescription}>Your identity has been successfully verified.</Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <View style={[styles.infoIconContainer, { backgroundColor: Colors[colorScheme].primary + '20' }]}>
              <FontAwesome5 name="university" size={20} color={Colors[colorScheme].primary} />
            </View>
            <View style={styles.infoTextContainer}>
              <Text style={styles.infoTitle}>Brokerage Account Created</Text>
              <Text style={styles.infoDescription}>Your brokerage account has been created and is ready to use.</Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <View style={[styles.infoIconContainer, { backgroundColor: Colors[colorScheme].primary + '20' }]}>
              <FontAwesome5 name="child" size={20} color={Colors[colorScheme].primary} />
            </View>
            <View style={styles.infoTextContainer}>
              <Text style={styles.infoTitle}>Add Child Profiles</Text>
              <Text style={styles.infoDescription}>Create profiles for your children to start their investment journey.</Text>
            </View>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.addChildButton, { borderColor: Colors[colorScheme].primary }]}
            onPress={handleAddChildProfile}
          >
            <FontAwesome5 name="child" size={16} color={Colors[colorScheme].primary} />
            <Text style={[styles.addChildButtonText, { color: Colors[colorScheme].primary }]}>Add Child Profile</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.continueButton, { backgroundColor: Colors[colorScheme].primary }]}
            onPress={handleContinue}
          >
            <Text style={styles.continueButtonText}>Continue to Dashboard</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
  },
  content: {
    padding: 24,
    alignItems: 'center',
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 32,
  },
  infoContainer: {
    width: '100%',
    marginBottom: 32,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  infoIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  infoTextContainer: {
    flex: 1,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  infoDescription: {
    fontSize: 14,
    color: '#666',
  },
  buttonContainer: {
    width: '100%',
  },
  addChildButton: {
    flexDirection: 'row',
    height: 48,
    borderRadius: 8,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  addChildButtonText: {
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  continueButton: {
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  continueButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
});
