import requests
import json
import time

def debug_gift_creation():
    """Debug the gift creation API"""
    print("Debugging gift creation API...")
    
    url = "http://127.0.0.1:8000/api/wall/jane_doe/gift"
    data = {
        "child_profile_handle": "jane_doe",
        "from_name": "Test Gifter",
        "from_email": "<EMAIL>",
        "amount_usd": 50,
        "message": "Test gift message",
        "is_anonymous": False
    }
    
    # Print request details
    print(f"Request URL: {url}")
    print(f"Request data: {json.dumps(data, indent=2)}")
    
    try:
        # Make the request with detailed debugging
        print("\nSending request...")
        response = requests.post(url, json=data)
        
        # Print response details
        print(f"\nResponse status code: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        # Try to parse as JSON
        try:
            response_data = response.json()
            print(f"\nResponse data (JSON): {json.dumps(response_data, indent=2)}")
        except json.JSONDecodeError:
            print(f"\nResponse is not valid JSON: {response.text}")
        
        # Check for specific error patterns
        if response.status_code >= 400:
            print("\nError detected in response")
            if "detail" in response_data:
                print(f"Error detail: {response_data['detail']}")
                
                # Check for common error patterns
                if "column" in response_data.get("detail", ""):
                    print("Database schema error detected")
                elif "Stripe" in response_data.get("detail", ""):
                    print("Stripe API error detected")
        
        return response
    except Exception as e:
        print(f"\nException occurred: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    debug_gift_creation()
    
    # Wait a moment to ensure logs are captured
    print("\nWaiting for logs to be captured...")
    time.sleep(2)
    print("Done.")
