from app.database import SessionLocal
from app.models import User
from app.auth import get_password_hash

def check_users():
    """Check if there are any users in the database"""
    db = SessionLocal()
    try:
        users = db.query(User).all()
        print(f"Found {len(users)} users in the database:")
        for user in users:
            print(f"  - {user.id}: {user.name} ({user.email})")
        
        if len(users) == 0:
            print("No users found. Creating a test user...")
            test_user = User(
                name="Test User",
                email="<EMAIL>",
                hashed_password=get_password_hash("password"),
                user_type="parent",
                is_active=True
            )
            db.add(test_user)
            db.commit()
            print(f"Created test user: {test_user.id} ({test_user.email})")
    finally:
        db.close()

if __name__ == "__main__":
    check_users()
