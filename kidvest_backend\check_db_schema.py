import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database connection details from DATABASE_URL
DATABASE_URL = os.getenv("DATABASE_URL")

print("=== Checking Database Schema ===")

# Parse the DATABASE_URL
if DATABASE_URL.startswith("postgresql://"):
    # Format: postgresql://username:password@host:port/database
    url_without_protocol = DATABASE_URL.replace("postgresql://", "")
    user_pass, host_port_db = url_without_protocol.split("@")
    
    if ":" in user_pass:
        username, password = user_pass.split(":")
    else:
        username = user_pass
        password = ""
    
    host_port, database = host_port_db.split("/")
    
    if ":" in host_port:
        host, port = host_port.split(":")
    else:
        host = host_port
        port = "5432"
else:
    print("Invalid DATABASE_URL format")
    exit(1)

def check_db_schema():
    """Check the database schema"""
    try:
        # Connect to the database
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=password
        )
        
        # Create a cursor
        cur = conn.cursor()
        
        # Get all tables
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        
        tables = cur.fetchall()
        print("\n=== Tables in Database ===")
        for table in tables:
            print(f"- {table[0]}")
        
        # For each table, get its columns
        for table in tables:
            table_name = table[0]
            print(f"\n=== Columns in {table_name} ===")
            
            cur.execute(f"""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = '{table_name}'
                ORDER BY ordinal_position
            """)
            
            columns = cur.fetchall()
            for column in columns:
                print(f"- {column[0]}: {column[1]} (Nullable: {column[2]})")
            
            # Check for foreign keys
            cur.execute(f"""
                SELECT
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name
                FROM
                    information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                      ON tc.constraint_name = kcu.constraint_name
                      AND tc.table_schema = kcu.table_schema
                    JOIN information_schema.constraint_column_usage AS ccu
                      ON ccu.constraint_name = tc.constraint_name
                      AND ccu.table_schema = tc.table_schema
                WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_name='{table_name}'
            """)
            
            foreign_keys = cur.fetchall()
            if foreign_keys:
                print("\n  Foreign Keys:")
                for fk in foreign_keys:
                    print(f"  - {fk[0]} references {fk[1]}.{fk[2]}")
        
    except Exception as e:
        print(f"Error checking database schema: {e}")
    finally:
        # Close the cursor and connection
        cur.close()
        conn.close()

if __name__ == "__main__":
    check_db_schema()
    print("\nDone.")
