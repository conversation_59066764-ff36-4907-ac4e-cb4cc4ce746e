import React, { useState, useEffect } from 'react';
import { StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, Image, RefreshControl } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

import { Text, View } from '@/components/Themed';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';
import { useAuth } from '../../context/AuthContext';
import { childProfilesAPI, portfolioAPI } from '../../services/api';

export default function ProfilesScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { user } = useAuth();

  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [profiles, setProfiles] = useState([]);
  const [portfolioData, setPortfolioData] = useState({});

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Fetch child profiles and portfolio data
  const fetchProfilesData = async (showRefresh = false) => {
    try {
      if (showRefresh) setIsRefreshing(true);
      else setIsLoading(true);

      // Try to get real portfolio overview first
      try {
        const overview = await portfolioAPI.getDashboardOverview();
        if (overview && overview.children_portfolios.length > 0) {
          // Use real data from portfolio API
          const profilesWithPortfolio = overview.children_portfolios.map(child => ({
            id: child.child_profile_id,
            name: child.child_name,
            age: 8, // Default age, could be added to backend
            balance: child.total_portfolio_value,
            avatar: null,
            totalGifts: child.total_gifts_received,
            totalInvested: child.total_invested,
            availableBalance: child.available_balance,
            giftsCount: child.total_gifts_count,
            investmentsCount: child.total_investments_count,
          }));
          setProfiles(profilesWithPortfolio);
        } else {
          // No children yet, use empty array
          setProfiles([]);
        }
      } catch (portfolioError) {
        console.log('Portfolio data not available, using mock data:', portfolioError);
        // Fallback to mock data
        setProfiles([
          {
            id: '1',
            name: 'Emma',
            age: 8,
            balance: 750.25,
            avatar: null,
            totalGifts: 800.00,
            totalInvested: 750.25,
            availableBalance: 49.75,
            giftsCount: 5,
            investmentsCount: 3,
          },
          {
            id: '2',
            name: 'Noah',
            age: 6,
            balance: 500.50,
            avatar: null,
            totalGifts: 600.00,
            totalInvested: 500.50,
            availableBalance: 99.50,
            giftsCount: 3,
            investmentsCount: 2,
          },
        ]);
      }
    } catch (error) {
      console.error('Error fetching profiles:', error);
      setProfiles([]);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    fetchProfilesData();
  }, []);

  const handleAddProfile = () => {
    router.push('/onboarding/child-profile');
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors[colorScheme].primary} />
        <Text style={styles.loadingText}>Loading profiles...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Child Profiles</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: Colors[colorScheme].primary }]}
          onPress={handleAddProfile}
        >
          <FontAwesome5 name="plus" size={16} color="white" />
          <Text style={styles.addButtonText}>Add Profile</Text>
        </TouchableOpacity>
      </View>

      {profiles.length > 0 ? (
        <FlatList
          data={profiles}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={() => fetchProfilesData(true)}
              colors={[Colors[colorScheme].primary]}
            />
          }
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.profileCard}
              onPress={() => router.push(`/portfolio/${item.id}`)}
            >
              <View style={styles.profileHeader}>
                <View style={styles.avatarContainer}>
                  {item.avatar ? (
                    <Image source={{ uri: item.avatar }} style={styles.avatar} />
                  ) : (
                    <View style={[styles.avatarPlaceholder, { backgroundColor: Colors[colorScheme].primary + '20' }]}>
                      <Text style={[styles.avatarInitial, { color: Colors[colorScheme].primary }]}>
                        {item.name.charAt(0)}
                      </Text>
                    </View>
                  )}
                </View>
                <View style={styles.profileInfo}>
                  <Text style={styles.profileName}>{item.name}</Text>
                  <Text style={styles.profileAge}>{item.age} years old</Text>
                </View>
                <View style={styles.profileActions}>
                  <TouchableOpacity style={styles.profileAction}>
                    <FontAwesome5 name="edit" size={16} color="#666" />
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.profileStats}>
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>Portfolio Value</Text>
                  <Text style={styles.statValue}>{formatCurrency(item.balance)}</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>Total Gifts</Text>
                  <Text style={styles.statValue}>{formatCurrency(item.totalGifts || 0)}</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>Available</Text>
                  <Text style={styles.statValue}>{formatCurrency(item.availableBalance || 0)}</Text>
                </View>
              </View>

              <View style={styles.portfolioProgress}>
                <View style={styles.progressInfo}>
                  <Text style={styles.progressLabel}>Investment Progress</Text>
                  <Text style={styles.progressPercentage}>
                    {item.totalGifts > 0
                      ? `${((item.totalInvested / item.totalGifts) * 100).toFixed(1)}%`
                      : '0%'
                    }
                  </Text>
                </View>
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      {
                        width: `${item.totalGifts > 0 ? (item.totalInvested / item.totalGifts) * 100 : 0}%`,
                        backgroundColor: Colors[colorScheme].primary
                      }
                    ]}
                  />
                </View>
              </View>

              <View style={styles.profileActions}>
                <TouchableOpacity
                  style={[styles.profileButton, { backgroundColor: Colors[colorScheme].primary + '10' }]}
                  onPress={() => router.push('/(tabs)/gifts')}
                >
                  <FontAwesome5 name="gift" size={16} color={Colors[colorScheme].primary} />
                  <Text style={[styles.profileButtonText, { color: Colors[colorScheme].primary }]}>Gift Wall</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.profileButton, { backgroundColor: Colors[colorScheme].primary + '10' }]}
                  onPress={() => router.push(`/portfolio/${item.id}`)}
                >
                  <FontAwesome5 name="chart-pie" size={16} color={Colors[colorScheme].primary} />
                  <Text style={[styles.profileButtonText, { color: Colors[colorScheme].primary }]}>Portfolio</Text>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          )}
          contentContainerStyle={styles.profilesList}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <View style={[styles.emptyIconContainer, { backgroundColor: Colors[colorScheme].primary + '20' }]}>
            <FontAwesome5 name="child" size={40} color={Colors[colorScheme].primary} />
          </View>
          <Text style={styles.emptyTitle}>No Child Profiles Yet</Text>
          <Text style={styles.emptyDescription}>
            Add your first child profile to start their investment journey
          </Text>
          <TouchableOpacity
            style={[styles.emptyButton, { backgroundColor: Colors[colorScheme].primary }]}
            onPress={handleAddProfile}
          >
            <Text style={styles.emptyButtonText}>Add Child Profile</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  profilesList: {
    padding: 16,
  },
  profileCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatarContainer: {
    marginRight: 16,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  avatarPlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarInitial: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  profileAge: {
    fontSize: 14,
    color: '#666',
  },
  profileActions: {
    flexDirection: 'row',
  },
  profileAction: {
    padding: 8,
  },
  profileStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  profileButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  profileButtonText: {
    fontWeight: '500',
    marginLeft: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  portfolioProgress: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 12,
    color: '#666',
  },
  progressPercentage: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  progressBar: {
    height: 6,
    backgroundColor: '#F0F0F0',
    borderRadius: 3,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
});
