import React, { useState, useEffect } from 'react';
import { StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, Image } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

import { Text, View } from '@/components/Themed';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';
import { useAuth } from '../../context/AuthContext';
import { childProfilesAPI } from '../../services/api';

export default function ProfilesScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { user } = useAuth();
  
  const [isLoading, setIsLoading] = useState(true);
  const [profiles, setProfiles] = useState([]);
  
  // Fetch child profiles
  useEffect(() => {
    const fetchProfiles = async () => {
      try {
        // In a real app, this would be an API call
        // const response = await childProfilesAPI.getProfiles();
        // setProfiles(response);
        
        // Mock data
        setTimeout(() => {
          setProfiles([
            {
              id: '1',
              name: 'Emma',
              age: 8,
              balance: 750.25,
              avatar: null,
            },
            {
              id: '2',
              name: 'Noah',
              age: 6,
              balance: 500.50,
              avatar: null,
            },
          ]);
          setIsLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error fetching profiles:', error);
        setIsLoading(false);
      }
    };
    
    fetchProfiles();
  }, []);

  const handleAddProfile = () => {
    router.push('/onboarding/child-profile');
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors[colorScheme].primary} />
        <Text style={styles.loadingText}>Loading profiles...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Child Profiles</Text>
        <TouchableOpacity 
          style={[styles.addButton, { backgroundColor: Colors[colorScheme].primary }]}
          onPress={handleAddProfile}
        >
          <FontAwesome5 name="plus" size={16} color="white" />
          <Text style={styles.addButtonText}>Add Profile</Text>
        </TouchableOpacity>
      </View>
      
      {profiles.length > 0 ? (
        <FlatList
          data={profiles}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity 
              style={styles.profileCard}
              onPress={() => {}}
            >
              <View style={styles.profileHeader}>
                <View style={styles.avatarContainer}>
                  {item.avatar ? (
                    <Image source={{ uri: item.avatar }} style={styles.avatar} />
                  ) : (
                    <View style={[styles.avatarPlaceholder, { backgroundColor: Colors[colorScheme].primary + '20' }]}>
                      <Text style={[styles.avatarInitial, { color: Colors[colorScheme].primary }]}>
                        {item.name.charAt(0)}
                      </Text>
                    </View>
                  )}
                </View>
                <View style={styles.profileInfo}>
                  <Text style={styles.profileName}>{item.name}</Text>
                  <Text style={styles.profileAge}>{item.age} years old</Text>
                </View>
                <View style={styles.profileActions}>
                  <TouchableOpacity style={styles.profileAction}>
                    <FontAwesome5 name="edit" size={16} color="#666" />
                  </TouchableOpacity>
                </View>
              </View>
              
              <View style={styles.profileStats}>
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>Account Balance</Text>
                  <Text style={styles.statValue}>${item.balance.toFixed(2)}</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>Investments</Text>
                  <Text style={styles.statValue}>3</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>Recent Gifts</Text>
                  <Text style={styles.statValue}>2</Text>
                </View>
              </View>
              
              <View style={styles.profileActions}>
                <TouchableOpacity 
                  style={[styles.profileButton, { backgroundColor: Colors[colorScheme].primary + '10' }]}
                >
                  <FontAwesome5 name="gift" size={16} color={Colors[colorScheme].primary} />
                  <Text style={[styles.profileButtonText, { color: Colors[colorScheme].primary }]}>Gift Wall</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.profileButton, { backgroundColor: Colors[colorScheme].primary + '10' }]}
                >
                  <FontAwesome5 name="chart-line" size={16} color={Colors[colorScheme].primary} />
                  <Text style={[styles.profileButtonText, { color: Colors[colorScheme].primary }]}>Investments</Text>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          )}
          contentContainerStyle={styles.profilesList}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <View style={[styles.emptyIconContainer, { backgroundColor: Colors[colorScheme].primary + '20' }]}>
            <FontAwesome5 name="child" size={40} color={Colors[colorScheme].primary} />
          </View>
          <Text style={styles.emptyTitle}>No Child Profiles Yet</Text>
          <Text style={styles.emptyDescription}>
            Add your first child profile to start their investment journey
          </Text>
          <TouchableOpacity 
            style={[styles.emptyButton, { backgroundColor: Colors[colorScheme].primary }]}
            onPress={handleAddProfile}
          >
            <Text style={styles.emptyButtonText}>Add Child Profile</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  profilesList: {
    padding: 16,
  },
  profileCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatarContainer: {
    marginRight: 16,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  avatarPlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarInitial: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  profileAge: {
    fontSize: 14,
    color: '#666',
  },
  profileActions: {
    flexDirection: 'row',
  },
  profileAction: {
    padding: 8,
  },
  profileStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  profileButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  profileButtonText: {
    fontWeight: '500',
    marginLeft: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
});
