#!/usr/bin/env python3

"""
Complete test script for trade execution with account-specific credentials
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_backend_connection():
    """Test if backend is running"""
    print("🔗 Testing backend connection...")
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print("   ✅ Backend server is running")
            return True
        else:
            print(f"   ❌ Backend responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Backend connection failed: {str(e)}")
        print("   💡 Start backend: uvicorn app.main:app --reload")
        return False

def test_encryption_service():
    """Test encryption service"""
    print("\n🔐 Testing encryption service...")
    try:
        response = requests.get(f"{BASE_URL}/api/test-encryption")
        # This endpoint doesn't exist, but we can test the service directly
        from app.encryption_service import encrypt_secret, decrypt_secret, is_encryption_enabled
        
        test_secret = "test_api_secret_12345"
        encrypted = encrypt_secret(test_secret)
        decrypted = decrypt_secret(encrypted)
        
        success = test_secret == decrypted
        encryption_enabled = is_encryption_enabled()
        
        print(f"   Encryption enabled: {encryption_enabled}")
        print(f"   Test passed: {success}")
        
        return success
        
    except Exception as e:
        print(f"   ❌ Encryption test failed: {str(e)}")
        return False

def test_parent_dashboard():
    """Test parent dashboard endpoint"""
    print("\n📊 Testing parent dashboard...")
    try:
        # This will fail without auth, but should return 401
        response = requests.get(f"{BASE_URL}/api/parent/dashboard")
        
        if response.status_code == 401:
            print("   ✅ Dashboard endpoint exists (requires auth)")
            return True
        elif response.status_code == 422:
            print("   ✅ Dashboard endpoint exists (validation error)")
            return True
        else:
            print(f"   ⚠️ Unexpected status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Dashboard test failed: {str(e)}")
        return False

def test_manual_investment_endpoint():
    """Test manual investment endpoint"""
    print("\n💰 Testing manual investment endpoint...")
    try:
        test_investment = {
            "child_profile_id": "550e8400-e29b-41d4-a716-446655440000",
            "stock_symbol": "SPY",
            "amount_usd": 10.0
        }
        
        response = requests.post(f"{BASE_URL}/api/invest/", json=test_investment)
        
        if response.status_code == 401:
            print("   ✅ Investment endpoint exists (requires auth)")
            return True
        elif response.status_code == 422:
            print("   ✅ Investment endpoint exists (validation error)")
            return True
        else:
            print(f"   ⚠️ Unexpected status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Investment test failed: {str(e)}")
        return False

def test_enable_trading_endpoint():
    """Test enable trading endpoint"""
    print("\n🔧 Testing enable trading endpoint...")
    try:
        account_id = "test_account_123"
        response = requests.post(f"{BASE_URL}/api/broker-accounts/{account_id}/enable-trading")
        
        if response.status_code == 401:
            print("   ✅ Enable trading endpoint exists (requires auth)")
            return True
        elif response.status_code == 422:
            print("   ✅ Enable trading endpoint exists (validation error)")
            return True
        else:
            print(f"   ⚠️ Unexpected status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Enable trading test failed: {str(e)}")
        return False

def test_alpaca_market_data():
    """Test Alpaca market data retrieval"""
    print("\n📈 Testing Alpaca market data...")
    try:
        from app.alpaca_service import get_alpaca_market_data
        
        # Test SPY
        market_data, error = get_alpaca_market_data("SPY")
        if error:
            print(f"   ⚠️ SPY market data error: {error}")
        else:
            print(f"   ✅ SPY price: ${market_data.get('last_price', 'N/A')}")
        
        # Test TSLA
        market_data, error = get_alpaca_market_data("TSLA")
        if error:
            print(f"   ⚠️ TSLA market data error: {error}")
        else:
            print(f"   ✅ TSLA price: ${market_data.get('last_price', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Market data test failed: {str(e)}")
        return False

def check_database_accounts():
    """Check broker accounts in database"""
    print("\n💾 Checking database accounts...")
    try:
        from app.database import SessionLocal
        from sqlalchemy import text
        
        db = SessionLocal()
        try:
            # Check broker accounts
            result = db.execute(text("""
                SELECT id, user_id, external_account_id, status, trading_enabled, 
                       CASE WHEN api_key_id IS NOT NULL THEN 'Yes' ELSE 'No' END as has_api_key
                FROM broker_accounts
            """))
            
            accounts = result.fetchall()
            
            if not accounts:
                print("   ℹ️ No broker accounts found")
                return True
            
            print(f"   Found {len(accounts)} broker accounts:")
            for account in accounts:
                print(f"   - ID: {account[2]} | Status: {account[3]} | Trading: {account[4]} | API Key: {account[5]}")
            
            # Count active accounts with trading enabled
            result = db.execute(text("""
                SELECT COUNT(*) FROM broker_accounts 
                WHERE status = 'active' AND trading_enabled = TRUE
            """))
            
            active_trading_count = result.scalar()
            print(f"   ✅ {active_trading_count} accounts ready for trading")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"   ❌ Database check failed: {str(e)}")
        return False

def simulate_complete_flow():
    """Simulate the complete trade execution flow"""
    print("\n🎯 SIMULATING COMPLETE TRADE EXECUTION FLOW")
    print("-" * 50)
    
    print("1. Parent logs in → JWT token generated")
    print("2. Parent navigates to manual investment screen")
    print("3. Frontend loads dashboard → GET /api/parent/dashboard")
    print("4. Parent selects child, stock (TSLA/SPY), amount")
    print("5. Parent clicks 'Invest' → POST /api/invest/")
    print("6. Backend validates parent owns child")
    print("7. Backend checks gift balance")
    print("8. Backend finds parent's broker account")
    print("9. Backend uses account-specific credentials")
    print("10. Backend calls Alpaca API → Real trade execution")
    print("11. Backend records investment in database")
    print("12. Frontend shows success with transaction details")
    
    print("\n🔍 EXPECTED BACKEND LOGS:")
    print("✅ Child profile validated: Alice")
    print("✅ Sufficient balance: $75.00")
    print("✅ Broker account found: alpaca_account_123")
    print("🔐 Using account-specific API credentials")
    print("📡 Placing order: $50 TSLA")
    print("✅ Order successful: order_123")
    print("✅ Investment created: investment_456")

def show_testing_instructions():
    """Show manual testing instructions"""
    print("\n" + "=" * 60)
    print("📋 MANUAL TESTING INSTRUCTIONS")
    print("=" * 60)
    
    print("\n1. 🚀 START SERVERS:")
    print("   Backend: uvicorn app.main:app --reload")
    print("   Frontend: cd kidvest-app-new && npm start")
    
    print("\n2. 🔐 ENABLE TRADING (if needed):")
    print("   For each active account:")
    print("   POST /api/broker-accounts/{account_id}/enable-trading")
    print("   (Use Swagger UI at http://localhost:8000/docs)")
    
    print("\n3. 📱 TEST FRONTEND:")
    print("   - Open React Native app")
    print("   - Navigate to manual investment screen")
    print("   - Select child: Alice or Bob")
    print("   - Select stock: TSLA or SPY")
    print("   - Enter amount: $10-50")
    print("   - Click 'Invest' button")
    
    print("\n4. 🔍 MONITOR LOGS:")
    print("   Watch backend terminal for:")
    print("   - Account-specific credential usage")
    print("   - Alpaca API calls")
    print("   - Investment creation")
    
    print("\n5. ✅ VERIFY SUCCESS:")
    print("   - Frontend shows success message")
    print("   - Transaction ID displayed")
    print("   - Shares and price shown")
    print("   - Database updated with investment")

def main():
    """Main test function"""
    print("🧪 Complete Trade Execution Test")
    print("=" * 50)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Backend Connection", test_backend_connection),
        ("Encryption Service", test_encryption_service),
        ("Parent Dashboard", test_parent_dashboard),
        ("Manual Investment", test_manual_investment_endpoint),
        ("Enable Trading", test_enable_trading_endpoint),
        ("Alpaca Market Data", test_alpaca_market_data),
        ("Database Accounts", check_database_accounts),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Show results summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Ready for manual testing.")
        simulate_complete_flow()
        show_testing_instructions()
    else:
        print("\n⚠️ Some tests failed. Fix issues before proceeding.")
    
    return passed == len(results)

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        exit(1)
