import React, { useState, useEffect } from 'react';
import { StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, Share, Alert } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

import { Text, View } from '@/components/Themed';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';
import { useAuth } from '../../context/AuthContext';
import { giftAPI } from '../../services/api';

export default function GiftsScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { user } = useAuth();

  const [isLoading, setIsLoading] = useState(true);
  const [giftWalls, setGiftWalls] = useState([]);

  // Fetch gift walls
  useEffect(() => {
    const fetchGiftWalls = async () => {
      try {
        // Fetch real gift walls from the API
        const response = await giftAPI.getGiftWalls();
        console.log('Fetched gift walls:', response);

        if (response && response.length > 0) {
          setGiftWalls(response);
        } else {
          // If no gift walls are returned, use mock data for testing
          console.log('No gift walls found, using mock data');
          setGiftWalls([
            {
              id: '1',
              childName: 'Emma',
              childAge: 8,
              handle: 'emma123',
              totalGifts: 12,
              totalAmount: 750.25,
              recentGifts: [
                { id: 'g1', sender: 'Grandma', amount: 50, date: '2023-05-01' },
                { id: 'g2', sender: 'Uncle Bob', amount: 100, date: '2023-04-15' },
              ],
              shareUrl: 'https://kidvest.app/gift/emma123',
            },
            {
              id: '2',
              childName: 'Noah',
              childAge: 6,
              handle: 'noah456',
              totalGifts: 8,
              totalAmount: 500.50,
              recentGifts: [
                { id: 'g3', sender: 'Aunt Sarah', amount: 25, date: '2023-03-22' },
                { id: 'g4', sender: 'Grandpa', amount: 75, date: '2023-02-14' },
              ],
              shareUrl: 'https://kidvest.app/gift/noah456',
            },
          ]);
        }
      } catch (error) {
        console.error('Error fetching gift walls:', error);
        // Use mock data as fallback
        setGiftWalls([
          {
            id: '1',
            childName: 'Emma',
            childAge: 8,
            handle: 'emma123',
            totalGifts: 12,
            totalAmount: 750.25,
            recentGifts: [
              { id: 'g1', sender: 'Grandma', amount: 50, date: '2023-05-01' },
              { id: 'g2', sender: 'Uncle Bob', amount: 100, date: '2023-04-15' },
            ],
            shareUrl: 'https://kidvest.app/gift/emma123',
          }
        ]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchGiftWalls();
  }, []);

  const handleCreateGift = (childId = null, childHandle = null) => {
    // Navigate to gift creation screen with child ID or handle if provided
    if (childId) {
      router.push(`/gifts/create?childId=${childId}`);
    } else if (childHandle) {
      router.push(`/gifts/create?childHandle=${childHandle}`);
    } else {
      // If no child is specified, show a dialog to select a child
      if (giftWalls.length > 0) {
        Alert.alert(
          'Select a Child',
          'Please select a child to create a gift for:',
          giftWalls.map(wall => ({
            text: wall.childName,
            onPress: () => router.push(`/gifts/create?childId=${wall.id}`)
          })).concat([
            { text: 'Cancel', style: 'cancel' }
          ])
        );
      } else {
        // No children available, prompt to create a child profile
        Alert.alert(
          'No Child Profiles',
          'You need to create a child profile before you can create a gift.',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Create Child Profile',
              onPress: () => router.push('/onboarding/child-profile')
            }
          ]
        );
      }
    }
  };

  const handleShareGiftWall = async (giftWall) => {
    try {
      await Share.share({
        message: `Help ${giftWall.childName} grow their investment account! Send a gift through their KidVest gift wall: ${giftWall.shareUrl}`,
        url: giftWall.shareUrl,
        title: `${giftWall.childName}'s KidVest Gift Wall`,
      });
    } catch (error) {
      console.error('Error sharing gift wall:', error);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors[colorScheme].primary} />
        <Text style={styles.loadingText}>Loading gift walls...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Gift Walls</Text>
        <TouchableOpacity
          style={[styles.createButton, { backgroundColor: Colors[colorScheme].primary }]}
          onPress={handleCreateGift}
        >
          <FontAwesome5 name="gift" size={16} color="white" />
          <Text style={styles.createButtonText}>Create Gift</Text>
        </TouchableOpacity>
      </View>

      {giftWalls.length > 0 ? (
        <FlatList
          data={giftWalls}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <View style={styles.giftWallCard}>
              <View style={styles.giftWallHeader}>
                <View style={styles.giftWallInfo}>
                  <Text style={styles.giftWallChildName}>{item.childName}'s Gift Wall</Text>
                  <Text style={styles.giftWallChildAge}>{item.childAge} years old</Text>
                </View>
                <TouchableOpacity
                  style={[styles.shareButton, { backgroundColor: Colors[colorScheme].primary + '20' }]}
                  onPress={() => handleShareGiftWall(item)}
                >
                  <FontAwesome5 name="share-alt" size={16} color={Colors[colorScheme].primary} />
                  <Text style={[styles.shareButtonText, { color: Colors[colorScheme].primary }]}>Share</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.giftWallStats}>
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>Total Gifts</Text>
                  <Text style={styles.statValue}>{item.totalGifts}</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>Total Amount</Text>
                  <Text style={styles.statValue}>${item.totalAmount.toFixed(2)}</Text>
                </View>
              </View>

              <View style={styles.recentGiftsContainer}>
                <Text style={styles.recentGiftsTitle}>Recent Gifts</Text>
                {item.recentGifts.map((gift) => (
                  <View key={gift.id} style={styles.giftItem}>
                    <View style={styles.giftIconContainer}>
                      <FontAwesome5 name="gift" size={16} color={Colors[colorScheme].primary} />
                    </View>
                    <View style={styles.giftDetails}>
                      <Text style={styles.giftSender}>{gift.sender}</Text>
                      <Text style={styles.giftDate}>{new Date(gift.date).toLocaleDateString()}</Text>
                    </View>
                    <Text style={styles.giftAmount}>${gift.amount}</Text>
                  </View>
                ))}
              </View>

              <View style={styles.giftWallActions}>
                <TouchableOpacity
                  style={[styles.giftWallButton, { backgroundColor: Colors[colorScheme].primary }]}
                  onPress={() => handleShareGiftWall(item)}
                >
                  <Text style={styles.giftWallButtonText}>Share Gift Wall</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.giftWallButton, { backgroundColor: Colors[colorScheme].secondary }]}
                  onPress={() => handleCreateGift(item.id, null)}
                >
                  <Text style={styles.giftWallButtonText}>Create Gift</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
          contentContainerStyle={styles.giftWallsList}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <View style={[styles.emptyIconContainer, { backgroundColor: Colors[colorScheme].primary + '20' }]}>
            <FontAwesome5 name="gift" size={40} color={Colors[colorScheme].primary} />
          </View>
          <Text style={styles.emptyTitle}>No Gift Walls Yet</Text>
          <Text style={styles.emptyDescription}>
            Create a gift wall for your child to receive monetary gifts from family and friends
          </Text>
          <TouchableOpacity
            style={[styles.emptyButton, { backgroundColor: Colors[colorScheme].primary }]}
            onPress={() => router.push('/onboarding/child-profile')}
          >
            <Text style={styles.emptyButtonText}>Add Child Profile</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  createButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  giftWallsList: {
    padding: 16,
  },
  giftWallCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  giftWallHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  giftWallInfo: {
    flex: 1,
  },
  giftWallChildName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  giftWallChildAge: {
    fontSize: 14,
    color: '#666',
  },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  shareButtonText: {
    fontWeight: '500',
    marginLeft: 6,
  },
  giftWallStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  recentGiftsContainer: {
    marginBottom: 16,
  },
  recentGiftsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  giftItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  giftIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  giftDetails: {
    flex: 1,
  },
  giftSender: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  giftDate: {
    fontSize: 12,
    color: '#999',
  },
  giftAmount: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  giftWallActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  giftWallButton: {
    flex: 1,
    paddingVertical: 10,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  giftWallButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
});
