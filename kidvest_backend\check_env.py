#!/usr/bin/env python3

import os
from dotenv import load_dotenv

print("🔍 Environment Variable Check")
print("=" * 40)

# Load environment variables
load_dotenv()

# Check what's actually loaded
webhook_secret = os.getenv("STRIPE_WEBHOOK_SECRET")
stripe_key = os.getenv("STRIPE_SECRET_KEY")

print(f"STRIPE_SECRET_KEY: {stripe_key[:20] if stripe_key else 'None'}...")
print(f"STRIPE_WEBHOOK_SECRET: {webhook_secret[:20] if webhook_secret else 'None'}...")

if webhook_secret:
    print(f"Full webhook secret length: {len(webhook_secret)}")
    print(f"Webhook secret starts with: {webhook_secret[:15]}...")
    print(f"Webhook secret ends with: ...{webhook_secret[-10:]}")
else:
    print("❌ No webhook secret found!")

# Check if .env file exists and read it directly
print(f"\n📁 Direct .env file check:")
try:
    with open('.env', 'r') as f:
        lines = f.readlines()
        for i, line in enumerate(lines, 1):
            if 'STRIPE_WEBHOOK_SECRET' in line:
                print(f"Line {i}: {line.strip()}")
except Exception as e:
    print(f"❌ Error reading .env file: {e}")

print(f"\n🔧 Recommendations:")
if webhook_secret and webhook_secret.startswith('whsec_test'):
    print(f"⚠️ You're using a test/placeholder webhook secret")
    print(f"🎯 Get the real secret from Stripe dashboard")
elif webhook_secret and len(webhook_secret) < 50:
    print(f"⚠️ Webhook secret seems too short")
    print(f"🎯 Verify you copied the complete secret from Stripe")
elif not webhook_secret:
    print(f"❌ No webhook secret loaded")
    print(f"🎯 Check your .env file and restart the server")
else:
    print(f"✅ Webhook secret looks valid")
    print(f"🎯 The issue might be with the secret itself - verify it matches Stripe dashboard")
