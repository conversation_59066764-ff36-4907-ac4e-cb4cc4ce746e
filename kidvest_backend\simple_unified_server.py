from flask import Flask, send_from_directory, redirect, request, jsonify, Response
import os
import logging
import sys
import uuid
from datetime import datetime
import requests

# Configure logging to stdout
logging.basicConfig(level=logging.DEBUG,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    handlers=[logging.StreamHandler(sys.stdout)])
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Define the base directory
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# Define the UI directories
UI_DIRS = {
    "auth": os.path.join(BASE_DIR, "auth-ui"),
    "onboarding": os.path.join(BASE_DIR, "onboarding-ui"),
    "child-profile": os.path.join(BASE_DIR, "child-profile-ui"),
    "dashboard": os.path.join(BASE_DIR, "dashboard-ui"),
    "gift-wall": os.path.join(BASE_DIR, "gift-wall-ui")
}

# Verify all directories exist
for name, path in UI_DIRS.items():
    if not os.path.exists(path):
        logger.warning(f"UI directory {name} not found at {path}")
    else:
        logger.info(f"Found UI directory {name} at {path}")

# Root route - redirect to auth by default
@app.route('/')
def index():
    logger.info("Accessing root route, redirecting to auth")
    print("Accessing root route, redirecting to auth")
    return redirect('/auth')

# Auth routes
@app.route('/auth')
@app.route('/auth/')
def auth():
    logger.info("Serving auth UI")
    print(f"Serving auth UI from {UI_DIRS['auth']}")
    try:
        return send_from_directory(UI_DIRS["auth"], "index.html")
    except Exception as e:
        print(f"Error serving auth UI: {str(e)}")
        logger.error(f"Error serving auth UI: {str(e)}")
        return jsonify(error="Error serving auth UI", details=str(e)), 500

@app.route('/auth/<path:path>')
def auth_files(path):
    logger.info(f"Serving auth file: {path}")
    print(f"Serving auth file: {path} from {UI_DIRS['auth']}")
    try:
        return send_from_directory(UI_DIRS["auth"], path)
    except Exception as e:
        print(f"Error serving auth file {path}: {str(e)}")
        logger.error(f"Error serving auth file {path}: {str(e)}")
        return jsonify(error=f"Error serving auth file {path}", details=str(e)), 500

# Dashboard routes
@app.route('/dashboard')
@app.route('/dashboard/')
def dashboard():
    logger.info("Serving dashboard UI")
    try:
        return send_from_directory(UI_DIRS["dashboard"], "index.html")
    except Exception as e:
        logger.error(f"Error serving dashboard UI: {str(e)}")
        return jsonify(error="Error serving dashboard UI", details=str(e)), 500

@app.route('/dashboard/<path:path>')
def dashboard_files(path):
    logger.info(f"Serving dashboard file: {path}")
    try:
        return send_from_directory(UI_DIRS["dashboard"], path)
    except Exception as e:
        logger.error(f"Error serving dashboard file {path}: {str(e)}")
        return jsonify(error=f"Error serving dashboard file {path}", details=str(e)), 500

# Onboarding routes
@app.route('/onboarding')
@app.route('/onboarding/')
def onboarding():
    logger.info("Serving onboarding UI")
    try:
        return send_from_directory(UI_DIRS["onboarding"], "index.html")
    except Exception as e:
        logger.error(f"Error serving onboarding UI: {str(e)}")
        return jsonify(error="Error serving onboarding UI", details=str(e)), 500

@app.route('/onboarding/<path:path>')
def onboarding_files(path):
    logger.info(f"Serving onboarding file: {path}")
    try:
        return send_from_directory(UI_DIRS["onboarding"], path)
    except Exception as e:
        logger.error(f"Error serving onboarding file {path}: {str(e)}")
        return jsonify(error=f"Error serving onboarding file {path}", details=str(e)), 500

# Child profile routes
@app.route('/child-profile')
@app.route('/child-profile/')
def child_profile():
    logger.info("Serving child profile UI")
    try:
        return send_from_directory(UI_DIRS["child-profile"], "index.html")
    except Exception as e:
        logger.error(f"Error serving child profile UI: {str(e)}")
        return jsonify(error="Error serving child profile UI", details=str(e)), 500

@app.route('/child-profile/<path:path>')
def child_profile_files(path):
    logger.info(f"Serving child profile file: {path}")
    try:
        return send_from_directory(UI_DIRS["child-profile"], path)
    except Exception as e:
        logger.error(f"Error serving child profile file {path}: {str(e)}")
        return jsonify(error=f"Error serving child profile file {path}", details=str(e)), 500

# Gift wall routes
@app.route('/gift-wall')
@app.route('/gift-wall/')
def gift_wall():
    logger.info("Serving gift wall UI")
    try:
        return send_from_directory(UI_DIRS["gift-wall"], "index.html")
    except Exception as e:
        logger.error(f"Error serving gift wall UI: {str(e)}")
        return jsonify(error="Error serving gift wall UI", details=str(e)), 500

@app.route('/gift-wall/<path:path>')
def gift_wall_files(path):
    logger.info(f"Serving gift wall file: {path}")
    try:
        return send_from_directory(UI_DIRS["gift-wall"], path)
    except Exception as e:
        logger.error(f"Error serving gift wall file {path}: {str(e)}")
        return jsonify(error=f"Error serving gift wall file {path}", details=str(e)), 500

# Wall routes for direct gift wall access
@app.route('/wall/<path:handle>')
def wall(handle):
    logger.info(f"Serving gift wall for handle: {handle}")
    try:
        return send_from_directory(UI_DIRS["gift-wall"], "index.html")
    except Exception as e:
        logger.error(f"Error serving gift wall for handle {handle}: {str(e)}")
        return jsonify(error=f"Error serving gift wall for handle {handle}", details=str(e)), 500

# Error handlers
@app.errorhandler(404)
def page_not_found(e):
    logger.error(f"404 error: {request.path}")
    return jsonify(error="Page not found", path=request.path), 404

@app.errorhandler(500)
def server_error(e):
    logger.error(f"500 error: {str(e)}")
    return jsonify(error="Internal server error", details=str(e)), 500

# API Proxy
@app.route('/api/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])
def proxy_api(path):
    logger.info(f"Proxying API request: {request.method} {path}")
    print(f"Proxying API request: {request.method} {path}")

    # Forward the request to the backend API
    backend_url = f"http://localhost:8001/api/{path}"
    logger.info(f"Forwarding to: {backend_url}")
    print(f"Forwarding to: {backend_url}")

    # Get the request data
    headers = {key: value for key, value in request.headers if key != 'Host'}
    data = request.get_data()

    # Log request details
    print(f"Request headers: {headers}")
    print(f"Request data: {data}")

    try:
        # Make the request to the backend API
        response = requests.request(
            method=request.method,
            url=backend_url,
            headers=headers,
            data=data,
            cookies=request.cookies,
            allow_redirects=False
        )

        # Log the response
        logger.info(f"Backend API response: {response.status_code}")
        print(f"Backend API response: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")

        # Log response content for debugging
        content = response.content
        content_str = content.decode('utf-8') if content else ''
        print(f"Response content: {content_str[:500]}{'...' if len(content_str) > 500 else ''}")

        # Return the response from the backend API
        return Response(
            content,
            status=response.status_code,
            headers=dict(response.headers)
        )
    except Exception as e:
        logger.error(f"Error proxying API request: {str(e)}")
        print(f"Error proxying API request: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify(error="API proxy error", details=str(e)), 500

# Add CORS headers
@app.after_request
def add_cors_headers(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

if __name__ == '__main__':
    print("Starting unified UI server...")
    print("Available UIs:")
    print(f"  - Auth: http://localhost:8080/auth/")
    print(f"  - Dashboard: http://localhost:8080/dashboard/")
    print(f"  - Onboarding: http://localhost:8080/onboarding/")
    print(f"  - Child Profiles: http://localhost:8080/child-profile/")
    print(f"  - Gift Walls: http://localhost:8080/gift-wall/")

    # Run the Flask app
    app.run(host='0.0.0.0', port=8080, debug=True)
