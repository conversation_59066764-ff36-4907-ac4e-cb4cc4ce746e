#!/usr/bin/env python3

"""
Script to check and update broker account status from Alpaca
"""

import sys
import requests
import json
from sqlalchemy import text
from app.database import SessionLocal
from app.alpaca_service import get_alpaca_account_status, enable_account_trading, create_account_api_keys, encrypt_secret

def check_single_account(account_id: str):
    """Check status of a single account"""
    print(f"\n🔍 Checking account: {account_id}")
    
    # Get status from Alpaca
    account_status, status_error = get_alpaca_account_status(account_id)
    if status_error:
        print(f"   ❌ Failed to get status: {status_error}")
        return None
    
    print(f"   ✅ Alpaca status: {account_status.get('status')}")
    return account_status

def update_account_in_database(db, account_id: str, alpaca_status: dict):
    """Update account status in database"""
    print(f"\n🔄 Updating database for account: {account_id}")
    
    try:
        # Find the broker account
        result = db.execute(text("""
            SELECT id, user_id, status, trading_enabled, api_key_id
            FROM broker_accounts 
            WHERE external_account_id = :account_id
        """), {"account_id": account_id})
        
        account_row = result.fetchone()
        if not account_row:
            print(f"   ❌ Account not found in database: {account_id}")
            return False
        
        print(f"   ✅ Found in database: {account_row[0]}")
        print(f"   Current status: {account_row[2]}")
        print(f"   Trading enabled: {account_row[3]}")
        print(f"   Has API key: {bool(account_row[4])}")
        
        # Update status if different
        new_status = alpaca_status.get("status", "").lower()
        if new_status and new_status != account_row[2]:
            print(f"   🔄 Updating status: {account_row[2]} → {new_status}")
            db.execute(text("""
                UPDATE broker_accounts 
                SET status = :new_status 
                WHERE external_account_id = :account_id
            """), {"new_status": new_status, "account_id": account_id})
        
        # If account is active and trading not enabled, enable it
        if new_status in ["active", "approved"] and not account_row[3]:
            print(f"   🎉 Account is active! Enabling trading...")
            
            # Enable trading configuration
            trading_result, trading_error = enable_account_trading(account_id)
            if trading_error:
                print(f"   ⚠️ Failed to enable trading: {trading_error}")
            else:
                print(f"   ✅ Trading enabled: {trading_result}")
                db.execute(text("""
                    UPDATE broker_accounts 
                    SET trading_enabled = TRUE 
                    WHERE external_account_id = :account_id
                """), {"account_id": account_id})
            
            # Create account-specific API keys if not already present
            if not account_row[4]:
                keys_result, keys_error = create_account_api_keys(account_id)
                if keys_error:
                    print(f"   ⚠️ Failed to create API keys: {keys_error}")
                else:
                    print(f"   ✅ API keys created: {keys_result['api_key_id']}")
                    encrypted_secret = encrypt_secret(keys_result["api_secret_key"])
                    db.execute(text("""
                        UPDATE broker_accounts 
                        SET api_key_id = :api_key_id, api_secret_key = :api_secret_key
                        WHERE external_account_id = :account_id
                    """), {
                        "api_key_id": keys_result["api_key_id"],
                        "api_secret_key": encrypted_secret,
                        "account_id": account_id
                    })
                    print(f"   🔐 API credentials stored and encrypted")
        
        db.commit()
        print(f"   ✅ Database updated successfully")
        return True
        
    except Exception as e:
        print(f"   ❌ Error updating database: {str(e)}")
        db.rollback()
        return False

def check_all_accounts():
    """Check and update all broker accounts"""
    print("🔍 Checking all broker accounts...")
    
    db = SessionLocal()
    try:
        # Get all broker accounts
        result = db.execute(text("""
            SELECT external_account_id, status, trading_enabled
            FROM broker_accounts
            ORDER BY created_at DESC
        """))
        
        accounts = result.fetchall()
        
        if not accounts:
            print("   ℹ️ No accounts found")
            return
        
        print(f"   Found {len(accounts)} accounts to check:")
        
        updated_count = 0
        for account in accounts:
            account_id = account[0]
            current_status = account[1]
            trading_enabled = account[2]
            
            print(f"\n📋 Account: {account_id}")
            print(f"   Current status: {current_status}")
            print(f"   Trading enabled: {trading_enabled}")
            
            # Check status with Alpaca
            alpaca_status = check_single_account(account_id)
            if alpaca_status:
                if update_account_in_database(db, account_id, alpaca_status):
                    updated_count += 1
        
        print(f"\n✅ Updated {updated_count} accounts")
        
        # Show final status
        show_account_summary(db)
        
    except Exception as e:
        print(f"❌ Error checking accounts: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

def show_account_summary(db):
    """Show summary of all accounts"""
    print(f"\n📊 Account Summary:")
    
    try:
        result = db.execute(text("""
            SELECT external_account_id, status, trading_enabled,
                   CASE WHEN api_key_id IS NOT NULL THEN 'Yes' ELSE 'No' END as has_api_key
            FROM broker_accounts
            ORDER BY created_at DESC
        """))
        
        accounts = result.fetchall()
        
        for account in accounts:
            print(f"   - {account[0]} | Status: {account[1]} | Trading: {account[2]} | API Key: {account[3]}")
        
        # Count ready accounts
        result = db.execute(text("""
            SELECT COUNT(*) FROM broker_accounts 
            WHERE status = 'active' AND trading_enabled = TRUE
        """))
        
        ready_count = result.scalar()
        print(f"\n   🎯 {ready_count} accounts ready for trading")
        
    except Exception as e:
        print(f"   ❌ Error showing summary: {str(e)}")

def main():
    """Main function"""
    print("🚀 Broker Account Status Updater")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        # Check specific account
        account_id = sys.argv[1]
        print(f"Checking specific account: {account_id}")
        
        alpaca_status = check_single_account(account_id)
        if alpaca_status:
            db = SessionLocal()
            try:
                update_account_in_database(db, account_id, alpaca_status)
                show_account_summary(db)
            finally:
                db.close()
    else:
        # Check all accounts
        check_all_accounts()
    
    print(f"\n📋 Next steps:")
    print(f"1. Restart backend server")
    print(f"2. Test manual investment in frontend")
    print(f"3. Monitor backend logs for trade execution")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ Operation interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
