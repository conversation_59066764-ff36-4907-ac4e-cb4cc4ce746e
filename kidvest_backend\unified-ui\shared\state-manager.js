/**
 * KidVest State Manager
 *
 * A minimal but effective state management solution for the KidVest application.
 * This provides a reliable way to share state between different pages.
 */

// Create a namespace to avoid global pollution
window.KidVest = window.KidVest || {};

// State manager implementation
KidVest.StateManager = (function() {
    // Constants
    const TOKEN_KEY = 'access_token';
    const API_BASE_URL = 'http://localhost:8080/api';

    // Private state
    let _initialized = false;

    /**
     * Initialize the state manager
     */
    function init() {
        if (_initialized) return;

        console.log('KidVest StateManager: Initializing');

        // Set up unload listener to save state
        window.addEventListener('beforeunload', function() {
            // Save any necessary state before page unload
            console.log('KidVest StateManager: Page unloading');
        });

        _initialized = true;
        return true;
    }

    /**
     * Check if the user is authenticated
     */
    function isAuthenticated() {
        const token = localStorage.getItem(TOKEN_KEY);
        console.log('StateManager.isAuthenticated() - Token exists:', !!token);

        // If we're on the auth page, don't redirect even if we have a token
        // This allows users to log out and log in as a different user
        if (window.location.pathname.startsWith('/auth')) {
            console.log('StateManager.isAuthenticated() - On auth page, not redirecting');
            return false;
        }

        return !!token;
    }

    /**
     * Get the authentication token
     */
    function getToken() {
        return localStorage.getItem(TOKEN_KEY);
    }

    /**
     * Set the authentication token
     */
    function setToken(token) {
        localStorage.setItem(TOKEN_KEY, token);
    }

    /**
     * Clear the authentication token (logout)
     */
    function clearToken() {
        localStorage.removeItem(TOKEN_KEY);
    }

    /**
     * Navigate to another page
     */
    function navigateTo(page) {
        // Add a timestamp to prevent caching
        const timestamp = new Date().getTime();
        window.location.href = `/${page}?_=${timestamp}`;
    }

    /**
     * Redirect to login if not authenticated
     */
    function requireAuth() {
        if (!isAuthenticated()) {
            console.log('KidVest StateManager: Authentication required, redirecting to login');
            navigateTo('auth');
            return false;
        }
        return true;
    }

    /**
     * Make an authenticated API request
     */
    async function apiRequest(endpoint, options = {}) {
        const token = getToken();

        if (!token) {
            console.error('KidVest StateManager: No authentication token available for API request');
            return null;
        }

        // Set up default options
        const defaultOptions = {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        };

        // Merge options
        const requestOptions = { ...defaultOptions, ...options };

        try {
            const response = await fetch(`${API_BASE_URL}/${endpoint}`, requestOptions);

            if (response.status === 401) {
                console.error('KidVest StateManager: Authentication failed for API request');
                clearToken();
                navigateTo('auth');
                return null;
            }

            return response;
        } catch (error) {
            console.error('KidVest StateManager: API request failed', error);
            return null;
        }
    }

    // Public API
    return {
        init,
        isAuthenticated,
        getToken,
        setToken,
        clearToken,
        navigateTo,
        requireAuth,
        apiRequest
    };
})();

// Initialize the state manager when the script loads
document.addEventListener('DOMContentLoaded', function() {
    KidVest.StateManager.init();
    console.log('KidVest StateManager: Ready');
});
