@echo off
echo Starting KidVest Gift Wall with ngrok compatibility...

REM Set execution policy and activate virtual environment in a separate window for the backend
start cmd /k "powershell -Command \"Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process; .\venv\Scripts\activate; python start_backend.py\""

REM Wait for the backend to start
timeout /t 5

REM Start the gift wall UI in a separate window
start cmd /k "powershell -Command \"Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process; .\venv\Scripts\activate; cd gift-wall-ui; python server.py\""

echo.
echo Servers started!
echo Backend: http://127.0.0.1:8000
echo Gift Wall UI: http://localhost:8082/wall/zohaib_ali
echo.
echo Note: Keep ngrok running in its own PowerShell window.
