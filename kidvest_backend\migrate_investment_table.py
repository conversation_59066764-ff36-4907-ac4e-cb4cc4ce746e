#!/usr/bin/env python3

"""
Migration script to add parent_id and executed_at fields to Investment table
for Phase 3: Manual Investment Flow
"""

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.database import DATABASE_URL
from app import models
import uuid

def migrate_investment_table():
    """Add parent_id and executed_at columns to investments table"""
    
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        print("🔄 Starting Investment table migration...")
        
        # Check if parent_id column exists
        result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'investments' AND column_name = 'parent_id'
        """))
        
        parent_id_exists = result.fetchone() is not None
        
        if not parent_id_exists:
            print("➕ Adding parent_id column...")
            db.execute(text("""
                ALTER TABLE investments 
                ADD COLUMN parent_id UUID REFERENCES users(id)
            """))
            print("✅ parent_id column added")
        else:
            print("✅ parent_id column already exists")
        
        # Check if executed_at column exists
        result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'investments' AND column_name = 'executed_at'
        """))
        
        executed_at_exists = result.fetchone() is not None
        
        if not executed_at_exists:
            print("➕ Adding executed_at column...")
            db.execute(text("""
                ALTER TABLE investments 
                ADD COLUMN executed_at TIMESTAMP
            """))
            print("✅ executed_at column added")
        else:
            print("✅ executed_at column already exists")
        
        # Update existing investments to set parent_id
        if not parent_id_exists:
            print("🔄 Updating existing investments with parent_id...")
            
            # Get all investments without parent_id
            investments = db.execute(text("""
                SELECT i.id, i.child_profile_id, cp.parent_id
                FROM investments i
                JOIN child_profiles cp ON i.child_profile_id = cp.id
                WHERE i.parent_id IS NULL
            """)).fetchall()
            
            for investment in investments:
                db.execute(text("""
                    UPDATE investments 
                    SET parent_id = :parent_id 
                    WHERE id = :investment_id
                """), {
                    "parent_id": investment.parent_id,
                    "investment_id": investment.id
                })
            
            print(f"✅ Updated {len(investments)} existing investments with parent_id")
        
        # Make parent_id NOT NULL after populating existing records
        if not parent_id_exists:
            print("🔒 Making parent_id NOT NULL...")
            db.execute(text("""
                ALTER TABLE investments 
                ALTER COLUMN parent_id SET NOT NULL
            """))
            print("✅ parent_id is now NOT NULL")
        
        db.commit()
        print("✅ Investment table migration completed successfully!")
        
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def verify_migration():
    """Verify the migration was successful"""
    
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        print("\n🔍 Verifying migration...")
        
        # Check table structure
        result = db.execute(text("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'investments'
            ORDER BY ordinal_position
        """))
        
        columns = result.fetchall()
        
        print("📋 Current investments table structure:")
        for col in columns:
            nullable = "NULL" if col.is_nullable == "YES" else "NOT NULL"
            print(f"   {col.column_name}: {col.data_type} {nullable}")
        
        # Check if we have the required columns
        column_names = [col.column_name for col in columns]
        
        required_columns = ['id', 'child_profile_id', 'parent_id', 'gift_id', 
                          'amount_usd', 'symbol', 'shares', 'purchase_price', 
                          'status', 'transaction_id', 'executed_at', 
                          'created_at', 'updated_at']
        
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            print(f"❌ Missing columns: {missing_columns}")
            return False
        else:
            print("✅ All required columns present")
        
        # Check sample data
        result = db.execute(text("SELECT COUNT(*) FROM investments"))
        count = result.scalar()
        print(f"📊 Total investments: {count}")
        
        if count > 0:
            result = db.execute(text("""
                SELECT COUNT(*) FROM investments WHERE parent_id IS NOT NULL
            """))
            with_parent_id = result.scalar()
            print(f"📊 Investments with parent_id: {with_parent_id}")
            
            if with_parent_id != count:
                print(f"⚠️ Warning: {count - with_parent_id} investments missing parent_id")
                return False
        
        print("✅ Migration verification successful!")
        return True
        
    except Exception as e:
        print(f"❌ Verification failed: {str(e)}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 Phase 3: Investment Table Migration")
    print("=" * 50)
    
    try:
        migrate_investment_table()
        
        if verify_migration():
            print("\n🎉 Migration completed successfully!")
            print("✅ Investment table is ready for Phase 3: Manual Investment Flow")
        else:
            print("\n❌ Migration verification failed!")
            exit(1)
            
    except Exception as e:
        print(f"\n💥 Migration failed with error: {str(e)}")
        exit(1)
