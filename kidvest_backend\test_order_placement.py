#!/usr/bin/env python3

"""
Test direct order placement to Alpaca
"""

import requests
import json
import os
import base64
from datetime import datetime

AUSTIN_ACCOUNT_ID = "25aa2245-9d53-42f9-b3b3-43cb899c1798"

def test_direct_alpaca_order():
    """Test direct order placement to Alpaca"""
    print("🚀 Testing direct Alpaca order placement...")
    
    api_key = os.getenv("ALPACA_API_KEY")
    api_secret = os.getenv("ALPACA_SECRET_KEY")
    
    if not api_key or not api_secret:
        print("❌ Alpaca API keys not found in environment")
        return False
    
    print(f"✅ API Key: {api_key[:10]}...")
    print(f"✅ API Secret: {api_secret[:10]}...")
    
    # Use fallback pricing
    symbol = "SPY"
    amount_usd = 15.0
    current_price = 550.0  # Fallback price
    shares_to_buy = amount_usd / current_price
    
    print(f"📊 Order details:")
    print(f"   Symbol: {symbol}")
    print(f"   Amount: ${amount_usd}")
    print(f"   Price: ${current_price}")
    print(f"   Shares: {shares_to_buy}")
    
    # Create authentication header
    auth_string = f"{api_key}:{api_secret}"
    encoded_auth = base64.b64encode(auth_string.encode()).decode()
    
    headers = {
        "Authorization": f"Basic {encoded_auth}",
        "Content-Type": "application/json"
    }
    
    # Order payload
    order_payload = {
        "symbol": symbol,
        "qty": shares_to_buy,
        "side": "buy",
        "type": "market",
        "time_in_force": "day"
    }
    
    url = f"https://broker-api.sandbox.alpaca.markets/v1/trading/accounts/{AUSTIN_ACCOUNT_ID}/orders"
    
    print(f"\n📡 Making order request:")
    print(f"   URL: {url}")
    print(f"   Headers: {headers}")
    print(f"   Payload: {json.dumps(order_payload, indent=2)}")
    
    try:
        response = requests.post(url, headers=headers, json=order_payload, timeout=30)
        
        print(f"\n📊 Response:")
        print(f"   Status: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        print(f"   Content Length: {len(response.content)}")
        print(f"   Content: {response.content}")
        
        if response.content:
            try:
                result = response.json()
                print(f"   JSON: {json.dumps(result, indent=2)}")
                
                if response.status_code in [200, 201]:
                    print("✅ Order successful!")
                    return True
                else:
                    print(f"❌ Order failed: {result}")
                    return False
            except json.JSONDecodeError:
                print(f"❌ Non-JSON response: {response.text}")
                return False
        else:
            print("❌ Empty response")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_account_status():
    """Test account status"""
    print("\n🔍 Testing account status...")
    
    api_key = os.getenv("ALPACA_API_KEY")
    api_secret = os.getenv("ALPACA_SECRET_KEY")
    
    auth_string = f"{api_key}:{api_secret}"
    encoded_auth = base64.b64encode(auth_string.encode()).decode()
    
    headers = {
        "Authorization": f"Basic {encoded_auth}",
        "Content-Type": "application/json"
    }
    
    url = f"https://broker-api.sandbox.alpaca.markets/v1/accounts/{AUSTIN_ACCOUNT_ID}"
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   Account Status: {result.get('status')}")
            print(f"   Trading Type: {result.get('trading_type')}")
            print(f"   Enabled Assets: {result.get('enabled_assets')}")
            return True
        else:
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"   Error: {str(e)}")
        return False

def test_market_hours():
    """Test if market is open"""
    print("\n🕐 Testing market hours...")
    
    try:
        # Simple market hours check
        now = datetime.now()
        hour = now.hour
        weekday = now.weekday()  # 0=Monday, 6=Sunday
        
        # US market hours: 9:30 AM - 4:00 PM ET (14:30 - 21:00 UTC)
        # Approximate check
        is_weekday = weekday < 5  # Monday-Friday
        is_market_hours = 9 <= hour <= 16  # Rough approximation
        
        print(f"   Current time: {now}")
        print(f"   Weekday: {is_weekday}")
        print(f"   Market hours: {is_market_hours}")
        print(f"   Market likely open: {is_weekday and is_market_hours}")
        
        return is_weekday and is_market_hours
        
    except Exception as e:
        print(f"   Error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🧪 Direct Alpaca Order Placement Test")
    print("=" * 50)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test 1: Market hours
    market_open = test_market_hours()
    
    # Test 2: Account status
    account_ok = test_account_status()
    
    # Test 3: Direct order
    if account_ok:
        if market_open:
            print("\n⚠️ WARNING: Market appears to be open - this will place a REAL order!")
            response = input("Proceed with real order? (y/N): ").strip().lower()
            if response != 'y':
                print("❌ Order test cancelled")
                return False
        
        order_success = test_direct_alpaca_order()
    else:
        print("❌ Account not ready - skipping order test")
        order_success = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Market Status: {'Open' if market_open else 'Closed/Unknown'}")
    print(f"✅ Account Status: {'Ready' if account_ok else 'Issues'}")
    print(f"✅ Order Placement: {'Success' if order_success else 'Failed'}")
    
    if not order_success:
        print(f"\n🔍 TROUBLESHOOTING:")
        print(f"1. Check Alpaca API keys are correct")
        print(f"2. Verify account has trading permissions")
        print(f"3. Check if market is open")
        print(f"4. Review Alpaca sandbox limitations")
        print(f"5. Check backend logs for detailed errors")
    
    return order_success

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted")
        exit(1)
    except Exception as e:
        print(f"\n💥 Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        exit(1)
