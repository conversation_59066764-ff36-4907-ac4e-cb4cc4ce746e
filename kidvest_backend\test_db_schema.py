from sqlalchemy import create_engine, inspect
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
from dotenv import load_dotenv
import sys

# Add the current directory to the path so we can import app modules
sys.path.append('.')

# Load environment variables
load_dotenv()

# Database connection
DATABASE_URL = os.getenv("DATABASE_URL")
print(f"Using database URL: {DATABASE_URL}")

# Create engine
engine = create_engine(DATABASE_URL)

def inspect_database():
    """Inspect the database schema"""
    print("=== Database Schema Inspection ===")
    
    try:
        # Create an inspector
        inspector = inspect(engine)
        
        # Get all table names
        tables = inspector.get_table_names()
        print(f"Tables in database: {tables}")
        
        # Inspect each table
        for table_name in tables:
            print(f"\nTable: {table_name}")
            
            # Get columns
            columns = inspector.get_columns(table_name)
            print("Columns:")
            for column in columns:
                print(f"  - {column['name']} ({column['type']})")
            
            # Get primary keys
            pk = inspector.get_pk_constraint(table_name)
            print(f"Primary key: {pk['constrained_columns']}")
            
            # Get foreign keys
            fks = inspector.get_foreign_keys(table_name)
            if fks:
                print("Foreign keys:")
                for fk in fks:
                    print(f"  - {fk['constrained_columns']} -> {fk['referred_table']}.{fk['referred_columns']}")
        
        print("\n=== Database inspection completed successfully! ===")
    except Exception as e:
        print(f"Error during database inspection: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    inspect_database()
    print("\nDone.")
