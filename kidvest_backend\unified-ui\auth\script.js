// API Base URL
const API_BASE_URL = 'http://localhost:8080/api';

// For debugging
console.log('Auth UI script loaded');

// Initialize UI
window.addEventListener('DOMContentLoaded', function() {
    console.log('Auth UI: DOM fully loaded');

    // Show initial debug message
    const apiResponse = document.getElementById('api-response');
    if (apiResponse) {
        apiResponse.style.display = 'block';
        apiResponse.innerHTML = '[' + new Date().toLocaleTimeString() + '] Auth UI initialized. Waiting for user action...';
    } else {
        console.error('API response element not found!');
    }

    // Check if we have a token but allow the user to stay on the auth page
    const hasToken = localStorage.getItem('access_token');
    if (hasToken) {
        console.log('Note: You are already logged in. You can continue to use this form to log in as a different user.');
        apiResponse.innerHTML = '[' + new Date().toLocaleTimeString() + '] Note: You are already logged in. You can continue to use this form to log in as a different user.' + '<hr>' + apiResponse.innerHTML;
    }
});

// Initialize when DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded');

    // DOM Elements
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');
    const apiResponse = document.getElementById('api-response');

    console.log('Login form:', loginForm);
    console.log('Register form:', registerForm);

    if (!loginForm || !registerForm) {
        console.error('Could not find form elements!');
        return;
    }

    // Show API Response
    function showResponse(response, isError = false) {
        apiResponse.style.display = 'block';
        apiResponse.style.backgroundColor = isError ? '#f8d7da' : '#d4edda';
        apiResponse.style.color = isError ? '#721c24' : '#155724';

        // Format the data
        const formattedData = typeof response === 'object' ? JSON.stringify(response, null, 2) : response;

        // Add timestamp
        const timestamp = new Date().toLocaleTimeString();
        const message = `[${timestamp}] ${formattedData}`;

        // Append to existing content instead of replacing
        apiResponse.innerHTML = message + '<hr>' + apiResponse.innerHTML;
    }

    // Clear debug information
    document.getElementById('clear-debug-btn').addEventListener('click', function() {
        apiResponse.innerHTML = 'Debug information cleared.';
        apiResponse.style.backgroundColor = '#f8f9fa';
        apiResponse.style.color = '#212529';
    });

    // Handle Registration
    console.log('Setting up registration form handler');
    registerForm.addEventListener('submit', async (e) => {
        console.log('Register form submitted');
        e.preventDefault();

        const name = document.getElementById('register-name').value;
        const email = document.getElementById('register-email').value;
        const password = document.getElementById('register-password').value;
        const confirmPassword = document.getElementById('register-confirm-password').value;

        console.log('Form values:', { name, email, password: '***', confirmPassword: '***' });

        // Validate passwords match
        if (password !== confirmPassword) {
            showResponse('Passwords do not match', true);
            return;
        }

        try {
            // Show initial response
            showResponse('Attempting to register...', false);

            console.log('Sending registration request...');
            const response = await fetch(`${API_BASE_URL}/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name,
                    email,
                    password,
                    user_type: 'parent'
                }),
                // Add timeout and cache control
                cache: 'no-store',
                credentials: 'same-origin',
                redirect: 'follow',
                referrerPolicy: 'no-referrer'
            });

            console.log('Registration response status:', response.status);
            console.log('Response headers:', response.headers);

            // Get the response text first to debug any JSON parsing issues
            const responseText = await response.text();
            console.log('Response text:', responseText);

            // Show raw response for debugging
            showResponse(`Raw server response: ${responseText}`, false);

            // Try to parse the response as JSON
            let data;
            try {
                data = JSON.parse(responseText);
                console.log('Registration response data:', data);
            } catch (jsonError) {
                console.error('Error parsing JSON:', jsonError);
                showResponse(`Error parsing response: ${jsonError.message}\n\nRaw response: ${responseText}`, true);
                return;
            }

            if (response.ok) {
                showResponse({
                    message: 'Registration successful! You can now log in.',
                    user: data
                });

                // Clear form
                registerForm.reset();

                // Switch to login tab
                document.getElementById('login-tab').click();
            } else {
                showResponse(data, true);
            }
        } catch (error) {
            console.error('Registration error:', error);
            showResponse(`Error: ${error.message}`, true);
        }
});

    // Handle Login
    loginForm.addEventListener('submit', async (e) => {
        console.log('Login form submitted');
        e.preventDefault();

        const email = document.getElementById('login-email').value;
        const password = document.getElementById('login-password').value;

        try {
            // Show initial response
            showResponse('Attempting to log in...', false);

            // Create form data for OAuth2 password flow
            const formData = new URLSearchParams();
            formData.append('username', email);
            formData.append('password', password);

            console.log('Sending login request...');
            console.log('Request URL:', `${API_BASE_URL}/token`);
            console.log('Request body:', formData.toString());

            // Make the fetch request
            const response = await fetch(`${API_BASE_URL}/token`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: formData,
                // Add timeout and cache control
                cache: 'no-store',
                credentials: 'same-origin',
                redirect: 'follow',
                referrerPolicy: 'no-referrer'
            });

            // Check if the response is ok before proceeding
            if (!response) {
                throw new Error('No response received from server');
            }

            console.log('Login response status:', response.status);
            console.log('Response headers:', response.headers);

            // Get the response text first to debug any JSON parsing issues
            const responseText = await response.text();
            console.log('Response text:', responseText);

            // Show raw response for debugging
            showResponse(`Raw server response: ${responseText}`, false);

            // Try to parse the response as JSON
            let data;
            try {
                data = JSON.parse(responseText);
                console.log('Login response data:', data);
            } catch (jsonError) {
                console.error('Error parsing JSON:', jsonError);
                showResponse(`Error parsing response: ${jsonError.message}\n\nRaw response: ${responseText}`, true);
                return;
            }

            if (response.ok) {
                // Save token using the state manager
                KidVest.StateManager.setToken(data.access_token);
                console.log('Token saved using StateManager:', data.access_token);

                // Get user profile
                const userResponse = await fetch(`${API_BASE_URL}/users/me`, {
                    headers: {
                        'Authorization': `Bearer ${data.access_token}`
                    }
                });

                const userData = await userResponse.json();

                showResponse({
                    message: 'Login successful!',
                    token: data,
                    user: userData
                });

                // Redirect to dashboard using the state manager
                setTimeout(() => {
                    KidVest.StateManager.navigateTo('dashboard');
                }, 2000);
            } else {
                showResponse(data, true);
            }
        } catch (error) {
            console.error('Login error:', error);
            showResponse(`Error: ${error.message}`, true);
        }
    });
});