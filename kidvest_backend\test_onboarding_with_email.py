import requests
import json
import uuid
import datetime

# Generate a unique email address using timestamp and UUID
timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
unique_id = str(uuid.uuid4())[:8]
unique_email = f"test.user.{timestamp}.{unique_id}@example.com"

# Test data with a unique email address
test_data = {
    "full_name": "<PERSON>",
    "email": unique_email,  # Unique email address
    "dob": "1992-03-15",
    "phone_number": "************",
    "street_address": "789 Pine Street",
    "city": "Boston",
    "state": "MA",
    "postal_code": "02108",
    "country": "USA",
    "ssn": "***********",
    "employment_status": "EMPLOYED",
    "funding_source": "employment_income"
}

print(f"Using unique email: {unique_email}")

# Send the request to your local API
url = "http://localhost:8000/onboarding/alpaca/"
headers = {"Content-Type": "application/json"}

print("Sending onboarding request...")
response = requests.post(url, json=test_data, headers=headers)

print(f"Status Code: {response.status_code}")
try:
    print(f"Response: {json.dumps(response.json(), indent=2)}")
except:
    print(f"Response: {response.text}")
