# Phase 2: Relationship Tracking and User Linkage Logic

## 🎯 **Overview**

Phase 2 implements comprehensive relationship tracking and user linkage logic to support gifting networks and social wall features. This creates a foundation for controlled access to children's profiles and future social features.

## 🏗️ **Backend Implementation**

### **New Database Model: Relationships**

```sql
CREATE TABLE relationships (
    id UUID PRIMARY KEY,
    from_user_id UUID REFERENCES users(id),
    to_child_id UUID REFERENCES child_profiles(id),
    relationship_type ENUM('parent', 'grandparent', 'aunt_uncle', 'sibling', 'cousin', 'family_friend', 'godparent', 'other'),
    status ENUM('pending', 'active', 'declined', 'blocked'),
    description TEXT,
    requested_by_user_id UUID REFERENCES users(id),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    approved_at TIMESTAMP
);
```

### **Relationship Types**
- **parent**: Direct parent relationship
- **grandparent**: Grandparent relationship
- **aunt_uncle**: Aunt or uncle relationship
- **sibling**: Sibling relationship
- **cousin**: Cousin relationship
- **family_friend**: Family friend relationship
- **godparent**: Godparent relationship
- **other**: Other relationship types

### **Relationship Status**
- **pending**: Relationship request sent, awaiting approval
- **active**: Relationship approved and active
- **declined**: Relationship request declined
- **blocked**: Relationship blocked by parent

### **New API Endpoints**

#### **Relationship Management**
```http
# Create relationship request
POST /api/relationships/
{
  "to_child_handle": "emma-johnson-123",
  "relationship_type": "family_friend",
  "description": "Family friend from church"
}

# Get user's relationships
GET /api/relationships/?status=pending
Response: {
  "relationships": [...],
  "total_count": 5,
  "pending_count": 2,
  "active_count": 3
}

# Update relationship status
PATCH /api/relationships/{id}
{
  "status": "active"
}

# Delete relationship
DELETE /api/relationships/{id}

# Get child's relationships
GET /api/child-profiles/{id}/relationships?status=active

# Search for child to request relationship
POST /api/relationships/search
{
  "child_handle": "emma-johnson-123",
  "relationship_type": "family_friend"
}
```

### **Enhanced CRUD Operations**

#### **New Functions in `crud.py`**
- `create_relationship_request()` - Create new relationship request
- `get_relationship()` - Get relationship by ID
- `get_user_relationships()` - Get all relationships for a user
- `get_child_relationships()` - Get relationships for a specific child
- `update_relationship_status()` - Approve/decline relationships
- `delete_relationship()` - Remove relationships
- `check_user_child_relationship()` - Verify active relationship
- `get_relationship_with_details()` - Get relationship with user/child info

#### **Access Control**
- Only child's parent can approve/decline relationship requests
- Relationship requester and child's parent can delete relationships
- Proper authentication and authorization on all endpoints

## 📱 **Frontend Implementation**

### **New Screens**

#### **1. Relationships Tab (`/relationships`)**
- **Main relationships management screen**
- **Features:**
  - View all relationships (sent and received)
  - Filter by status (all, pending, active)
  - Approve/decline pending requests
  - Delete relationships
  - Request new connections button

#### **2. Request Relationship (`/relationships/request`)**
- **Multi-step relationship request flow**
- **Features:**
  - Search for child by handle
  - Select relationship type
  - Add optional description
  - Confirm and send request
  - Success confirmation

### **Enhanced Navigation**
- Added "Relationships" tab to main navigation
- Uses `users` icon from FontAwesome5
- Positioned between "Gift Walls" and "Settings"

### **New API Integration**
- Complete `relationshipsAPI` service in `services/api.ts`
- All CRUD operations for relationships
- Search functionality for finding children
- Status management for approvals/declines

## 🔐 **Security & Access Control**

### **Relationship Verification**
- Only users with active relationships can send gifts (future enforcement)
- Parents control who can connect with their children
- Proper authentication required for all relationship operations

### **Privacy Protection**
- Child handles are used for public discovery
- Personal information only shared with approved relationships
- Parents have full control over relationship approvals

### **Authorization Levels**
1. **Public**: Can view child's gift wall (if public)
2. **Pending**: Relationship requested but not approved
3. **Active**: Full access to send gifts and interact
4. **Blocked**: Explicitly blocked from interaction

## 🎨 **User Experience**

### **Relationship Request Flow**
1. **Search**: User enters child's handle
2. **Verify**: System confirms child exists
3. **Request**: User selects relationship type and adds description
4. **Confirm**: Review request details
5. **Send**: Submit request to child's parent
6. **Notify**: Parent receives notification to approve/decline

### **Parent Approval Flow**
1. **Notification**: Parent sees pending relationship request
2. **Review**: View requester information and relationship details
3. **Decision**: Approve, decline, or block the relationship
4. **Confirmation**: Both parties notified of decision

### **Visual Design**
- **Status badges**: Color-coded status indicators
- **Relationship icons**: Different icons for each relationship type
- **Action buttons**: Clear approve/decline options
- **Empty states**: Helpful guidance when no relationships exist

## 🔄 **Integration with Existing Features**

### **Gift Wall Enhancement**
- Foundation for future gift access control
- Currently allows all gifts (Phase 2 preparation)
- Comment added for future relationship enforcement

### **Child Profiles**
- Relationships can be viewed per child
- Parents can manage connections for each child
- Integration with portfolio and gift management

### **User Management**
- Relationships linked to user accounts
- Proper parent-child ownership verification
- Support for multiple children per parent

## 🚀 **Future Enhancements (Phase 3+)**

### **Social Wall Features**
- Public posts on child profiles
- Relationship-based content visibility
- Activity feeds for connected users

### **Enhanced Gifting**
- Relationship-based gift restrictions
- Gift approval workflows
- Relationship-specific gift limits

### **Notifications**
- Real-time relationship request notifications
- Email notifications for important events
- Push notifications for mobile app

### **Advanced Relationship Management**
- Relationship expiration dates
- Bulk relationship management
- Relationship analytics and insights

## 📊 **Database Schema Updates**

### **New Tables**
- `relationships` - Core relationship tracking
- Proper foreign key constraints
- Indexed for performance

### **Enhanced Queries**
- Efficient relationship lookups
- Status-based filtering
- User-child relationship verification

## 🧪 **Testing Strategy**

### **Backend Testing**
- Unit tests for all CRUD operations
- Integration tests for API endpoints
- Authorization and security testing
- Database constraint validation

### **Frontend Testing**
- Component testing for relationship screens
- API integration testing
- User flow testing (request → approval)
- Error handling and edge cases

### **End-to-End Testing**
- Complete relationship lifecycle testing
- Multi-user scenario testing
- Permission and access control verification

## 📈 **Performance Considerations**

### **Database Optimization**
- Proper indexing on relationship queries
- Efficient joins for relationship details
- Pagination for large relationship lists

### **API Efficiency**
- Minimal data transfer for relationship lists
- Cached relationship status checks
- Optimized search functionality

## 🎉 **Phase 2 Completion Status**

### ✅ **Completed Features**
- [x] Relationship database model and migrations
- [x] Complete backend API endpoints
- [x] CRUD operations with proper authorization
- [x] Frontend relationship management screens
- [x] Request relationship workflow
- [x] Approve/decline functionality
- [x] Integration with main navigation
- [x] API service layer implementation
- [x] Security and access control
- [x] User experience design

### 🔄 **Ready for Phase 3**
- Foundation for social wall features
- Relationship-based access control
- Scalable architecture for future enhancements
- Complete user linkage system

## 📝 **Usage Examples**

### **Request a Relationship**
1. Navigate to Relationships tab
2. Tap "Request Connection"
3. Enter child's handle (e.g., "emma-johnson-123")
4. Select relationship type (e.g., "Family Friend")
5. Add description (optional)
6. Send request

### **Approve a Relationship**
1. Navigate to Relationships tab
2. View pending requests
3. Review request details
4. Tap "Approve" or "Decline"
5. Relationship status updated

### **Manage Relationships**
1. View all relationships by status
2. Filter by pending/active
3. Delete unwanted relationships
4. Monitor connection activity

**Phase 2 relationship tracking and user linkage logic is now complete and ready for production use!** 🎯
