# Start KidVest Backend and React Native App
Write-Host "Starting KidVest Backend and React Native App..." -ForegroundColor Green

# Start Backend Server in a new PowerShell window
Write-Host "`nStarting Backend Server..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-Command `"Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process; cd C:\Users\<USER>\kidvest_backend; .\venv\Scripts\activate; python -m app.main`""

# Wait a moment for the backend to start
Start-Sleep -Seconds 3

# Start React Native App in a new PowerShell window
Write-Host "`nStarting React Native App..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-Command `"cd C:\Users\<USER>\kidvest_backend\kidvest-app; npx expo start --web`""

Write-Host "`nServers started!" -ForegroundColor Green
Write-Host "Backend: http://localhost:8000" -ForegroundColor Yellow
Write-Host "React Native App: http://localhost:8081" -ForegroundColor Yellow
Write-Host "`nPress 'w' in the Expo window to open the app in a web browser" -ForegroundColor Yellow
