// API Base URL
// Use the fixed backend URL
const API_BASE_URL = 'http://localhost:8001/api';

// For debugging
console.log('Auth UI script loaded');

// Initialize when DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded');

    // DOM Elements
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');
    const apiResponse = document.getElementById('api-response');

    console.log('Login form:', loginForm);
    console.log('Register form:', registerForm);

    if (!loginForm || !registerForm) {
        console.error('Could not find form elements!');
        return;
    }

    // Show API Response
    function showResponse(response, isError = false) {
        apiResponse.style.display = 'block';
        apiResponse.style.backgroundColor = isError ? '#f8d7da' : '#d4edda';
        apiResponse.style.color = isError ? '#721c24' : '#155724';
        apiResponse.textContent = typeof response === 'object' ? JSON.stringify(response, null, 2) : response;
    }

    // Handle Registration
    console.log('Setting up registration form handler');
    registerForm.addEventListener('submit', async (e) => {
        console.log('Register form submitted');
        e.preventDefault();

        const name = document.getElementById('register-name').value;
        const email = document.getElementById('register-email').value;
        const password = document.getElementById('register-password').value;
        const confirmPassword = document.getElementById('register-confirm-password').value;

        console.log('Form values:', { name, email, password: '***', confirmPassword: '***' });

        // Validate passwords match
        if (password !== confirmPassword) {
            showResponse('Passwords do not match', true);
            return;
        }

        try {
            console.log('Sending registration request...');
            console.log('API URL:', `${API_BASE_URL}/register`);
            const requestBody = {
                name,
                email,
                password,
                user_type: 'parent',
                phone_number: null,
                address: null,
                city: null,
                state: null,
                postal_code: null,
                country: null
            };
            console.log('Request body:', JSON.stringify(requestBody));

            const response = await fetch(`${API_BASE_URL}/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            console.log('Registration response status:', response.status);

            if (response.ok) {
                try {
                    const data = await response.json();
                    console.log('Registration response data:', data);

                    showResponse({
                        message: 'Registration successful! You can now log in.',
                        user: data
                    });

                    // Clear form
                    registerForm.reset();

                    // Switch to login tab
                    document.getElementById('login-tab').click();
                } catch (jsonError) {
                    console.error('Error parsing JSON for successful response:', jsonError);
                    // Even if we can't parse the JSON, if the status is OK, consider it a success
                    showResponse('Registration successful! You can now log in.', false);

                    // Clear form
                    registerForm.reset();

                    // Switch to login tab
                    document.getElementById('login-tab').click();
                }
            } else {
                try {
                    const errorData = await response.json();
                    console.error('Error response data:', errorData);
                    showResponse(errorData, true);
                } catch (jsonError) {
                    console.error('Error parsing JSON for error response:', jsonError);
                    try {
                        const errorText = await response.text();
                        console.error('Error response text:', errorText);
                        showResponse(`Server error: ${errorText}`, true);
                    } catch (textError) {
                        console.error('Error getting response text:', textError);
                        showResponse(`Error: Status ${response.status} - ${response.statusText}`, true);
                    }
                }
            }
        } catch (error) {
            console.error('Registration error:', error);
            console.error('Error details:', error.stack);
            showResponse(`Error: ${error.message}\nPlease check the browser console for more details.`, true);
        }
});

    // Handle Login
    loginForm.addEventListener('submit', async (e) => {
        console.log('Login form submitted');
        e.preventDefault();

        const email = document.getElementById('login-email').value;
        const password = document.getElementById('login-password').value;

        try {
            // Create form data for OAuth2 password flow
            const formData = new URLSearchParams();
            formData.append('username', email);
            formData.append('password', password);

            console.log('Sending login request...');
            const response = await fetch(`${API_BASE_URL}/token`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: formData
            });

            console.log('Login response status:', response.status);

            if (response.ok) {
                try {
                    const data = await response.json();
                    console.log('Login response data:', data);

                    // Save token to localStorage
                    localStorage.setItem('access_token', data.access_token);

                    try {
                        // Get user profile
                        const userResponse = await fetch(`${API_BASE_URL}/users/me`, {
                            headers: {
                                'Authorization': `Bearer ${data.access_token}`
                            }
                        });

                        if (userResponse.ok) {
                            const userData = await userResponse.json();
                            showResponse({
                                message: 'Login successful!',
                                token: data,
                                user: userData
                            });
                        } else {
                            // Still consider login successful even if profile fetch fails
                            showResponse({
                                message: 'Login successful, but could not fetch profile.',
                                token: data
                            });
                        }
                    } catch (profileError) {
                        console.error('Error fetching profile:', profileError);
                        showResponse({
                            message: 'Login successful, but could not fetch profile.',
                            token: data,
                            error: profileError.message
                        });
                    }

                    // Redirect to dashboard
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 2000);
                } catch (jsonError) {
                    console.error('Error parsing JSON for successful login:', jsonError);
                    // Even if we can't parse the JSON, if the status is OK, consider it a success
                    showResponse('Login successful!', false);

                    // Redirect to dashboard
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 2000);
                }
            } else {
                try {
                    const errorData = await response.json();
                    console.error('Error response data:', errorData);
                    showResponse(errorData, true);
                } catch (jsonError) {
                    console.error('Error parsing JSON for error response:', jsonError);
                    try {
                        const errorText = await response.text();
                        console.error('Error response text:', errorText);
                        showResponse(`Server error: ${errorText}`, true);
                    } catch (textError) {
                        console.error('Error getting response text:', textError);
                        showResponse(`Error: Status ${response.status} - ${response.statusText}`, true);
                    }
                }
            }
        } catch (error) {
            console.error('Login error:', error);
            showResponse(`Error: ${error.message}`, true);
        }
});
});