<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Registration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        #response {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Test Registration</h1>
    
    <div class="form-group">
        <label for="name">Full Name</label>
        <input type="text" id="name" value="Test User">
    </div>
    
    <div class="form-group">
        <label for="email">Email</label>
        <input type="email" id="email" value="<EMAIL>">
    </div>
    
    <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" value="password123">
    </div>
    
    <div class="form-group">
        <label for="backend-url">Backend URL</label>
        <input type="text" id="backend-url" value="http://localhost:8001/api/register">
    </div>
    
    <button onclick="testRegister()">Test Register</button>
    
    <div id="response"></div>
    
    <script>
        async function testRegister() {
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const backendUrl = document.getElementById('backend-url').value;
            
            const responseDiv = document.getElementById('response');
            responseDiv.textContent = 'Sending request...';
            
            const data = {
                name,
                email,
                password,
                user_type: 'parent'
            };
            
            try {
                console.log('Sending request to:', backendUrl);
                console.log('Request data:', data);
                
                const response = await fetch(backendUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                console.log('Response status:', response.status);
                
                try {
                    const responseData = await response.json();
                    console.log('Response data:', responseData);
                    responseDiv.textContent = JSON.stringify(responseData, null, 2);
                } catch (jsonError) {
                    console.error('Error parsing JSON:', jsonError);
                    const responseText = await response.text();
                    responseDiv.textContent = `Status: ${response.status}\nResponse: ${responseText}`;
                }
            } catch (error) {
                console.error('Error:', error);
                responseDiv.textContent = `Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
