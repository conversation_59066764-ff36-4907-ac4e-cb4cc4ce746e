#!/usr/bin/env powershell

# KidVest Gift Wall Startup Script
Write-Host "🚀 Starting KidVest Gift Wall System..." -ForegroundColor Green

# Set execution policy for this session
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process -Force

# Check if virtual environment exists
if (-not (Test-Path ".\venv\Scripts\activate.ps1")) {
    Write-Host "❌ Virtual environment not found. Please run setup first." -ForegroundColor Red
    exit 1
}

# Activate virtual environment
Write-Host "📦 Activating virtual environment..." -ForegroundColor Yellow
& ".\venv\Scripts\activate.ps1"

# Function to start backend
function Start-Backend {
    Write-Host "🔧 Starting FastAPI Backend (Port 8000)..." -ForegroundColor Cyan
    Start-Process powershell -ArgumentList @(
        "-Command",
        "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process; .\venv\Scripts\activate; uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
    ) -WindowStyle Normal
}

# Function to start gift wall UI
function Start-GiftWallUI {
    Write-Host "🎁 Starting Gift Wall UI (Port 8082)..." -ForegroundColor Cyan
    Start-Process powershell -ArgumentList @(
        "-Command",
        "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process; .\venv\Scripts\activate; cd gift-wall-ui; python server.py"
    ) -WindowStyle Normal
}

# Start both servers
Start-Backend
Start-Sleep -Seconds 2
Start-GiftWallUI

# Wait a moment for servers to start
Write-Host "⏳ Waiting for servers to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Test if servers are running
Write-Host "🧪 Testing server connectivity..." -ForegroundColor Yellow

try {
    $backendTest = Invoke-WebRequest -Uri "http://localhost:8000/" -Method GET -TimeoutSec 5
    Write-Host "✅ Backend server is running!" -ForegroundColor Green
} catch {
    Write-Host "❌ Backend server failed to start" -ForegroundColor Red
}

try {
    $frontendTest = Invoke-WebRequest -Uri "http://localhost:8082/" -Method GET -TimeoutSec 5
    Write-Host "✅ Gift Wall UI server is running!" -ForegroundColor Green
} catch {
    Write-Host "❌ Gift Wall UI server failed to start" -ForegroundColor Red
}

# Display access information
Write-Host ""
Write-Host "🎉 KidVest Gift Wall System Started!" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Gray
Write-Host "📊 Backend API:     http://localhost:8000" -ForegroundColor White
Write-Host "🎁 Gift Wall UI:    http://localhost:8082" -ForegroundColor White
Write-Host ""
Write-Host "🧪 Test Gift Wall:  http://localhost:8082/wall/test-child-163809" -ForegroundColor Cyan
Write-Host "📝 API Docs:        http://localhost:8000/docs" -ForegroundColor Cyan
Write-Host ""
Write-Host "💡 Tips:" -ForegroundColor Yellow
Write-Host "   • Use the test gift wall URL above to test Stripe checkout" -ForegroundColor Gray
Write-Host "   • Test card: 4242 4242 4242 4242" -ForegroundColor Gray
Write-Host "   • Check browser console for debugging info" -ForegroundColor Gray
Write-Host "   • Press Ctrl+C in server windows to stop" -ForegroundColor Gray
Write-Host ""
Write-Host "🔧 Troubleshooting:" -ForegroundColor Yellow
Write-Host "   • If gift creation fails, check both server windows for errors" -ForegroundColor Gray
Write-Host "   • Make sure Stripe API key is configured in .env file" -ForegroundColor Gray
Write-Host "   • Check browser network tab for API request details" -ForegroundColor Gray
Write-Host ""

# Keep this window open
Write-Host "Press any key to exit..." -ForegroundColor Green
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
