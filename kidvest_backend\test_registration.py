import requests
import json

def test_registration():
    """Test the registration API directly"""
    print("=== Testing Registration API ===")
    
    # API endpoint
    url = "http://localhost:8000/api/register"
    
    # Request data
    data = {
        "name": "Test User",
        "email": "<EMAIL>",
        "password": "password123",
        "user_type": "parent"
    }
    
    # Print request details
    print(f"Request URL: {url}")
    print(f"Request data: {json.dumps(data, indent=2)}")
    
    # Send the request
    try:
        response = requests.post(
            url,
            json=data,
            headers={"Content-Type": "application/json"}
        )
        
        # Print response details
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"Response data: {json.dumps(response_data, indent=2)}")
            return response_data
        except json.JSONDecodeError:
            print(f"Response is not JSON: {response.text}")
            return None
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_registration()
