# app/alpaca_service.py
import os
import requests
import json
import traceback
import base64

def onboard_alpaca_user(user_payload: dict):
    # Use the correct Broker API endpoint
    url = "https://broker-api.sandbox.alpaca.markets/v1/accounts"

    # Make sure API keys are available
    api_key = os.getenv("ALPACA_API_KEY")
    api_secret = os.getenv("ALPACA_SECRET_KEY")

    print(f"\n==== ALPACA API CREDENTIALS ====\nAPI Key: {api_key[:5]}...{api_key[-5:]}\nAPI Secret: {api_secret[:5]}...{api_secret[-5:]}")

    if not api_key or not api_secret:
        print("ERROR: Alpaca API keys not found in environment variables")
        return None, {"error": "API credentials not configured"}

    # Create HTTP Basic Authentication header
    auth_string = f"{api_key}:{api_secret}"
    encoded_auth = base64.b64encode(auth_string.encode()).decode()

    headers = {
        "Authorization": f"Basic {encoded_auth}",
        "Content-Type": "application/json"
    }

    # Debug: Print request details
    print("\n==== ALPACA BROKER API REQUEST ====")
    print("Request URL:", url)
    print("Request Headers (masked):", json.dumps({k: (v[:10] + "..." if k.lower() == "authorization" else v) for k, v in headers.items()}, indent=2))
    print("Request Payload:", json.dumps(user_payload, indent=2))

    try:
        response = requests.post(url, headers=headers, json=user_payload, timeout=30)

        # Debug: Print response details
        print("\n==== ALPACA BROKER API RESPONSE ====")
        print("Response Status Code:", response.status_code)
        print("Response Headers:", json.dumps(dict(response.headers), indent=2))

        # Get the request ID for troubleshooting
        request_id = response.headers.get("X-Request-ID", "Unknown")
        print(f"Request ID: {request_id} (save this for troubleshooting)")

        try:
            response_json = response.json()
            print("Response Body:", json.dumps(response_json, indent=2))

            if response.status_code not in [200, 201]:
                error_details = {
                    "error": response_json.get("message", "Unknown error"),
                    "details": response_json,
                    "request_id": request_id
                }
                return None, error_details

            return response_json, None

        except json.JSONDecodeError:
            print("Response Body (not JSON):", response.text)
            return None, {"error": "Invalid JSON response", "details": response.text, "request_id": request_id}

    except requests.exceptions.RequestException as e:
        error_details = {
            "error": "Request failed",
            "details": str(e),
            "traceback": traceback.format_exc()
        }
        print("\n==== ALPACA BROKER API ERROR ====")
        print(json.dumps(error_details, indent=2))
        return None, error_details
