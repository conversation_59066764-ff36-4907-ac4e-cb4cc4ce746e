# app/alpaca_service.py
import os
import requests
import json
import traceback
import base64

def onboard_alpaca_user(user_payload: dict):
    # Use the correct Broker API endpoint
    url = "https://broker-api.sandbox.alpaca.markets/v1/accounts"

    # Make sure API keys are available
    api_key = os.getenv("ALPACA_API_KEY")
    api_secret = os.getenv("ALPACA_SECRET_KEY")

    print(f"\n==== ALPACA API CREDENTIALS ====\nAPI Key: {api_key[:5]}...{api_key[-5:]}\nAPI Secret: {api_secret[:5]}...{api_secret[-5:]}")

    if not api_key or not api_secret:
        print("ERROR: Alpaca API keys not found in environment variables")
        return None, {"error": "API credentials not configured"}

    # Create HTTP Basic Authentication header
    auth_string = f"{api_key}:{api_secret}"
    encoded_auth = base64.b64encode(auth_string.encode()).decode()

    headers = {
        "Authorization": f"Basic {encoded_auth}",
        "Content-Type": "application/json"
    }

    # Debug: Print request details
    print("\n==== ALPACA BROKER API REQUEST ====")
    print("Request URL:", url)
    print("Request Headers (masked):", json.dumps({k: (v[:10] + "..." if k.lower() == "authorization" else v) for k, v in headers.items()}, indent=2))
    print("Request Payload:", json.dumps(user_payload, indent=2))

    try:
        response = requests.post(url, headers=headers, json=user_payload, timeout=30)

        # Debug: Print response details
        print("\n==== ALPACA BROKER API RESPONSE ====")
        print("Response Status Code:", response.status_code)
        print("Response Headers:", json.dumps(dict(response.headers), indent=2))

        # Get the request ID for troubleshooting
        request_id = response.headers.get("X-Request-ID", "Unknown")
        print(f"Request ID: {request_id} (save this for troubleshooting)")

        try:
            response_json = response.json()
            print("Response Body:", json.dumps(response_json, indent=2))

            if response.status_code not in [200, 201]:
                error_details = {
                    "error": response_json.get("message", "Unknown error"),
                    "details": response_json,
                    "request_id": request_id
                }
                return None, error_details

            return response_json, None

        except json.JSONDecodeError:
            print("Response Body (not JSON):", response.text)
            return None, {"error": "Invalid JSON response", "details": response.text, "request_id": request_id}

    except requests.exceptions.RequestException as e:
        error_details = {
            "error": "Request failed",
            "details": str(e),
            "traceback": traceback.format_exc()
        }
        print("\n==== ALPACA BROKER API ERROR ====")
        print(json.dumps(error_details, indent=2))
        return None, error_details


def get_alpaca_market_data(symbol: str):
    """Get current market data for a symbol from Alpaca"""
    api_key = os.getenv("ALPACA_API_KEY")
    api_secret = os.getenv("ALPACA_SECRET_KEY")

    if not api_key or not api_secret:
        return None, {"error": "API credentials not configured"}

    # Use Alpaca Market Data API
    url = f"https://data.alpaca.markets/v2/stocks/{symbol}/quotes/latest"

    headers = {
        "APCA-API-KEY-ID": api_key,
        "APCA-API-SECRET-KEY": api_secret
    }

    try:
        response = requests.get(url, headers=headers, timeout=10)

        if response.status_code == 200:
            data = response.json()
            quote = data.get("quote", {})
            return {
                "symbol": symbol,
                "bid_price": quote.get("bp", 0),
                "ask_price": quote.get("ap", 0),
                "last_price": (quote.get("bp", 0) + quote.get("ap", 0)) / 2,  # Midpoint
                "timestamp": quote.get("t")
            }, None
        else:
            return None, {"error": f"Market data request failed: {response.status_code}"}

    except Exception as e:
        return None, {"error": f"Market data request error: {str(e)}"}


def place_alpaca_order(account_id: str, symbol: str, amount_usd: float):
    """Place a market order with Alpaca Broker API"""
    api_key = os.getenv("ALPACA_API_KEY")
    api_secret = os.getenv("ALPACA_SECRET_KEY")

    print(f"\n==== PLACING ALPACA ORDER ====")
    print(f"Account ID: {account_id}")
    print(f"Symbol: {symbol}")
    print(f"Amount USD: ${amount_usd}")

    if not api_key or not api_secret:
        print("ERROR: Alpaca API keys not found")
        return None, {"error": "API credentials not configured"}

    # Get current market price first
    market_data, market_error = get_alpaca_market_data(symbol)
    if market_error:
        print(f"ERROR: Could not get market data: {market_error}")
        return None, market_error

    current_price = market_data["last_price"]
    shares_to_buy = amount_usd / current_price

    print(f"Current Price: ${current_price}")
    print(f"Shares to Buy: {shares_to_buy}")

    # Create HTTP Basic Authentication header
    auth_string = f"{api_key}:{api_secret}"
    encoded_auth = base64.b64encode(auth_string.encode()).decode()

    headers = {
        "Authorization": f"Basic {encoded_auth}",
        "Content-Type": "application/json"
    }

    # Order payload for Alpaca Broker API
    order_payload = {
        "symbol": symbol,
        "qty": shares_to_buy,
        "side": "buy",
        "type": "market",
        "time_in_force": "day"
    }

    url = f"https://broker-api.sandbox.alpaca.markets/v1/trading/accounts/{account_id}/orders"

    print(f"Order URL: {url}")
    print(f"Order Payload: {json.dumps(order_payload, indent=2)}")

    try:
        response = requests.post(url, headers=headers, json=order_payload, timeout=30)

        print(f"Order Response Status: {response.status_code}")
        print(f"Order Response Headers: {dict(response.headers)}")

        if response.status_code in [200, 201]:
            result = response.json()
            print(f"Order Success: {json.dumps(result, indent=2)}")

            return {
                "order_id": result.get("id"),
                "symbol": result.get("symbol"),
                "qty": float(result.get("qty", 0)),
                "side": result.get("side"),
                "status": result.get("status"),
                "submitted_at": result.get("submitted_at"),
                "filled_qty": float(result.get("filled_qty", 0)),
                "filled_avg_price": float(result.get("filled_avg_price", 0)) if result.get("filled_avg_price") else current_price,
                "estimated_price": current_price
            }, None
        else:
            error_data = response.json() if response.content else {"error": "Unknown error"}
            print(f"Order Error: {json.dumps(error_data, indent=2)}")
            return None, error_data

    except Exception as e:
        error_details = {
            "error": "Order request failed",
            "details": str(e),
            "traceback": traceback.format_exc()
        }
        print(f"Order Exception: {json.dumps(error_details, indent=2)}")
        return None, error_details
