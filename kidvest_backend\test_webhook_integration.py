#!/usr/bin/env python3

import requests
import json
import os
import time
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_webhook_endpoint():
    """Test if the webhook endpoint is accessible"""
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🧪 Testing Webhook Integration")
    print("=" * 60)
    
    # Test local webhook endpoint
    local_webhook_url = "http://localhost:8000/webhook/"
    
    print(f"🔗 Testing local webhook endpoint: {local_webhook_url}")
    
    try:
        # Test with a simple GET request first
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print("✅ Backend server is running")
        else:
            print(f"⚠️ Backend server responded with status {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Backend server is not running on port 8000")
        return False
    except Exception as e:
        print(f"❌ Error connecting to backend: {str(e)}")
        return False
    
    return True

def test_ngrok_tunnel():
    """Test if ngrok tunnel is running and get the public URL"""
    print(f"\n🌐 Testing ngrok tunnel...")
    
    try:
        # Get ngrok tunnels
        ngrok_api = requests.get("http://localhost:4040/api/tunnels", timeout=5)
        
        if ngrok_api.status_code == 200:
            tunnels = ngrok_api.json()
            
            if tunnels.get('tunnels') and len(tunnels['tunnels']) > 0:
                public_url = tunnels['tunnels'][0]['public_url']
                print(f"✅ ngrok tunnel is running")
                print(f"🔗 Public URL: {public_url}")
                print(f"🎯 Webhook URL: {public_url}/webhook/")
                
                # Save webhook URL to file
                with open("webhook_url.txt", "w") as f:
                    f.write(f"{public_url}/webhook/")
                
                return public_url
            else:
                print("❌ No ngrok tunnels found")
                return None
        else:
            print(f"❌ ngrok API responded with status {ngrok_api.status_code}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ ngrok is not running (no API on port 4040)")
        return None
    except Exception as e:
        print(f"❌ Error connecting to ngrok API: {str(e)}")
        return None

def create_test_gift():
    """Create a test gift to get a real gift ID for webhook testing"""
    print(f"\n🎁 Creating test gift for webhook testing...")
    
    test_handle = "test-child-165125"
    
    gift_data = {
        "child_profile_handle": test_handle,
        "from_name": "Webhook Test User",
        "from_email": "<EMAIL>",
        "amount_usd": 10.0,
        "message": "Test gift for webhook integration",
        "is_anonymous": False
    }
    
    try:
        api_url = f"http://localhost:8000/api/wall/{test_handle}/gift"
        response = requests.post(
            api_url,
            json=gift_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            response_data = response.json()
            gift_id = response_data.get('gift_id')
            checkout_url = response_data.get('checkout_url')
            
            print(f"✅ Test gift created successfully!")
            print(f"   Gift ID: {gift_id}")
            print(f"   Checkout URL: {checkout_url}")
            
            return gift_id, checkout_url
        else:
            print(f"❌ Failed to create test gift: {response.status_code}")
            return None, None
            
    except Exception as e:
        print(f"❌ Error creating test gift: {str(e)}")
        return None, None

def simulate_webhook_event(gift_id):
    """Simulate a Stripe webhook event"""
    print(f"\n🔔 Simulating Stripe webhook event...")
    
    # Create a simulated checkout.session.completed event
    webhook_payload = {
        "type": "checkout.session.completed",
        "data": {
            "object": {
                "id": f"cs_test_webhook_{int(time.time())}",
                "object": "checkout.session",
                "payment_status": "paid",
                "payment_intent": f"pi_test_webhook_{int(time.time())}",
                "metadata": {
                    "gift_id": gift_id
                }
            }
        }
    }
    
    try:
        webhook_url = "http://localhost:8000/webhook/"
        
        # Note: In a real scenario, this would include Stripe signature headers
        # For testing, we'll use the development mode that skips signature verification
        response = requests.post(
            webhook_url,
            json=webhook_payload,
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'Stripe/1.0 (+https://stripe.com/docs/webhooks)'
            },
            timeout=10
        )
        
        print(f"📡 Webhook request sent to: {webhook_url}")
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"✅ Webhook processed successfully!")
            print(f"   Response: {response_data}")
            return True
        else:
            print(f"❌ Webhook failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data}")
            except:
                print(f"   Error text: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error sending webhook: {str(e)}")
        return False

def check_gift_status(gift_id):
    """Check if the gift status was updated by the webhook"""
    print(f"\n🔍 Checking gift status after webhook...")
    
    try:
        from app.database import SessionLocal
        from app import models
        from uuid import UUID
        
        db = SessionLocal()
        try:
            gift = db.query(models.Gift).filter(models.Gift.id == UUID(gift_id)).first()
            
            if gift:
                print(f"📋 Gift found: {gift.id}")
                print(f"   Payment status: {gift.payment_status}")
                print(f"   Payment intent ID: {gift.payment_intent_id}")
                print(f"   Created at: {gift.created_at}")
                
                if gift.payment_status == "completed":
                    print(f"✅ SUCCESS: Gift status updated to completed!")
                    return True
                else:
                    print(f"❌ Gift status is still: {gift.payment_status}")
                    return False
            else:
                print(f"❌ Gift not found with ID: {gift_id}")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Error checking gift status: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Webhook Integration Test")
    print("=" * 60)
    
    # Test 1: Check if backend is running
    if not test_webhook_endpoint():
        print("\n❌ Backend server test failed. Please start the backend first.")
        return
    
    # Test 2: Check ngrok tunnel
    ngrok_url = test_ngrok_tunnel()
    
    # Test 3: Create a test gift
    gift_id, checkout_url = create_test_gift()
    
    if not gift_id:
        print("\n❌ Failed to create test gift. Cannot proceed with webhook test.")
        return
    
    # Test 4: Simulate webhook event
    webhook_success = simulate_webhook_event(gift_id)
    
    if not webhook_success:
        print("\n❌ Webhook simulation failed.")
        return
    
    # Test 5: Check if gift status was updated
    time.sleep(2)  # Give the webhook time to process
    status_updated = check_gift_status(gift_id)
    
    # Final results
    print("\n" + "=" * 60)
    print("📊 WEBHOOK INTEGRATION TEST RESULTS:")
    print("=" * 60)
    
    if status_updated:
        print("✅ WEBHOOK INTEGRATION: WORKING")
        print("✅ Gift status update: WORKING")
        print("✅ Database integration: WORKING")
        print()
        print("🎯 NEXT STEPS:")
        print("   1. Configure Stripe webhook endpoint with your ngrok URL")
        print("   2. Test with real Stripe checkout payments")
        print("   3. Verify gifts appear in child portfolios after payment")
        
        if ngrok_url:
            print(f"\n🔧 Stripe Webhook Configuration:")
            print(f"   URL: {ngrok_url}/webhook/")
            print(f"   Events: checkout.session.completed, checkout.session.expired")
    else:
        print("❌ WEBHOOK INTEGRATION: FAILED")
        print("❌ Gift status not updated")
        print()
        print("🔧 TROUBLESHOOTING:")
        print("   1. Check backend server logs for errors")
        print("   2. Verify webhook endpoint is accessible")
        print("   3. Check database connection")
        print("   4. Ensure gift ID is valid")
    
    print()
    print("🧪 Test completed!")

if __name__ == "__main__":
    main()
