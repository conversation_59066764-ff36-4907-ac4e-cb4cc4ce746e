import os
from dotenv import load_dotenv
from sqlalchemy import create_engine, text
import sys

# Load environment variables
load_dotenv()

# Get database URL
DATABASE_URL = os.getenv("DATABASE_URL")
print(f"Database URL: {DATABASE_URL}")

try:
    # Create engine
    engine = create_engine(DATABASE_URL)
    
    # Test connection
    with engine.connect() as connection:
        result = connection.execute(text("SELECT 1"))
        print("Database connection successful!")
        
        # Try to query the users table
        try:
            users_result = connection.execute(text("SELECT COUNT(*) FROM users"))
            user_count = users_result.scalar()
            print(f"Number of users in database: {user_count}")
        except Exception as e:
            print(f"Error querying users table: {str(e)}")
            
except Exception as e:
    print(f"Database connection error: {str(e)}")
    sys.exit(1)
