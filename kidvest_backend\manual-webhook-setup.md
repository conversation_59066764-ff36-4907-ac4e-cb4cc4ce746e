# Manual Webhook Setup Guide

## 🚀 **Simple 3-Step Setup**

Since the PowerShell script had issues, here's a manual approach that's guaranteed to work:

### **Step 1: Start Backend Server**

```powershell
# In Terminal 1 - Start Backend
cd kidvest_backend
.\venv\Scripts\activate
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**Keep this terminal open!**

### **Step 2: Start ngrok Tunnel**

```powershell
# In Terminal 2 - Start ngrok
ngrok http 8000
```

**You should see output like:**
```
Session Status                online
Account                       your-account
Version                       3.x.x
Region                        United States (us)
Latency                       -
Web Interface                 http://127.0.0.1:4040
Forwarding                    https://abc123.ngrok-free.app -> http://localhost:8000
```

**Copy the https URL (e.g., `https://abc123.ngrok-free.app`)**

### **Step 3: Configure Stripe Webhook**

1. **Go to Stripe Dashboard:**
   - https://dashboard.stripe.com/webhooks

2. **Add Endpoint:**
   - Click "Add endpoint"
   - Enter: `https://YOUR_NGROK_URL.ngrok-free.app/webhook/`
   - Example: `https://abc123.ngrok-free.app/webhook/`

3. **Select Events:**
   - `checkout.session.completed`
   - `checkout.session.expired`

4. **Get Webhook Secret:**
   - After creating, click on the endpoint
   - Copy the "Signing secret" (starts with `whsec_`)

5. **Update .env File:**
   ```env
   STRIPE_WEBHOOK_SECRET=whsec_your_actual_secret_here
   ```

6. **Restart Backend:**
   - Stop Terminal 1 (Ctrl+C)
   - Restart: `uvicorn app.main:app --reload --host 0.0.0.0 --port 8000`

## 🧪 **Test the Setup**

### **Quick Test:**

```powershell
# In Terminal 3 - Test webhook
cd kidvest_backend
.\venv\Scripts\activate
python test_webhook_integration.py
```

**Expected output:**
```
✅ Backend server is running
✅ ngrok tunnel is running
✅ Test gift created successfully!
✅ Webhook processed successfully!
✅ SUCCESS: Gift status updated to completed!
```

### **Full End-to-End Test:**

1. **Start React Native app:**
   ```bash
   cd kidvest-app-new
   npm start
   ```

2. **Open in browser:** `http://localhost:8081`

3. **Create a gift:**
   - Go to Gifts → Create Gift
   - Fill form and click "Send Gift"

4. **Complete payment:**
   - Use test card: `4242 4242 4242 4242`
   - Complete Stripe checkout

5. **Check result:**
   - Backend should log: "✅ Updated gift {id} to completed"
   - Gift should appear in child portfolio

## 🔧 **Troubleshooting**

### **Issue: ngrok not found**
```bash
# Install ngrok
# 1. Download from https://ngrok.com/download
# 2. Extract to folder
# 3. Add to PATH or use full path
C:\path\to\ngrok.exe http 8000
```

### **Issue: Webhook signature failed**
```
❌ Signature verification failed
```
**Solution:**
- Check STRIPE_WEBHOOK_SECRET in .env
- Make sure it starts with `whsec_`
- Restart backend after updating .env

### **Issue: Gift status not updating**
```
Gift created but status still pending
```
**Solution:**
- Check backend console for webhook logs
- Verify ngrok tunnel is active
- Test webhook with: `python test_webhook_integration.py`

## 📋 **Terminal Setup Summary**

### **Terminal 1: Backend**
```powershell
cd kidvest_backend
.\venv\Scripts\activate
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### **Terminal 2: ngrok**
```powershell
ngrok http 8000
```

### **Terminal 3: React Native**
```bash
cd kidvest-app-new
npm start
```

## ✅ **Success Indicators**

### **Backend Terminal:**
```
INFO:     Uvicorn running on http://0.0.0.0:8000
INFO:     Application startup complete.
```

### **ngrok Terminal:**
```
Forwarding    https://abc123.ngrok-free.app -> http://localhost:8000
```

### **After Payment:**
```
=== Stripe Webhook Received ===
🎯 Checkout session completed!
✅ Updated gift {gift-id} to completed
```

### **In Child Portfolio:**
- Gift appears with correct amount
- Status shows as completed
- Portfolio balance updated

## 🎯 **Why This Works**

1. **Backend** handles API requests and webhook processing
2. **ngrok** exposes local server to internet
3. **Stripe** sends webhook to ngrok URL
4. **Webhook** updates gift status from "pending" to "completed"
5. **Gift** appears in child portfolio

## 🚀 **Quick Commands**

### **Start Everything:**
```powershell
# Terminal 1
cd kidvest_backend && .\venv\Scripts\activate && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Terminal 2  
ngrok http 8000

# Terminal 3
cd kidvest-app-new && npm start
```

### **Test Webhook:**
```powershell
python test_webhook_integration.py
```

### **Check Webhook URL:**
```powershell
# Visit ngrok web interface
start http://localhost:4040
```

**This manual setup is guaranteed to work and will solve your webhook integration issue!** 🎉
