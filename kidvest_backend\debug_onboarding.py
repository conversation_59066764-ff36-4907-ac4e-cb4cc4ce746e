import requests
import json
import uuid
import datetime
import time

# Base URL for API
BASE_URL = "http://localhost:8000"

def test_root():
    """Test if the server is running by hitting the root endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"Root endpoint - Status Code: {response.status_code}")
        print(f"Root endpoint - Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error testing root endpoint: {str(e)}")
        return False

def test_step1():
    """Test step 1 of the onboarding process"""
    # Generate unique data for testing
    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    unique_id = str(uuid.uuid4())[:8]
    unique_email = f"test.user.{timestamp}.{unique_id}@example.com"

    # Step 1: Basic account creation
    step1_data = {
        "full_name": "<PERSON>",
        "email": unique_email
    }

    print(f"\nStep 1: Using unique email: {unique_email}")
    try:
        response = requests.post(f"{BASE_URL}/api/onboarding/step1", json=step1_data)
        print(f"Step 1 - Status Code: {response.status_code}")
        print(f"Step 1 - Response Text: {response.text}")

        if response.status_code == 200:
            try:
                step1_response = response.json()
                print(f"Step 1 - Response JSON: {json.dumps(step1_response, indent=2)}")
                return step1_response.get("session_token")
            except Exception as e:
                print(f"Error parsing JSON: {str(e)}")
                return None
        else:
            print("Step 1 failed")
            return None
    except Exception as e:
        print(f"Error in step 1: {str(e)}")
        return None

def test_step2(session_token):
    """Test step 2 of the onboarding process"""
    # Step 2: Identity verification
    step2_data = {
        "dob": "1992-03-15",
        "phone_number": "************",
        "street_address": "789 Pine Street",
        "city": "Boston",
        "state": "MA",
        "postal_code": "02108",
        "country": "USA",
        "ssn": "***********"
    }

    print(f"\nStep 2: Identity verification with session token: {session_token}")
    try:
        response = requests.post(f"{BASE_URL}/api/onboarding/step2?session_token={session_token}", json=step2_data)
        print(f"Step 2 - Status Code: {response.status_code}")
        print(f"Step 2 - Response Text: {response.text}")

        if response.status_code == 200:
            try:
                step2_response = response.json()
                print(f"Step 2 - Response JSON: {json.dumps(step2_response, indent=2)}")
                return True
            except Exception as e:
                print(f"Error parsing JSON: {str(e)}")
                return False
        else:
            print("Step 2 failed")
            return False
    except Exception as e:
        print(f"Error in step 2: {str(e)}")
        return False

def test_step3(session_token):
    """Test step 3 of the onboarding process"""
    # Step 3: Financial profile
    step3_data = {
        "employment_status": "EMPLOYED",
        "income_range": "50k_100k",
        "net_worth_range": "50k_100k",
        "funding_source": "employment_income",
        "investment_experience": "some",
        "risk_tolerance": "moderate"
    }

    print(f"\nStep 3: Financial profile with session token: {session_token}")
    try:
        response = requests.post(f"{BASE_URL}/api/onboarding/step3?session_token={session_token}", json=step3_data)
        print(f"Step 3 - Status Code: {response.status_code}")
        print(f"Step 3 - Response Text: {response.text}")

        if response.status_code == 200:
            try:
                step3_response = response.json()
                print(f"Step 3 - Response JSON: {json.dumps(step3_response, indent=2)}")
                return True
            except Exception as e:
                print(f"Error parsing JSON: {str(e)}")
                return False
        else:
            print("Step 3 failed")
            return False
    except Exception as e:
        print(f"Error in step 3: {str(e)}")
        return False

def test_step4(session_token):
    """Test step 4 of the onboarding process"""
    # Step 4: Disclosures and agreements
    step4_data = {
        "is_control_person": False,
        "is_affiliated_exchange_or_finra": False,
        "is_politically_exposed": False,
        "immediate_family_exposed": False,
        "customer_agreement_accepted": True,
        "margin_agreement_accepted": True,
        "account_agreement_accepted": True
    }

    print(f"\nStep 4: Disclosures and agreements with session token: {session_token}")
    try:
        response = requests.post(f"{BASE_URL}/api/onboarding/step4?session_token={session_token}", json=step4_data)
        print(f"Step 4 - Status Code: {response.status_code}")
        print(f"Step 4 - Response Text: {response.text}")

        if response.status_code == 200:
            try:
                step4_response = response.json()
                print(f"Step 4 - Response JSON: {json.dumps(step4_response, indent=2)}")
                return True
            except Exception as e:
                print(f"Error parsing JSON: {str(e)}")
                return False
        else:
            print("Step 4 failed")
            return False
    except Exception as e:
        print(f"Error in step 4: {str(e)}")
        return False

def test_submit(session_token):
    """Test final submission to Alpaca"""
    print(f"\nFinal submission to Alpaca with session token: {session_token}")
    try:
        response = requests.post(f"{BASE_URL}/api/onboarding/submit?session_token={session_token}")
        print(f"Submit - Status Code: {response.status_code}")
        print(f"Submit - Response Text: {response.text}")

        if response.status_code == 200:
            try:
                submit_response = response.json()
                print(f"Submit - Response JSON: {json.dumps(submit_response, indent=2)}")
                return True
            except Exception as e:
                print(f"Error parsing JSON: {str(e)}")
                return False
        else:
            print("Submission failed")
            return False
    except Exception as e:
        print(f"Error in submission: {str(e)}")
        return False

def main():
    """Main function to run the tests"""
    print("Testing if server is running...")
    if not test_root():
        print("Server is not running or not responding. Exiting.")
        return

    print("\nServer is running. Testing step 1...")
    session_token = test_step1()

    if not session_token:
        print("Step 1 failed. Exiting.")
        return

    print(f"\nStep 1 succeeded. Session token: {session_token}")

    print("\nTesting step 2...")
    if not test_step2(session_token):
        print("Step 2 failed. Exiting.")
        return

    print("\nStep 2 succeeded.")

    print("\nTesting step 3...")
    if not test_step3(session_token):
        print("Step 3 failed. Exiting.")
        return

    print("\nStep 3 succeeded.")

    print("\nTesting step 4...")
    if not test_step4(session_token):
        print("Step 4 failed. Exiting.")
        return

    print("\nStep 4 succeeded.")

    print("\nTesting final submission...")
    if not test_submit(session_token):
        print("Final submission failed. Exiting.")
        return

    print("\nFinal submission succeeded. Multi-step onboarding complete!")

if __name__ == "__main__":
    main()
