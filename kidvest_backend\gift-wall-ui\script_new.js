// API Base URL
// Using 127.0.0.1 instead of localhost to avoid potential DNS resolution issues
const API_BASE_URL = 'http://127.0.0.1:8000/api';

// Add error handling for fetch operations
const fetchWithRetry = async (url, options = {}, retries = 3, delay = 500) => {
    try {
        const response = await fetch(url, options);
        return response;
    } catch (error) {
        if (retries > 0) {
            console.log(`Fetch failed, retrying... (${retries} retries left)`);
            await new Promise(resolve => setTimeout(resolve, delay));
            return fetchWithRetry(url, options, retries - 1, delay * 1.5);
        }
        throw error;
    }
};

// DOM Elements
const profileContainer = document.getElementById('profile-container');
const giftsList = document.getElementById('gifts-list');
const giftForm = document.getElementById('gift-form');
const giftModal = document.getElementById('gift-modal');
const openGiftBtn = document.getElementById('open-gift-modal');
const closeModalBtn = document.getElementById('close-modal');
const cancelBtn = document.getElementById('cancel-btn');
const submitGiftBtn = document.getElementById('submit-gift-btn');
const toast = document.getElementById('toast');
const toastMessage = document.getElementById('toast-message');

// Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    // Get the handle from the URL
    const handle = window.location.pathname.split('/').pop();
    
    // Load the gift wall
    loadGiftWall(handle);
    
    // Open gift modal
    openGiftBtn.addEventListener('click', openGiftModal);
    
    // Close gift modal
    closeModalBtn.addEventListener('click', () => giftModal.classList.remove('active'));
    cancelBtn.addEventListener('click', () => giftModal.classList.remove('active'));
    
    // Submit gift form
    giftForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // Validate form
        const fromName = document.getElementById('from_name').value;
        if (!fromName) {
            showToast('Please enter your name');
            return;
        }
        
        const fromEmail = document.getElementById('from_email').value;
        if (!fromEmail || !isValidEmail(fromEmail)) {
            showToast('Please enter a valid email address');
            return;
        }
        
        // Get the amount
        const amountValue = document.getElementById('amount').value;
        if (!amountValue || parseInt(amountValue) < 1) {
            showToast('Please enter a valid amount');
            return;
        }
        
        // Create the gift
        await createGift();
    });
});

// State
let childProfile = null;

// Get the handle from the URL
const handle = window.location.pathname.split('/').pop();

// Functions
async function loadGiftWall(handle) {
    try {
        profileContainer.innerHTML = '<div class="loading">Loading profile...</div>';
        giftsList.innerHTML = '<div class="loading">Loading gifts...</div>';
        
        console.log('Loading gift wall for handle:', handle);
        console.log('API URL:', `${API_BASE_URL}/wall/${handle}`);
        
        let data;
        try {
            const response = await fetchWithRetry(`${API_BASE_URL}/wall/${handle}`, {}, 1);
            console.log('Response status:', response.status);
            
            if (!response.ok) {
                const errorText = await response.text();
                console.error('Error response:', errorText);
                throw new Error(`Failed to load gift wall: ${response.status} ${response.statusText}`);
            }
            
            data = await response.json();
        } catch (apiError) {
            console.log('Using mock data due to API error:', apiError.message);
            // Use mock data if API fails
            if (typeof mockProfileData !== 'undefined') {
                data = mockProfileData;
                console.log('Mock data loaded successfully');
            } else {
                throw new Error('Mock data not available');
            }
        }
        
        childProfile = data.profile;
        renderProfile(data.profile);
        renderGifts(data.gifts);
    } catch (error) {
        showToast(`Error: ${error.message}`);
        profileContainer.innerHTML = '<div class="error">Failed to load profile. Please try again.</div>';
        giftsList.innerHTML = '<div class="error">Failed to load gifts. Please try again.</div>';
    }
}

async function createGift() {
    try {
        const formData = {
            child_profile_handle: handle,
            from_name: document.getElementById('from_name').value,
            from_email: document.getElementById('from_email').value,
            amount_usd: parseInt(document.getElementById('amount').value),
            message: document.getElementById('message').value,
            is_anonymous: document.getElementById('is_anonymous').checked
        };
        
        console.log('Creating gift with data:', formData);
        console.log('API URL:', `${API_BASE_URL}/wall/${handle}/gift`);
        
        submitGiftBtn.disabled = true;
        submitGiftBtn.textContent = 'Processing...';
        
        try {
            const response = await fetchWithRetry(`${API_BASE_URL}/wall/${handle}/gift`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            }, 1);
            
            console.log('Response status:', response.status);
            
            if (!response.ok) {
                const errorText = await response.text();
                console.error('Error response:', errorText);
                try {
                    const errorData = JSON.parse(errorText);
                    throw new Error(errorData.detail || 'Failed to create gift');
                } catch (e) {
                    throw new Error(`Failed to create gift: ${response.status} ${response.statusText}`);
                }
            }
            
            const data = await response.json();
            console.log('API response:', data);
            
            // Redirect to Stripe checkout
            if (data.checkout_url) {
                console.log('Redirecting to checkout URL:', data.checkout_url);
                window.location.href = data.checkout_url;
            } else {
                throw new Error('No checkout URL provided');
            }
        } catch (apiError) {
            console.log('Using mock success for demo:', apiError.message);
            // For demo purposes, show success message
            showToast('Gift created successfully (demo mode)');
            
            // Reload the gift wall to show the new gift
            setTimeout(() => {
                giftModal.classList.remove('active');
                loadGiftWall(handle);
            }, 2000);
        }
    } catch (error) {
        showToast(`Error: ${error.message}`);
        submitGiftBtn.disabled = false;
        submitGiftBtn.textContent = 'Post & Gift';
    }
}

// UI Functions
function renderProfile(profile) {
    profileContainer.innerHTML = `
        <div class="profile-header">
            <div class="profile-avatar">${getInitials(profile.name)}</div>
            <h1 class="profile-name">${profile.name}</h1>
            <div class="profile-handle">@${profile.handle}</div>
            ${profile.age ? `<div class="profile-age">${profile.age} years old</div>` : ''}
        </div>
        <div class="profile-bio">${profile.bio || ''}</div>
        <div class="profile-stats">
            <div class="stat-item">
                <div class="stat-value">${formatCurrency(getTotalAmount())}</div>
                <div class="stat-label">Total Gifts</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${getGiftCount()}</div>
                <div class="stat-label">Gift Count</div>
            </div>
        </div>
    `;
}

function renderGifts(gifts) {
    if (!gifts || gifts.length === 0) {
        giftsList.innerHTML = `
            <div class="empty-gifts">
                <p>No gifts yet. Be the first to send a gift!</p>
            </div>
        `;
        return;
    }
    
    giftsList.innerHTML = '';
    
    gifts.forEach(gift => {
        const giftCard = document.createElement('div');
        giftCard.className = 'gift-card';
        giftCard.innerHTML = `
            <div class="gift-amount">${formatCurrency(gift.amount_usd)}</div>
            ${gift.message ? `<div class="gift-message">"${gift.message}"</div>` : ''}
            ${gift.from_name ? `<div class="gift-from">From: ${gift.from_name}</div>` : '<div class="gift-from">From: Anonymous</div>'}
            <div class="gift-date">${formatDate(gift.created_at)}</div>
        `;
        
        giftsList.appendChild(giftCard);
    });
}

function openGiftModal() {
    // Reset form
    giftForm.reset();
    document.getElementById('amount').value = '50';
    
    // Reset submit button
    submitGiftBtn.disabled = false;
    submitGiftBtn.textContent = 'Post & Gift';
    
    // Set profile info in modal
    if (childProfile) {
        document.getElementById('modal-avatar').innerHTML = getInitials(childProfile.name);
        document.getElementById('modal-name').textContent = childProfile.name;
        document.getElementById('modal-handle').textContent = '@' + childProfile.handle;
    }
    
    // Show modal
    giftModal.classList.add('active');
}

function getInitials(name) {
    return name
        .split(' ')
        .map(part => part.charAt(0))
        .join('')
        .toUpperCase();
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function getTotalAmount() {
    const gifts = document.querySelectorAll('.gift-card');
    if (gifts.length === 0) return 0;
    
    return Array.from(gifts)
        .map(gift => {
            const amountText = gift.querySelector('.gift-amount').textContent;
            return parseFloat(amountText.replace(/[^0-9.-]+/g, ''));
        })
        .reduce((total, amount) => total + amount, 0);
}

function getGiftCount() {
    return document.querySelectorAll('.gift-card').length;
}

function showToast(message) {
    toastMessage.textContent = message;
    toast.classList.add('active');
    
    setTimeout(() => {
        toast.classList.remove('active');
    }, 3000);
}

function isValidEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

// Success and cancel pages
if (window.location.pathname.includes('/success')) {
    // Show success message
    document.body.innerHTML = `
        <div class="app-container">
            <div class="success-container">
                <div class="success-icon">✓</div>
                <h1>Thank You!</h1>
                <p>Your gift has been successfully processed.</p>
                <a href="/wall/${handle}" class="btn btn-primary">Return to Gift Wall</a>
            </div>
        </div>
    `;
} else if (window.location.pathname.includes('/cancel')) {
    // Show cancel message
    document.body.innerHTML = `
        <div class="app-container">
            <div class="cancel-container">
                <div class="cancel-icon">×</div>
                <h1>Payment Cancelled</h1>
                <p>Your gift has been cancelled. No payment was processed.</p>
                <a href="/wall/${handle}" class="btn btn-primary">Return to Gift Wall</a>
            </div>
        </div>
    `;
}
