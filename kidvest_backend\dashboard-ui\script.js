// API Base URL
const API_BASE_URL = 'http://localhost:8000/api';

// For debugging
console.log('Dashboard UI script loaded');

// DOM Elements
const userNameElement = document.getElementById('user-name');
const logoutBtn = document.getElementById('logout-btn');

// Check Authentication
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded');
    
    const token = localStorage.getItem('access_token');
    if (!token) {
        console.log('No token found, redirecting to login');
        alert('Please log in to continue');
        window.location.href = '/auth';
        return;
    }
    
    // Load user profile
    loadUserProfile();
});

// Load User Profile
async function loadUserProfile() {
    try {
        const token = localStorage.getItem('access_token');
        console.log('Fetching user profile...');
        
        const response = await fetch(`${API_BASE_URL}/users/me`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        console.log('User profile response status:', response.status);
        
        if (response.ok) {
            const userData = await response.json();
            console.log('User data:', userData);
            userNameElement.textContent = `Welcome, ${userData.name}`;
        } else {
            console.error('Failed to load user profile');
            if (response.status === 401) {
                // Token expired or invalid
                localStorage.removeItem('access_token');
                alert('Your session has expired. Please log in again.');
                window.location.href = '/auth';
            }
        }
    } catch (error) {
        console.error('Error loading user profile:', error);
    }
}

// Logout
logoutBtn.addEventListener('click', () => {
    console.log('Logging out...');
    localStorage.removeItem('access_token');
    window.location.href = '/auth';
});
