#!/usr/bin/env python3

"""
Test trade execution for Austin Glusac
"""

import requests
import json
import sys
from datetime import datetime
from sqlalchemy import text
from app.database import SessionLocal

BASE_URL = "http://localhost:8000"
AUSTIN_EMAIL = "<EMAIL>"
AUSTIN_ACCOUNT_ID = "25aa2245-9d53-42f9-b3b3-43cb899c1798"

def get_austin_user_info():
    """Get Austin's user information from database"""
    print("🔍 Looking up Austin Glusac in database...")
    
    db = SessionLocal()
    try:
        # Find Austin's user record
        result = db.execute(text("""
            SELECT id, name, email, hashed_password
            FROM users
            WHERE email = :email
        """), {"email": AUSTIN_EMAIL})
        
        user_row = result.fetchone()
        if not user_row:
            print(f"   ❌ User not found: {AUSTIN_EMAIL}")
            return None
        
        print(f"   ✅ Found user: {user_row[1]} ({user_row[2]})")
        print(f"   User ID: {user_row[0]}")
        
        # Find Austin's broker account
        result = db.execute(text("""
            SELECT id, external_account_id, status, trading_enabled
            FROM broker_accounts 
            WHERE user_id = :user_id
        """), {"user_id": user_row[0]})
        
        broker_row = result.fetchone()
        if not broker_row:
            print(f"   ❌ No broker account found for user")
            return None
        
        print(f"   ✅ Broker account: {broker_row[1]}")
        print(f"   Status: {broker_row[2]} | Trading: {broker_row[3]}")
        
        # Find Austin's children
        result = db.execute(text("""
            SELECT id, name, handle
            FROM child_profiles 
            WHERE parent_id = :user_id
        """), {"user_id": user_row[0]})
        
        children = result.fetchall()
        print(f"   ✅ Found {len(children)} children:")
        for child in children:
            print(f"      - {child[1]} (@{child[2]}) - ID: {child[0]}")
        
        return {
            "user_id": str(user_row[0]),
            "name": user_row[1],
            "email": user_row[2],
            "broker_account_id": broker_row[1],
            "children": [{"id": str(child[0]), "name": child[1], "handle": child[2]} for child in children]
        }
        
    except Exception as e:
        print(f"   ❌ Database error: {str(e)}")
        return None
    finally:
        db.close()

def create_test_gift_for_child(child_id: str, amount: float = 100.0):
    """Create a test gift for the child to have balance"""
    print(f"\n💰 Creating test gift for child: {child_id}")
    
    db = SessionLocal()
    try:
        # Create a test gift
        result = db.execute(text("""
            INSERT INTO gifts (id, child_profile_id, amount_usd, message, from_name, from_email, payment_status, created_at)
            VALUES (gen_random_uuid(), :child_id, :amount, 'Test gift for trading', 'Test Giver', '<EMAIL>', 'completed', NOW())
            RETURNING id, amount_usd
        """), {"child_id": child_id, "amount": amount})
        
        gift_row = result.fetchone()
        db.commit()
        
        print(f"   ✅ Created test gift: ${gift_row[1]} (ID: {gift_row[0]})")
        return gift_row[0]
        
    except Exception as e:
        print(f"   ❌ Error creating test gift: {str(e)}")
        db.rollback()
        return None
    finally:
        db.close()

def test_backend_connection():
    """Test if backend is running"""
    print("\n🔗 Testing backend connection...")
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print("   ✅ Backend server is running")
            return True
        else:
            print(f"   ❌ Backend responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Backend connection failed: {str(e)}")
        print("   💡 Start backend: uvicorn app.main:app --reload")
        return False

def test_manual_investment_api(child_id: str, stock_symbol: str = "SPY", amount: float = 25.0):
    """Test the manual investment API endpoint"""
    print(f"\n📈 Testing manual investment API...")
    print(f"   Child ID: {child_id}")
    print(f"   Stock: {stock_symbol}")
    print(f"   Amount: ${amount}")
    
    # Investment request payload
    investment_request = {
        "child_profile_id": child_id,
        "stock_symbol": stock_symbol,
        "amount_usd": amount
    }
    
    print(f"   Request: {json.dumps(investment_request, indent=2)}")
    
    try:
        # Make the API call (this will fail without auth, but we can see the response)
        response = requests.post(
            f"{BASE_URL}/api/invest/", 
            json=investment_request,
            timeout=30
        )
        
        print(f"   Response Status: {response.status_code}")
        
        if response.status_code == 401:
            print("   ✅ API endpoint exists (requires authentication)")
            print("   💡 This is expected - we need JWT token for real testing")
            return True
        elif response.status_code == 422:
            print("   ✅ API endpoint exists (validation error)")
            try:
                error_data = response.json()
                print(f"   Validation errors: {json.dumps(error_data, indent=2)}")
            except:
                pass
            return True
        else:
            print(f"   ⚠️ Unexpected response: {response.status_code}")
            try:
                response_data = response.json()
                print(f"   Response: {json.dumps(response_data, indent=2)}")
            except:
                print(f"   Response text: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ API call failed: {str(e)}")
        return False

def test_alpaca_market_data():
    """Test Alpaca market data retrieval"""
    print(f"\n📊 Testing Alpaca market data...")
    
    try:
        from app.alpaca_service import get_alpaca_market_data
        
        # Test SPY
        print("   Testing SPY market data...")
        market_data, error = get_alpaca_market_data("SPY")
        if error:
            print(f"   ⚠️ SPY error: {error}")
        else:
            print(f"   ✅ SPY price: ${market_data.get('last_price', 'N/A')}")
        
        # Test TSLA
        print("   Testing TSLA market data...")
        market_data, error = get_alpaca_market_data("TSLA")
        if error:
            print(f"   ⚠️ TSLA error: {error}")
        else:
            print(f"   ✅ TSLA price: ${market_data.get('last_price', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Market data test failed: {str(e)}")
        return False

def test_direct_alpaca_order():
    """Test direct Alpaca order placement for Austin's account"""
    print(f"\n🚀 Testing direct Alpaca order for Austin's account...")
    print(f"   Account ID: {AUSTIN_ACCOUNT_ID}")
    
    try:
        from app.alpaca_service import place_alpaca_order
        
        # Test small order
        test_amount = 10.0
        test_symbol = "SPY"
        
        print(f"   Placing test order: ${test_amount} {test_symbol}")
        
        order_result, order_error = place_alpaca_order(
            account_id=AUSTIN_ACCOUNT_ID,
            symbol=test_symbol,
            amount_usd=test_amount
        )
        
        if order_error:
            print(f"   ❌ Order failed: {order_error}")
            return False
        else:
            print(f"   ✅ Order successful!")
            print(f"   Order ID: {order_result.get('order_id')}")
            print(f"   Symbol: {order_result.get('symbol')}")
            print(f"   Quantity: {order_result.get('qty')}")
            print(f"   Status: {order_result.get('status')}")
            print(f"   Price: ${order_result.get('estimated_price', 'N/A')}")
            return True
        
    except Exception as e:
        print(f"   ❌ Direct order test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_test_summary(austin_info):
    """Show test summary and next steps"""
    print(f"\n" + "=" * 60)
    print(f"📋 AUSTIN GLUSAC TRADE EXECUTION TEST SUMMARY")
    print(f"=" * 60)
    
    if austin_info:
        print(f"\n✅ AUSTIN'S ACCOUNT READY:")
        print(f"   Name: {austin_info['name']}")
        print(f"   Email: {austin_info['email']}")
        print(f"   Broker Account: {austin_info['broker_account_id']}")
        print(f"   Children: {len(austin_info['children'])}")
        
        for child in austin_info['children']:
            print(f"      - {child['name']} (@{child['handle']})")
    
    print(f"\n🧪 MANUAL TESTING STEPS:")
    print(f"1. 🚀 Start backend: uvicorn app.main:app --reload")
    print(f"2. 📱 Start frontend: cd kidvest-app-new && npm start")
    print(f"3. 🔐 Login as Austin: {AUSTIN_EMAIL}")
    print(f"4. 📈 Navigate to manual investment screen")
    print(f"5. 👶 Select one of Austin's children")
    print(f"6. 📊 Select stock: TSLA or SPY")
    print(f"7. 💰 Enter amount: $10-50")
    print(f"8. 🚀 Click 'Invest' button")
    
    print(f"\n🔍 EXPECTED BACKEND LOGS:")
    print(f"✅ Child profile validated: [Child Name]")
    print(f"✅ Sufficient balance: $100.00")
    print(f"✅ Broker account found: {AUSTIN_ACCOUNT_ID}")
    print(f"🔐 Using master API credentials (fallback)")
    print(f"📡 Placing order: $25 SPY")
    print(f"✅ Order successful: order_123")
    print(f"✅ Investment created: investment_456")

def main():
    """Main test function"""
    print("🧪 Austin Glusac Trade Execution Test")
    print("=" * 50)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Get Austin's info
    austin_info = get_austin_user_info()
    if not austin_info:
        print("\n❌ Cannot proceed without Austin's user info")
        return False
    
    # Step 2: Test backend connection
    if not test_backend_connection():
        print("\n❌ Backend not available")
        return False
    
    # Step 3: Create test gift if Austin has children
    if austin_info['children']:
        first_child = austin_info['children'][0]
        gift_id = create_test_gift_for_child(first_child['id'], 100.0)
        if gift_id:
            print(f"   ✅ Test gift created for {first_child['name']}")
    
    # Step 4: Test API endpoint
    if austin_info['children']:
        test_manual_investment_api(austin_info['children'][0]['id'])
    
    # Step 5: Test market data
    test_alpaca_market_data()
    
    # Step 6: Test direct Alpaca order (REAL TRADE!)
    print(f"\n⚠️ WARNING: The next test will place a REAL trade!")
    print(f"This will execute a real $10 SPY order in Austin's account.")
    
    response = input("Proceed with real trade test? (y/N): ").strip().lower()
    if response == 'y':
        test_direct_alpaca_order()
    else:
        print("   ⏭️ Skipping real trade test")
    
    # Step 7: Show summary
    show_test_summary(austin_info)
    
    print(f"\n🎉 Austin's account is ready for trade execution!")
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
