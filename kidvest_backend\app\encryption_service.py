"""
Encryption service for securing sensitive data in the database
"""

import os
import base64
from typing import Optional

try:
    from cryptography.fernet import <PERSON><PERSON><PERSON>
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False
    print("⚠️ cryptography library not installed. Using base64 encoding (NOT SECURE)")


class EncryptionService:
    """Service for encrypting and decrypting sensitive data"""
    
    def __init__(self):
        self.encryption_key = self._get_or_create_encryption_key()
        
        if CRYPTOGRAPHY_AVAILABLE and self.encryption_key:
            try:
                self.fernet = Fernet(self.encryption_key)
                self.encryption_enabled = True
                print("✅ Encryption service initialized with <PERSON><PERSON><PERSON>")
            except Exception as e:
                print(f"⚠️ Failed to initialize Fernet encryption: {e}")
                self.encryption_enabled = False
        else:
            self.encryption_enabled = False
            print("⚠️ Encryption disabled - using base64 encoding")
    
    def _get_or_create_encryption_key(self) -> Optional[bytes]:
        """Get encryption key from environment or create one"""
        
        # Try to get key from environment
        env_key = os.getenv("KIDVEST_ENCRYPTION_KEY")
        if env_key:
            try:
                return base64.urlsafe_b64decode(env_key.encode())
            except Exception as e:
                print(f"⚠️ Invalid encryption key in environment: {e}")
        
        # Generate a new key if none exists
        if CRYPTOGRAPHY_AVAILABLE:
            key = Fernet.generate_key()
            key_str = base64.urlsafe_b64encode(key).decode()
            
            print("🔑 Generated new encryption key!")
            print("⚠️ IMPORTANT: Add this to your .env file:")
            print(f"KIDVEST_ENCRYPTION_KEY={key_str}")
            print("⚠️ Without this key, encrypted data cannot be decrypted!")
            
            return key
        
        return None
    
    def encrypt_secret(self, secret: str) -> str:
        """Encrypt a secret string"""
        if not secret:
            return ""
        
        if self.encryption_enabled:
            try:
                encrypted_bytes = self.fernet.encrypt(secret.encode())
                return base64.urlsafe_b64encode(encrypted_bytes).decode()
            except Exception as e:
                print(f"❌ Encryption failed: {e}")
                # Fallback to base64
                return base64.b64encode(secret.encode()).decode()
        else:
            # Fallback to base64 encoding (NOT SECURE)
            return base64.b64encode(secret.encode()).decode()
    
    def decrypt_secret(self, encrypted_secret: str) -> str:
        """Decrypt a secret string"""
        if not encrypted_secret:
            return ""
        
        if self.encryption_enabled:
            try:
                encrypted_bytes = base64.urlsafe_b64decode(encrypted_secret.encode())
                decrypted_bytes = self.fernet.decrypt(encrypted_bytes)
                return decrypted_bytes.decode()
            except Exception as e:
                print(f"❌ Decryption failed: {e}")
                # Try base64 fallback
                try:
                    return base64.b64decode(encrypted_secret.encode()).decode()
                except:
                    return encrypted_secret  # Return as-is if all fails
        else:
            # Fallback to base64 decoding
            try:
                return base64.b64decode(encrypted_secret.encode()).decode()
            except:
                return encrypted_secret  # Return as-is if decoding fails
    
    def is_encryption_enabled(self) -> bool:
        """Check if proper encryption is enabled"""
        return self.encryption_enabled


# Global encryption service instance
encryption_service = EncryptionService()


def encrypt_secret(secret: str) -> str:
    """Encrypt sensitive data before storing in database"""
    return encryption_service.encrypt_secret(secret)


def decrypt_secret(encrypted_secret: str) -> str:
    """Decrypt sensitive data from database"""
    return encryption_service.decrypt_secret(encrypted_secret)


def is_encryption_enabled() -> bool:
    """Check if proper encryption is enabled"""
    return encryption_service.is_encryption_enabled()


def setup_encryption_key():
    """Setup encryption key in environment"""
    if CRYPTOGRAPHY_AVAILABLE:
        key = Fernet.generate_key()
        key_str = base64.urlsafe_b64encode(key).decode()
        
        print("🔑 Generated encryption key for KidVest")
        print("=" * 50)
        print(f"KIDVEST_ENCRYPTION_KEY={key_str}")
        print("=" * 50)
        print("⚠️ IMPORTANT:")
        print("1. Add this line to your .env file")
        print("2. Keep this key secure and backed up")
        print("3. Without this key, encrypted data cannot be decrypted")
        print("4. Restart your backend after adding to .env")
        
        return key_str
    else:
        print("❌ cryptography library not available")
        print("💡 Install with: pip install cryptography==41.0.7")
        return None


if __name__ == "__main__":
    # Test encryption service
    print("🧪 Testing Encryption Service")
    print("=" * 40)
    
    # Test encryption/decryption
    test_secret = "test_api_secret_12345"
    print(f"Original: {test_secret}")
    
    encrypted = encrypt_secret(test_secret)
    print(f"Encrypted: {encrypted}")
    
    decrypted = decrypt_secret(encrypted)
    print(f"Decrypted: {decrypted}")
    
    print(f"Encryption enabled: {is_encryption_enabled()}")
    print(f"Match: {test_secret == decrypted}")
    
    if not is_encryption_enabled():
        print("\n💡 To enable proper encryption:")
        print("1. pip install cryptography==41.0.7")
        print("2. python app/encryption_service.py")
        print("3. Add generated key to .env file")
