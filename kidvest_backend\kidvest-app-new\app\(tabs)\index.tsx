import React, { useState, useEffect } from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

import { Text, View } from '@/components/Themed';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';
import { useAuth } from '../../context/AuthContext';

export default function DashboardScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { user, needsOnboarding } = useAuth();

  const [isLoading, setIsLoading] = useState(true);
  const [accountBalance, setAccountBalance] = useState(0);
  const [recentGifts, setRecentGifts] = useState([]);
  const [showKycPrompt, setShowKycPrompt] = useState(true);
  const [kycCompleted, setKycCompleted] = useState(!needsOnboarding);

  // Log the onboarding status for debugging
  console.log('Dashboard: needsOnboarding state:', needsOnboarding);
  console.log('Dashboard: kycCompleted state:', kycCompleted);

  // Update KYC status when needsOnboarding changes
  useEffect(() => {
    console.log('Dashboard: needsOnboarding changed to:', needsOnboarding);
    setKycCompleted(!needsOnboarding);
  }, [needsOnboarding]);

  // Simulate loading data
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
      // Mock data
      setAccountBalance(1250.75);
      setRecentGifts([
        { id: '1', sender: 'Grandma', amount: 50, date: '2023-05-01', message: 'Happy birthday!' },
        { id: '2', sender: 'Uncle Bob', amount: 100, date: '2023-04-15', message: 'For your college fund' },
        { id: '3', sender: 'Aunt Sarah', amount: 25, date: '2023-03-22', message: 'Just because' },
      ]);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors[colorScheme].primary} />
        <Text style={styles.loadingText}>Loading your dashboard...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Welcome Section */}
      <View style={styles.welcomeSection}>
        <View style={styles.welcomeContent}>
          <Text style={styles.welcomeTitle}>Welcome, {user?.name || 'User'}</Text>
          <Text style={styles.welcomeSubtitle}>Your child's investment journey starts here</Text>
        </View>
        <TouchableOpacity
          style={styles.notificationButton}
          onPress={() => {}}
        >
          <FontAwesome5 name="bell" size={20} color={Colors[colorScheme].text} />
        </TouchableOpacity>
      </View>

      {/* Account Balance */}
      <View style={styles.balanceCard}>
        <Text style={styles.balanceLabel}>Total Account Balance</Text>
        <Text style={styles.balanceAmount}>${accountBalance.toFixed(2)}</Text>
        <View style={styles.balanceActions}>
          <TouchableOpacity style={styles.balanceActionButton}>
            <FontAwesome5 name="plus-circle" size={16} color={Colors[colorScheme].primary} />
            <Text style={styles.balanceActionText}>Add Funds</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.balanceActionButton}>
            <FontAwesome5 name="exchange-alt" size={16} color={Colors[colorScheme].primary} />
            <Text style={styles.balanceActionText}>Transfer</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* KYC Prompt */}
      {showKycPrompt && !kycCompleted && (
        <View style={styles.kycPromptContainer}>
          <View style={styles.kycPromptContent}>
            <View style={styles.kycPromptIconContainer}>
              <FontAwesome5 name="exclamation-circle" size={24} color="#FF9800" />
            </View>
            <View style={styles.kycPromptTextContainer}>
              <Text style={styles.kycPromptTitle}>Complete Your KYC Verification</Text>
              <Text style={styles.kycPromptDescription}>
                You need to complete KYC to create a brokerage account and start investing the gifts.
              </Text>
            </View>
          </View>
          <View style={styles.kycPromptActions}>
            <TouchableOpacity
              style={styles.kycPromptSkipButton}
              onPress={() => setShowKycPrompt(false)}
            >
              <Text style={styles.kycPromptSkipText}>Skip for now</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.kycPromptActionButton, { backgroundColor: Colors[colorScheme].primary }]}
              onPress={() => router.push('/onboarding/kyc')}
            >
              <Text style={styles.kycPromptActionText}>Complete KYC</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Recent Gifts */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Gifts</Text>
          <TouchableOpacity>
            <Text style={[styles.sectionLink, { color: Colors[colorScheme].primary }]}>View All</Text>
          </TouchableOpacity>
        </View>

        {recentGifts.length > 0 ? (
          recentGifts.map((gift: any) => (
            <View key={gift.id} style={styles.giftItem}>
              <View style={styles.giftIconContainer}>
                <FontAwesome5 name="gift" size={20} color={Colors[colorScheme].primary} />
              </View>
              <View style={styles.giftDetails}>
                <Text style={styles.giftSender}>{gift.sender}</Text>
                <Text style={styles.giftMessage}>{gift.message}</Text>
                <Text style={styles.giftDate}>{new Date(gift.date).toLocaleDateString()}</Text>
              </View>
              <Text style={styles.giftAmount}>${gift.amount}</Text>
            </View>
          ))
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>No gifts received yet</Text>
          </View>
        )}
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity style={styles.quickActionButton}>
          <FontAwesome5 name="gift" size={20} color={Colors[colorScheme].primary} />
          <Text style={styles.quickActionText}>Create Gift</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={() => router.push('/onboarding/kyc')}
        >
          <FontAwesome5 name="id-card" size={20} color={kycCompleted ? '#4CAF50' : '#FF9800'} />
          <Text style={[styles.quickActionText, kycCompleted ? {color: '#4CAF50'} : {color: '#FF9800'}]}>
            {kycCompleted ? 'KYC Verified' : 'Complete KYC'}
          </Text>
          {!kycCompleted && (
            <View style={styles.kycBadge}>
              <Text style={styles.kycBadgeText}>!</Text>
            </View>
          )}
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={() => router.push('/onboarding/child-profile')}
        >
          <FontAwesome5 name="child" size={20} color={Colors[colorScheme].primary} />
          <Text style={styles.quickActionText}>Add Child</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.quickActionButton}>
          <FontAwesome5 name="share-alt" size={20} color={Colors[colorScheme].primary} />
          <Text style={styles.quickActionText}>Share</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  welcomeSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 24,
  },
  welcomeContent: {
    flex: 1,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  welcomeSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  balanceCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#F5F5F5',
  },
  balanceLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  balanceAmount: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  balanceActions: {
    flexDirection: 'row',
  },
  balanceActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  balanceActionText: {
    marginLeft: 8,
    color: '#666',
  },
  kycPromptContainer: {
    margin: 16,
    borderRadius: 12,
    backgroundColor: '#FFF8E1',
    overflow: 'hidden',
  },
  kycPromptContent: {
    flexDirection: 'row',
    padding: 16,
  },
  kycPromptIconContainer: {
    marginRight: 16,
  },
  kycPromptTextContainer: {
    flex: 1,
  },
  kycPromptTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  kycPromptDescription: {
    fontSize: 14,
    color: '#666',
  },
  kycPromptActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
  },
  kycPromptSkipButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  kycPromptSkipText: {
    color: '#666',
  },
  kycPromptActionButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  kycPromptActionText: {
    color: 'white',
    fontWeight: 'bold',
  },
  sectionContainer: {
    margin: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  sectionLink: {
    fontSize: 14,
  },
  giftItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  giftIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  giftDetails: {
    flex: 1,
  },
  giftSender: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  giftMessage: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  giftDate: {
    fontSize: 12,
    color: '#999',
  },
  giftAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyState: {
    padding: 24,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
    justifyContent: 'space-between',
  },
  quickActionButton: {
    width: '48%',
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
  },
  quickActionText: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  kycBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#FF9800',
    justifyContent: 'center',
    alignItems: 'center',
  },
  kycBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
});
