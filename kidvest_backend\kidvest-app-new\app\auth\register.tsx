import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, TextInput, ScrollView, KeyboardAvoidingView, Platform, ActivityIndicator, View, Text, Alert } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';
import { useAuth } from '../../context/AuthContext';

export default function RegisterScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { signUp, isLoading, error } = useAuth();

  const [step, setStep] = useState(1);

  // Step 1 fields
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  // Step 2 fields
  const [phone, setPhone] = useState('');
  const [dob, setDob] = useState('');
  const [ssn, setSsn] = useState('');
  const [address, setAddress] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [zipCode, setZipCode] = useState('');
  const [agreeTerms, setAgreeTerms] = useState(false);

  const [localError, setLocalError] = useState<string | null>(null);

  const validateStep1 = () => {
    if (!firstName.trim()) {
      setLocalError('First name is required');
      return false;
    }

    if (!lastName.trim()) {
      setLocalError('Last name is required');
      return false;
    }

    if (!email.trim()) {
      setLocalError('Email is required');
      return false;
    }

    if (!password) {
      setLocalError('Password is required');
      return false;
    }

    if (password !== confirmPassword) {
      setLocalError('Passwords do not match');
      return false;
    }

    setLocalError(null);
    return true;
  };

  const validateStep2 = () => {
    if (!phone.trim()) {
      setLocalError('Phone number is required');
      return false;
    }

    if (!dob.trim()) {
      setLocalError('Date of birth is required');
      return false;
    }

    if (!ssn.trim()) {
      setLocalError('SSN is required');
      return false;
    }

    if (!address.trim()) {
      setLocalError('Address is required');
      return false;
    }

    if (!city.trim()) {
      setLocalError('City is required');
      return false;
    }

    if (!state.trim()) {
      setLocalError('State is required');
      return false;
    }

    if (!zipCode.trim()) {
      setLocalError('ZIP code is required');
      return false;
    }

    if (!agreeTerms) {
      setLocalError('You must agree to the terms and conditions');
      return false;
    }

    setLocalError(null);
    return true;
  };

  const handleNextStep = () => {
    if (validateStep1()) {
      setStep(2);
    }
  };

  const handlePreviousStep = () => {
    setStep(1);
  };

  const handleRegister = async () => {
    if (!validateStep2()) {
      return;
    }

    try {
      console.log('Register screen: Attempting to sign up with email:', email);

      // Format date of birth if needed (MM/DD/YYYY to YYYY-MM-DD)
      let formattedDob = dob;
      if (dob.includes('/')) {
        const parts = dob.split('/');
        if (parts.length === 3) {
          // Make sure we have month, day, year
          const month = parts[0].padStart(2, '0');
          const day = parts[1].padStart(2, '0');
          const year = parts[2].length === 2 ? `20${parts[2]}` : parts[2];
          formattedDob = `${year}-${month}-${day}`;
        }
      }

      console.log('Register screen: Formatted DOB:', formattedDob);

      // Format SSN if needed (remove dashes)
      const formattedSsn = ssn.replace(/-/g, '');

      // The signUp function will handle the loading state internally
      await signUp({
        firstName,
        lastName,
        email,
        password,
        ssn: formattedSsn,
        dob: formattedDob,
        phone,
        address,
        city,
        state,
        zipCode,
      });

      console.log('Register screen: Sign up successful');

      // Navigate to the main app after successful registration
      router.replace('/(tabs)');
    } catch (e: any) {
      // Error is already handled in the auth context
      console.error('Register screen: Registration failed:', e);

      // Show a more user-friendly error message
      let errorMessage = 'Registration failed. Please try again.';

      if (e.response?.data?.detail) {
        errorMessage = e.response.data.detail;
      } else if (e.response?.data?.message) {
        errorMessage = e.response.data.message;
      } else if (e.message) {
        errorMessage = e.message;
      }

      console.error('Register screen: Setting error message:', errorMessage);
      setLocalError(errorMessage);

      // Display alert for debugging purposes
      Alert.alert(
        'Registration Error',
        `Error: ${errorMessage}\n\nPlease check the console for more details.`,
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => step === 1 ? router.back() : handlePreviousStep()}
          >
            <FontAwesome name="arrow-left" size={16} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Create Account</Text>
          <View style={styles.stepIndicator}>
            <View style={[styles.stepDot, step >= 1 && styles.activeStepDot]} />
            <View style={[styles.stepDot, step >= 2 && styles.activeStepDot]} />
          </View>
        </View>

        {(error || localError) && (
          <View style={styles.errorContainer}>
            <FontAwesome name="exclamation-circle" size={16} color="#F44336" />
            <Text style={styles.errorText}>{localError || error}</Text>
          </View>
        )}

        {step === 1 && (
          <View style={styles.formContainer}>
            <Text style={styles.stepTitle}>Personal Information</Text>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>First Name</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your first name"
                value={firstName}
                onChangeText={setFirstName}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Last Name</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your last name"
                value={lastName}
                onChangeText={setLastName}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Email</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Password</Text>
              <View style={styles.passwordInputContainer}>
                <TextInput
                  style={styles.passwordInput}
                  placeholder="Create a password"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <FontAwesome
                    name={showPassword ? "eye-slash" : "eye"}
                    size={16}
                    color="#AAAAAA"
                  />
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Confirm Password</Text>
              <TextInput
                style={styles.input}
                placeholder="Confirm your password"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry={!showPassword}
              />
            </View>

            <TouchableOpacity
              style={[styles.nextButton, { backgroundColor: Colors[colorScheme].primary }]}
              onPress={handleNextStep}
            >
              <Text style={styles.nextButtonText}>Next</Text>
              <FontAwesome name="arrow-right" size={16} color="white" />
            </TouchableOpacity>
          </View>
        )}

        {step === 2 && (
          <View style={styles.formContainer}>
            <Text style={styles.stepTitle}>Additional Information</Text>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Phone Number</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your phone number"
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Date of Birth</Text>
              <TextInput
                style={styles.input}
                placeholder="MM/DD/YYYY"
                value={dob}
                onChangeText={setDob}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Social Security Number</Text>
              <TextInput
                style={styles.input}
                placeholder="XXX-XX-XXXX"
                value={ssn}
                onChangeText={setSsn}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Address</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your street address"
                value={address}
                onChangeText={setAddress}
              />
            </View>

            <View style={styles.rowContainer}>
              <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
                <Text style={styles.inputLabel}>City</Text>
                <TextInput
                  style={styles.input}
                  placeholder="City"
                  value={city}
                  onChangeText={setCity}
                />
              </View>

              <View style={[styles.inputContainer, { flex: 1, marginLeft: 8 }]}>
                <Text style={styles.inputLabel}>State</Text>
                <TextInput
                  style={styles.input}
                  placeholder="State"
                  value={state}
                  onChangeText={setState}
                />
              </View>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>ZIP Code</Text>
              <TextInput
                style={styles.input}
                placeholder="ZIP Code"
                value={zipCode}
                onChangeText={setZipCode}
                keyboardType="numeric"
              />
            </View>

            <TouchableOpacity
              style={styles.termsContainer}
              onPress={() => setAgreeTerms(!agreeTerms)}
            >
              <View style={[styles.checkbox, agreeTerms && { borderColor: Colors[colorScheme].primary }]}>
                {agreeTerms && (
                  <FontAwesome name="check" size={12} color={Colors[colorScheme].primary} />
                )}
              </View>
              <Text style={styles.termsText}>
                I agree to the <Text style={[styles.termsLink, { color: Colors[colorScheme].primary }]}>Terms of Service</Text> and <Text style={[styles.termsLink, { color: Colors[colorScheme].primary }]}>Privacy Policy</Text>
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.registerButton, { backgroundColor: Colors[colorScheme].primary }]}
              onPress={handleRegister}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text style={styles.registerButtonText}>Create Account</Text>
              )}
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  stepIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#E0E0E0',
    marginHorizontal: 4,
  },
  activeStepDot: {
    backgroundColor: '#4CAF50',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFEBEE',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: '#F44336',
    marginLeft: 8,
    flex: 1,
  },
  formContainer: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: '#666666',
  },
  input: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 48,
    fontSize: 16,
  },
  passwordInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    height: 48,
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  passwordToggle: {
    padding: 12,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 16,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 4,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  termsText: {
    flex: 1,
    fontSize: 14,
    color: '#666666',
  },
  termsLink: {
    fontWeight: 'bold',
  },
  nextButton: {
    height: 48,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
    marginRight: 8,
  },
  registerButton: {
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  registerButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
});
