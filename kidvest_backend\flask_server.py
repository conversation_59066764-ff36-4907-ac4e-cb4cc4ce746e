from flask import Flask, send_from_directory, redirect, url_for
import os

app = Flask(__name__)

# Define the base directory
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# Define the UI directories
UI_DIRS = {
    "auth": os.path.join(BASE_DIR, "auth-ui"),
    "onboarding": os.path.join(BASE_DIR, "onboarding-ui"),
    "child-profile": os.path.join(BASE_DIR, "child-profile-ui"),
    "dashboard": os.path.join(BASE_DIR, "dashboard-ui"),
    "gift-wall": os.path.join(BASE_DIR, "gift-wall-ui")
}

# Root route
@app.route('/')
def index():
    return send_from_directory(UI_DIRS["dashboard"], "index.html")

# Auth routes
@app.route('/auth')
@app.route('/auth/')
def auth():
    return send_from_directory(UI_DIRS["auth"], "index.html")

@app.route('/auth/<path:path>')
def auth_files(path):
    return send_from_directory(UI_DIRS["auth"], path)

# Dashboard routes
@app.route('/dashboard')
@app.route('/dashboard/')
def dashboard():
    return send_from_directory(UI_DIRS["dashboard"], "index.html")

@app.route('/dashboard/<path:path>')
def dashboard_files(path):
    return send_from_directory(UI_DIRS["dashboard"], path)

# Onboarding routes
@app.route('/onboarding')
@app.route('/onboarding/')
def onboarding():
    return send_from_directory(UI_DIRS["onboarding"], "index.html")

@app.route('/onboarding/<path:path>')
def onboarding_files(path):
    return send_from_directory(UI_DIRS["onboarding"], path)

# Child profile routes
@app.route('/child-profile')
@app.route('/child-profile/')
def child_profile():
    return send_from_directory(UI_DIRS["child-profile"], "index.html")

@app.route('/child-profile/<path:path>')
def child_profile_files(path):
    return send_from_directory(UI_DIRS["child-profile"], path)

# Gift wall routes
@app.route('/gift-wall')
@app.route('/gift-wall/')
def gift_wall():
    return send_from_directory(UI_DIRS["gift-wall"], "index.html")

@app.route('/gift-wall/<path:path>')
def gift_wall_files(path):
    return send_from_directory(UI_DIRS["gift-wall"], path)

# Wall routes
@app.route('/wall/<path:path>')
def wall(path):
    return send_from_directory(UI_DIRS["gift-wall"], f"wall/{path}")

if __name__ == '__main__':
    print("Starting Flask server...")
    print("Available UIs:")
    print(f"  - Main: http://localhost:8080/")
    print(f"  - Auth: http://localhost:8080/auth/")
    print(f"  - Dashboard: http://localhost:8080/dashboard/")
    print(f"  - Onboarding: http://localhost:8080/onboarding/")
    print(f"  - Child Profiles: http://localhost:8080/child-profile/")
    print(f"  - Gift Walls: http://localhost:8080/gift-wall/")
    app.run(host='0.0.0.0', port=8080, debug=True)
