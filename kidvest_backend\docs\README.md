# KidVest Documentation

## Overview

This directory contains comprehensive documentation for the KidVest platform, a system that allows parents to create investment accounts for children, receive monetary gifts through a social wall, and invest using Alpaca's trading API.

## Documentation Files

### [API Documentation](api_documentation.md)

Detailed information about the KidVest API endpoints, request/response formats, and authentication mechanisms. This document is intended for developers who need to interact with the KidVest API.

**Contents:**
- Base URL information
- Authentication endpoints
- User management endpoints
- Child profile endpoints
- Gift wall endpoints
- Onboarding endpoints
- Stripe webhook handling

### [Data Models](data_models.md)

Complete and detailed accounting of all data models used in the KidVest application, including database models and Pydantic schemas.

**Contents:**
- Database models with field descriptions
- Relationships between models
- Pydantic schemas for request/response validation
- Data validation rules

### [Architecture](architecture.md)

Comprehensive architecture documentation with diagrams explaining the system design, components, and data flow.

**Contents:**
- System architecture overview
- Backend architecture
- Frontend architecture
- Authentication flow
- Onboarding flow
- Gift flow
- Database schema
- Integration points
- Deployment architecture
- Security considerations
- Performance considerations

## Using This Documentation

- **Developers**: Start with the API documentation to understand available endpoints and data formats.
- **Database Administrators**: Refer to the data models documentation for database schema details.
- **System Architects**: Use the architecture documentation to understand the overall system design.
- **New Team Members**: Read all documents to get a comprehensive understanding of the KidVest platform.

## Updating Documentation

When making changes to the codebase, please ensure that the corresponding documentation is updated to reflect those changes. This helps maintain the accuracy and usefulness of the documentation.

## Contact

For questions or clarifications about this documentation, please contact the KidVest development team.
